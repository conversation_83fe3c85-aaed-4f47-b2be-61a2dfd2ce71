#!/usr/bin/env python3
"""
增强版Tushare数据同步命令行工具
优化用户体验和错误处理
"""

import click
import sys
import os
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from sync.enhanced_data_sync import (
    EnhancedTushareDataSync, 
    sync_table_enhanced, 
    sync_daily_tables_enhanced,
    sync_all_tables_enhanced
)
from database.init_db import init_database, check_database
from config import get_config, TUSHARE_TABLES

# 全局变量
current_syncer = None
progress_data = {}

def progress_callback(data: Dict[str, Any]):
    """进度回调函数"""
    global progress_data
    progress_data = data
    
    # 显示进度条
    progress = data.get('progress', 0)
    message = data.get('message', '')
    table_name = data.get('table_name', '')
    
    # 创建进度条
    bar_length = 40
    filled_length = int(bar_length * progress // 100)
    bar = '█' * filled_length + '░' * (bar_length - filled_length)
    
    # 清除当前行并显示进度
    print(f'\r[{bar}] {progress:3d}% | {table_name} | {message}', end='', flush=True)
    
    if progress >= 100 or data.get('is_cancelled'):
        print()  # 换行

@click.group()
@click.version_option(version='1.1.0', prog_name='Tushare数据镜像系统')
def cli():
    """🚀 Tushare数据镜像系统 - 增强版命令行工具"""
    pass

@cli.command()
@click.option('--force', is_flag=True, help='强制重新初始化数据库')
@click.option('--quiet', is_flag=True, help='静默模式')
def init(force, quiet):
    """📊 初始化数据库"""
    if not quiet:
        click.echo("🚀 初始化Tushare数据镜像系统...")
    
    try:
        result = init_database(force_recreate=force)
        
        if not quiet:
            if result:
                click.echo("✅ 数据库初始化成功！")
                click.echo(f"📍 数据库位置: {get_config().DATABASE_URL}")
                click.echo("💡 使用 'python sync_cli_enhanced.py status' 查看系统状态")
            else:
                click.echo("ℹ️  数据库已存在，跳过初始化")
                
    except Exception as e:
        click.echo(f"❌ 数据库初始化失败: {e}", err=True)
        sys.exit(1)

@cli.command()
@click.option('--format', type=click.Choice(['table', 'json']), default='table', help='输出格式')
def status(format):
    """📋 查看系统状态"""
    try:
        click.echo("🔍 检查系统状态...")
        
        # 检查数据库
        tables_info = check_database()
        
        if not tables_info:
            click.echo("❌ 数据库未初始化，请先运行 'init' 命令")
            sys.exit(1)
        
        if format == 'json':
            import json
            status_data = {
                'timestamp': datetime.now().isoformat(),
                'tables': dict(tables_info),
                'total_records': sum(count for _, count in tables_info if isinstance(count, int))
            }
            click.echo(json.dumps(status_data, indent=2, ensure_ascii=False))
        else:
            # 表格格式
            click.echo("\n📊 数据库状态:")
            click.echo("=" * 80)
            click.echo(f"{'表名':<20} {'中文名':<20} {'记录数':<15} {'状态':<10}")
            click.echo("-" * 80)
            
            total_records = 0
            for table_name, count in tables_info:
                chinese_name = TUSHARE_TABLES.get(table_name, {}).get('name', '未知')
                
                if isinstance(count, int):
                    total_records += count
                    status = "✅ 有数据" if count > 0 else "⚪ 空表"
                    count_str = f"{count:,}"
                else:
                    status = "❌ 错误"
                    count_str = str(count)
                
                click.echo(f"{table_name:<20} {chinese_name:<20} {count_str:<15} {status:<10}")
            
            click.echo("-" * 80)
            click.echo(f"总记录数: {total_records:,}")
            click.echo(f"支持表数: {len(TUSHARE_TABLES)}")
            click.echo(f"已创建表: {len(tables_info)}")
            
    except Exception as e:
        click.echo(f"❌ 状态检查失败: {e}", err=True)
        sys.exit(1)

@cli.command()
@click.option('--table', help='指定要同步的表名')
@click.option('--start-date', help='开始日期 (YYYYMMDD)')
@click.option('--end-date', help='结束日期 (YYYYMMDD)')
@click.option('--force-full', is_flag=True, help='强制全量同步')
@click.option('--list-tables', is_flag=True, help='列出所有支持的表')
@click.option('--batch', is_flag=True, help='批量同步所有表')
@click.option('--daily-only', is_flag=True, help='只同步每日更新的表')
def sync(table, start_date, end_date, force_full, list_tables, batch, daily_only):
    """🔄 数据同步"""
    global current_syncer
    
    if list_tables:
        click.echo("\n📋 支持的数据表:")
        click.echo("=" * 80)
        click.echo(f"{'表名':<20} {'中文名':<25} {'同步类型':<10} {'更新频率':<10}")
        click.echo("-" * 80)
        
        for table_name, config in TUSHARE_TABLES.items():
            click.echo(f"{table_name:<20} {config['name']:<25} {config['sync_type']:<10} {config['sync_frequency']:<10}")
        
        click.echo(f"\n总计: {len(TUSHARE_TABLES)} 个数据表")
        return
    
    try:
        # 创建同步器
        current_syncer = EnhancedTushareDataSync(progress_callback=progress_callback)
        
        # 设置信号处理
        import signal
        def signal_handler(signum, frame):
            click.echo("\n\n🛑 接收到中断信号，正在取消同步...")
            if current_syncer:
                current_syncer.cancel_sync()
        
        signal.signal(signal.SIGINT, signal_handler)
        
        if batch:
            # 批量同步
            if daily_only:
                click.echo("🔄 开始批量同步每日更新表...")
                result = sync_daily_tables_enhanced(progress_callback)
            else:
                click.echo("🔄 开始批量同步所有表...")
                result = sync_all_tables_enhanced(progress_callback)
            
            # 显示结果
            click.echo(f"\n🎉 批量同步完成!")
            click.echo(f"✅ 成功: {result['success_count']}/{result['total_tables']} 个表")
            click.echo(f"📊 总记录: {result['total_records']:,} 条")
            
            # 显示详细结果
            if result['results']:
                click.echo("\n📋 详细结果:")
                for table_name, table_result in result['results'].items():
                    status_icon = "✅" if table_result['status'] == 'success' else "❌"
                    chinese_name = TUSHARE_TABLES.get(table_name, {}).get('name', table_name)
                    
                    if table_result['status'] == 'success':
                        click.echo(f"  {status_icon} {chinese_name} ({table_name}): {table_result['record_count']:,} 条记录")
                    else:
                        click.echo(f"  {status_icon} {chinese_name} ({table_name}): {table_result.get('error_message', '未知错误')}")
        
        elif table:
            # 单表同步
            if table not in TUSHARE_TABLES:
                click.echo(f"❌ 不支持的表名: {table}")
                click.echo("💡 使用 --list-tables 查看支持的表")
                sys.exit(1)
            
            chinese_name = TUSHARE_TABLES[table]['name']
            click.echo(f"🔄 开始同步表: {chinese_name} ({table})")
            
            if start_date and end_date:
                click.echo(f"📅 日期范围: {start_date} - {end_date}")
            
            if force_full:
                click.echo("🔄 执行全量同步")
            
            result = current_syncer.sync_table_enhanced(table, start_date, end_date, force_full)
            
            if result['status'] == 'success':
                click.echo(f"\n✅ 同步成功!")
                click.echo(f"📊 记录数: {result['record_count']:,}")
                click.echo(f"📅 同步日期: {result.get('sync_date', '未知')}")
                click.echo(f"🔄 同步类型: {result.get('sync_type', '未知')}")
            else:
                click.echo(f"\n❌ 同步失败: {result.get('error_message', '未知错误')}")
                sys.exit(1)
        
        else:
            click.echo("❌ 请指定要同步的表 (--table) 或使用批量同步 (--batch)")
            click.echo("💡 使用 --help 查看帮助信息")
            sys.exit(1)
            
    except KeyboardInterrupt:
        click.echo("\n🛑 同步已被用户取消")
        sys.exit(1)
    except Exception as e:
        click.echo(f"\n❌ 同步失败: {e}", err=True)
        sys.exit(1)

@cli.command()
@click.option('--days', default=7, help='显示最近N天的同步记录')
@click.option('--table', help='指定表名')
@click.option('--format', type=click.Choice(['table', 'json']), default='table', help='输出格式')
def report(days, table, format):
    """📊 生成同步报告"""
    try:
        from database.models import db_config, SyncStatus
        
        session = db_config.get_session()
        
        try:
            # 构建查询
            query = session.query(SyncStatus)
            
            if table:
                query = query.filter_by(table_name=table)
            
            # 时间过滤
            cutoff_time = datetime.now() - timedelta(days=days)
            query = query.filter(SyncStatus.last_sync_time >= cutoff_time)
            
            # 排序
            sync_records = query.order_by(SyncStatus.last_sync_time.desc()).all()
            
            if format == 'json':
                import json
                report_data = {
                    'generated_at': datetime.now().isoformat(),
                    'days': days,
                    'table_filter': table,
                    'records': []
                }
                
                for record in sync_records:
                    report_data['records'].append({
                        'table_name': record.table_name,
                        'chinese_name': TUSHARE_TABLES.get(record.table_name, {}).get('name', '未知'),
                        'status': record.sync_status,
                        'sync_date': record.last_sync_date,
                        'sync_time': record.last_sync_time.isoformat() if record.last_sync_time else None,
                        'record_count': record.record_count,
                        'error_message': record.error_message
                    })
                
                click.echo(json.dumps(report_data, indent=2, ensure_ascii=False))
            
            else:
                # 表格格式
                click.echo(f"\n📊 同步报告 (最近 {days} 天)")
                if table:
                    click.echo(f"🔍 筛选表: {table}")
                click.echo("=" * 100)
                click.echo(f"{'表名':<20} {'中文名':<20} {'状态':<8} {'同步日期':<12} {'记录数':<12} {'同步时间':<20}")
                click.echo("-" * 100)
                
                for record in sync_records:
                    chinese_name = TUSHARE_TABLES.get(record.table_name, {}).get('name', '未知')
                    status_icon = {
                        'success': '✅',
                        'error': '❌',
                        'syncing': '🔄'
                    }.get(record.sync_status, '❓')
                    
                    sync_time = record.last_sync_time.strftime('%Y-%m-%d %H:%M') if record.last_sync_time else '未知'
                    record_count = f"{record.record_count:,}" if record.record_count else '0'
                    
                    click.echo(f"{record.table_name:<20} {chinese_name:<20} {status_icon:<8} {record.last_sync_date or '未知':<12} {record_count:<12} {sync_time:<20}")
                
                if not sync_records:
                    click.echo("暂无同步记录")
                else:
                    click.echo(f"\n总计: {len(sync_records)} 条记录")
        
        finally:
            session.close()
            
    except Exception as e:
        click.echo(f"❌ 生成报告失败: {e}", err=True)
        sys.exit(1)

@cli.command()
@click.confirmation_option(prompt='确定要重置数据库吗？这将删除所有数据！')
def reset():
    """🗑️  重置数据库"""
    try:
        click.echo("🗑️  正在重置数据库...")
        
        result = init_database(force_recreate=True)
        
        if result:
            click.echo("✅ 数据库重置成功！")
        else:
            click.echo("❌ 数据库重置失败")
            sys.exit(1)
            
    except Exception as e:
        click.echo(f"❌ 重置失败: {e}", err=True)
        sys.exit(1)

if __name__ == '__main__':
    cli()
