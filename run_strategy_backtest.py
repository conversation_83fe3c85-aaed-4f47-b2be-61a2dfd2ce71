#!/usr/bin/env python3
"""
策略回测运行脚本
一键运行完整的策略回测和分析
"""

import sys
import os
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_data_availability():
    """检查数据可用性"""
    print("🔍 检查数据可用性...")
    
    try:
        from database.models import db_config
        from sqlalchemy import text
        
        session = db_config.get_session()
        
        # 检查必要的表
        required_tables = ['stock_basic', 'daily', 'daily_basic', 'trade_cal']
        table_status = {}
        
        for table in required_tables:
            try:
                result = session.execute(text(f"SELECT COUNT(*) FROM {table}"))
                count = result.scalar()
                table_status[table] = count
                
                if count > 0:
                    print(f"  ✅ {table}: {count:,} 条记录")
                else:
                    print(f"  ⚠️  {table}: 空表")
                    
            except Exception as e:
                print(f"  ❌ {table}: 表不存在或查询失败")
                table_status[table] = 0
        
        session.close()
        
        # 检查数据完整性
        min_required_records = {
            'stock_basic': 1000,    # 至少1000只股票
            'daily': 50000,         # 至少5万条日线数据
            'daily_basic': 50000,   # 至少5万条基本面数据
            'trade_cal': 1000       # 至少1000个交易日
        }
        
        data_sufficient = True
        for table, min_count in min_required_records.items():
            if table_status.get(table, 0) < min_count:
                print(f"  ⚠️  {table} 数据不足，建议至少 {min_count:,} 条记录")
                data_sufficient = False
        
        if data_sufficient:
            print("✅ 数据检查通过，可以进行回测")
            return True
        else:
            print("⚠️  数据不足，建议先同步更多数据")
            return False
            
    except Exception as e:
        print(f"❌ 数据检查失败: {e}")
        return False

def sync_required_data():
    """同步必要数据"""
    print("\n🔄 同步必要数据...")
    
    try:
        from sync.enhanced_data_sync import EnhancedTushareDataSync
        
        def progress_callback(data):
            progress = data.get('progress', 0)
            message = data.get('message', '')
            table_name = data.get('table_name', '')
            print(f"\r[{progress:3d}%] {table_name}: {message}", end='', flush=True)
            if progress >= 100:
                print()
        
        syncer = EnhancedTushareDataSync(progress_callback=progress_callback)
        
        # 同步基础数据
        basic_tables = ['stock_basic', 'trade_cal']
        for table in basic_tables:
            print(f"\n📊 同步 {table}...")
            try:
                result = syncer.sync_table_enhanced(table, force_full=True)
                if result['status'] == 'success':
                    print(f"✅ {table} 同步成功: {result['record_count']:,} 条记录")
                else:
                    print(f"❌ {table} 同步失败: {result.get('error_message', '未知错误')}")
            except Exception as e:
                print(f"❌ {table} 同步异常: {e}")
        
        # 同步最近的行情数据
        print(f"\n📈 同步最近行情数据...")
        recent_tables = ['daily', 'daily_basic']
        
        # 同步最近3个月的数据
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now().replace(month=datetime.now().month-3)).strftime('%Y%m%d')
        
        for table in recent_tables:
            print(f"\n📊 同步 {table} ({start_date}-{end_date})...")
            try:
                result = syncer.sync_table_enhanced(
                    table, 
                    start_date=start_date, 
                    end_date=end_date
                )
                if result['status'] == 'success':
                    print(f"✅ {table} 同步成功: {result['record_count']:,} 条记录")
                else:
                    print(f"❌ {table} 同步失败: {result.get('error_message', '未知错误')}")
            except Exception as e:
                print(f"❌ {table} 同步异常: {e}")
        
        print("\n✅ 数据同步完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据同步失败: {e}")
        return False

def run_backtest():
    """运行策略回测"""
    print("\n🚀 运行策略回测...")
    
    try:
        from strategy_backtest import StrategyBacktest
        
        # 创建回测实例
        backtest = StrategyBacktest()
        
        # 运行回测
        print("📊 开始回测计算...")
        start_time = time.time()
        
        if backtest.run_backtest():
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"✅ 回测完成，耗时 {duration:.1f} 秒")
            
            # 生成报告
            backtest.generate_report()
            
            return True
        else:
            print("❌ 回测失败")
            return False
            
    except Exception as e:
        print(f"❌ 回测运行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_results():
    """分析回测结果"""
    print("\n📊 分析回测结果...")
    
    try:
        from strategy_analyzer import StrategyAnalyzer
        
        # 检查结果文件
        if not os.path.exists('backtest_results.xlsx'):
            print("❌ 找不到回测结果文件")
            return False
        
        # 创建分析器
        analyzer = StrategyAnalyzer()
        
        # 加载和分析数据
        if analyzer.load_results():
            analyzer.generate_report()
            print("✅ 结果分析完成")
            return True
        else:
            print("❌ 结果分析失败")
            return False
            
    except Exception as e:
        print(f"❌ 结果分析失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 量化投资策略回测系统")
    print("=" * 60)
    print("策略概述:")
    print("  • 大盘择时: 市净率2倍买入，1.2倍满仓")
    print("  • 个股评分: PE、PB、涨幅三因子评分")
    print("  • 风险控制: 行业分散，单行业不超过3%")
    print("  • 止盈策略: 涨幅100%减半，200%清仓")
    print("=" * 60)
    
    # 步骤1: 检查数据
    if not check_data_availability():
        print("\n💡 数据不足，是否需要同步数据？")
        response = input("输入 'y' 同步数据，其他键跳过: ").strip().lower()
        
        if response == 'y':
            if not sync_required_data():
                print("❌ 数据同步失败，无法继续回测")
                return
        else:
            print("⚠️  数据可能不足，回测结果可能不准确")
    
    # 步骤2: 运行回测
    print("\n" + "=" * 60)
    response = input("🤔 是否开始策略回测？(y/N): ").strip().lower()
    
    if response != 'y':
        print("👋 用户取消回测")
        return
    
    if not run_backtest():
        print("❌ 回测失败")
        return
    
    # 步骤3: 分析结果
    print("\n" + "=" * 60)
    if analyze_results():
        print("\n🎉 策略回测完成！")
        print("\n📁 生成的文件:")
        print("  • backtest_results.xlsx - 详细回测数据")
        print("  • strategy_performance.png - 策略表现图表")
        
        print("\n💡 下一步建议:")
        print("  • 查看回测报告，分析策略表现")
        print("  • 尝试参数优化，寻找最佳参数")
        print("  • 考虑实盘应用的风险控制")
        
    else:
        print("❌ 结果分析失败")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
