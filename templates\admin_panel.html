<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员面板 - Tushare数据镜像系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='style.css') }}" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-danger">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="bi bi-shield-lock"></i>
                管理员面板
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('index') }}">
                    <i class="bi bi-house"></i>
                    返回首页
                </a>
                <a class="nav-link" href="#" onclick="logout()">
                    <i class="bi bi-box-arrow-right"></i>
                    退出
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- 系统概览 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center bg-primary text-white">
                    <div class="card-body">
                        <i class="bi bi-people fs-1"></i>
                        <h5 class="card-title">用户总数</h5>
                        <p class="card-text fs-4" id="totalUsers">-</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center bg-success text-white">
                    <div class="card-body">
                        <i class="bi bi-database fs-1"></i>
                        <h5 class="card-title">数据记录</h5>
                        <p class="card-text fs-4" id="totalRecords">-</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center bg-info text-white">
                    <div class="card-body">
                        <i class="bi bi-activity fs-1"></i>
                        <h5 class="card-title">今日API调用</h5>
                        <p class="card-text fs-4" id="todayAPICalls">-</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center bg-warning text-white">
                    <div class="card-body">
                        <i class="bi bi-exclamation-triangle fs-1"></i>
                        <h5 class="card-title">系统告警</h5>
                        <p class="card-text fs-4" id="alertCount">-</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能标签页 -->
        <ul class="nav nav-tabs" id="adminTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button">
                    <i class="bi bi-people"></i> 用户管理
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="monitor-tab" data-bs-toggle="tab" data-bs-target="#monitor" type="button">
                    <i class="bi bi-graph-up"></i> 系统监控
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="sync-tab" data-bs-toggle="tab" data-bs-target="#sync" type="button">
                    <i class="bi bi-arrow-repeat"></i> 同步管理
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="export-tab" data-bs-toggle="tab" data-bs-target="#export" type="button">
                    <i class="bi bi-download"></i> 数据导出
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button">
                    <i class="bi bi-gear"></i> 系统设置
                </button>
            </li>
        </ul>

        <div class="tab-content" id="adminTabContent">
            <!-- 用户管理 -->
            <div class="tab-pane fade show active" id="users" role="tabpanel">
                <div class="card mt-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">用户管理</h5>
                        <div>
                            <input type="text" class="form-control d-inline-block" style="width: 200px;" 
                                   id="userSearch" placeholder="搜索用户...">
                            <button class="btn btn-primary ms-2" onclick="searchUsers()">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>用户名</th>
                                        <th>邮箱</th>
                                        <th>状态</th>
                                        <th>类型</th>
                                        <th>今日调用</th>
                                        <th>注册时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="usersTableBody">
                                    <tr>
                                        <td colspan="8" class="text-center">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <nav>
                            <ul class="pagination justify-content-center" id="usersPagination">
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- 系统监控 -->
            <div class="tab-pane fade" id="monitor" role="tabpanel">
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">系统资源使用</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="systemChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">数据库状态</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="databaseChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">系统告警</h6>
                    </div>
                    <div class="card-body">
                        <div id="alertsContainer">
                            <div class="text-center text-muted">加载中...</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 同步管理 -->
            <div class="tab-pane fade" id="sync" role="tabpanel">
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">数据同步状态</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>数据表</th>
                                        <th>中文名</th>
                                        <th>记录数</th>
                                        <th>同步状态</th>
                                        <th>最后同步</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="syncStatusTable">
                                    <tr>
                                        <td colspan="6" class="text-center">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据导出 -->
            <div class="tab-pane fade" id="export" role="tabpanel">
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">数据导出管理</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>单表导出</h6>
                                <div class="mb-3">
                                    <select class="form-select" id="exportTableSelect">
                                        <option value="">选择数据表...</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <select class="form-select" id="exportFormatSelect">
                                        <option value="csv">CSV格式</option>
                                        <option value="excel">Excel格式</option>
                                        <option value="json">JSON格式</option>
                                    </select>
                                </div>
                                <button class="btn btn-primary" onclick="exportSingleTable()">
                                    <i class="bi bi-download"></i> 导出
                                </button>
                            </div>
                            <div class="col-md-6">
                                <h6>批量导出</h6>
                                <div class="mb-3">
                                    <div id="batchExportTables" style="max-height: 200px; overflow-y: auto;">
                                        <!-- 动态生成复选框 -->
                                    </div>
                                </div>
                                <button class="btn btn-success" onclick="exportBatchTables()">
                                    <i class="bi bi-archive"></i> 批量导出
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统设置 -->
            <div class="tab-pane fade" id="settings" role="tabpanel">
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">系统维护</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>数据清理</h6>
                                <button class="btn btn-warning me-2" onclick="cleanupAPILogs()">
                                    <i class="bi bi-trash"></i> 清理API调用记录
                                </button>
                                <button class="btn btn-warning" onclick="cleanupOldLogs()">
                                    <i class="bi bi-journal-x"></i> 清理旧日志
                                </button>
                            </div>
                            <div class="col-md-6">
                                <h6>系统信息</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td>系统版本:</td>
                                        <td>v1.0.0</td>
                                    </tr>
                                    <tr>
                                        <td>数据库大小:</td>
                                        <td id="databaseSize">-</td>
                                    </tr>
                                    <tr>
                                        <td>运行时间:</td>
                                        <td id="systemUptime">-</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 消息提示区域 -->
        <div class="row mt-4">
            <div class="col-12">
                <div id="messageContainer"></div>
            </div>
        </div>
    </div>

    <!-- 用户编辑模态框 -->
    <div class="modal fade" id="userEditModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="userEditForm">
                        <input type="hidden" id="editUserId">
                        <div class="mb-3">
                            <label class="form-label">用户名</label>
                            <input type="text" class="form-control" id="editUsername" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">邮箱</label>
                            <input type="email" class="form-control" id="editEmail" readonly>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="editIsActive">
                                <label class="form-check-label" for="editIsActive">账户激活</label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="editIsPremium">
                                <label class="form-check-label" for="editIsPremium">高级用户</label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">每日API限制</label>
                            <input type="number" class="form-control" id="editApiLimit" min="0" max="100000">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveUserEdit()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <!-- 管理员面板JS -->
    <script src="{{ url_for('static', filename='admin_panel.js') }}"></script>
</body>
</html>
