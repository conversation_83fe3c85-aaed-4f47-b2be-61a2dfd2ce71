#!/usr/bin/env python3
"""
同步缺失的历史数据
专门解决基本面数据缺失问题
"""

import sys
import os
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def sync_missing_basic_data():
    """同步缺失的基本面数据"""
    print("📊 同步缺失的基本面数据...")
    print("=" * 60)
    
    try:
        from sync.enhanced_data_sync import EnhancedTushareDataSync
        
        def progress_callback(data):
            progress = data.get('progress', 0)
            message = data.get('message', '')
            table_name = data.get('table_name', '')
            print(f"\r[{progress:3d}%] {table_name}: {message}", end='', flush=True)
            if progress >= 100:
                print()
        
        syncer = EnhancedTushareDataSync(progress_callback=progress_callback)
        
        # 分批同步，避免API限制
        # 优先同步最近几年的数据
        years_to_sync = [2020, 2021, 2022, 2023, 2024, 2019, 2018, 2017, 2016, 2015]

        print(f"🎯 目标: 分批同步 {len(years_to_sync)} 年的基本面数据")
        print(f"📅 优先顺序: {years_to_sync}")
        print(f"⚠️  注意: 遇到API限制会自动等待和重试")

        success_count = 0
        total_records = 0

        for i, year in enumerate(years_to_sync):
            start_date = f"{year}0101"
            end_date = f"{year}1231"

            print(f"\n📊 [{i+1}/{len(years_to_sync)}] 同步 {year} 年基本面数据...")
            print(f"   日期范围: {start_date} - {end_date}")

            # 重试机制
            max_retries = 3
            retry_count = 0

            while retry_count < max_retries:
                try:
                    result = syncer.sync_table_enhanced(
                        'daily_basic',
                        start_date=start_date,
                        end_date=end_date,
                        force_full=False
                    )

                    if result['status'] == 'success':
                        records = result['record_count']
                        total_records += records
                        success_count += 1
                        print(f"✅ {year} 年基本面数据同步成功: {records:,} 条记录")
                        break  # 成功则跳出重试循环
                    else:
                        error_msg = result.get('error_message', '未知错误')
                        print(f"❌ {year} 年基本面数据同步失败: {error_msg}")

                        # 如果是API限制，等待更长时间
                        if '700次' in error_msg or 'limit' in error_msg.lower() or '频率' in error_msg:
                            wait_time = 120 + retry_count * 60  # 递增等待时间
                            print(f"   ⏱️  API频率限制，等待 {wait_time} 秒...")
                            time.sleep(wait_time)
                            retry_count += 1
                        else:
                            break  # 其他错误不重试

                except Exception as e:
                    error_str = str(e)
                    print(f"❌ {year} 年基本面数据同步异常: {e}")

                    # 检查是否是API限制异常
                    if '700次' in error_str or 'limit' in error_str.lower() or '频率' in error_str:
                        wait_time = 120 + retry_count * 60
                        print(f"   ⏱️  API频率限制异常，等待 {wait_time} 秒...")
                        time.sleep(wait_time)
                        retry_count += 1
                    else:
                        break  # 其他异常不重试

            # 每年之间休息，避免API限制
            if i < len(years_to_sync) - 1:
                print("   ⏱️  休息30秒，避免API限制...")
                time.sleep(30)
        
        print(f"\n📊 基本面数据同步总结:")
        print(f"   成功年份: {success_count}/{len(years_to_sync)}")
        print(f"   总记录数: {total_records:,}")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 基本面数据同步失败: {e}")
        return False

def sync_missing_daily_data():
    """同步缺失的日线数据"""
    print("\n📈 同步缺失的日线数据...")
    print("=" * 60)
    
    try:
        from sync.enhanced_data_sync import EnhancedTushareDataSync
        
        def progress_callback(data):
            progress = data.get('progress', 0)
            message = data.get('message', '')
            table_name = data.get('table_name', '')
            print(f"\r[{progress:3d}%] {table_name}: {message}", end='', flush=True)
            if progress >= 100:
                print()
        
        syncer = EnhancedTushareDataSync(progress_callback=progress_callback)
        
        # 重点同步缺失年份的日线数据
        years_to_sync = [2016, 2017, 2018, 2019, 2020, 2021, 2022, 2023]  # 2015年有部分数据，2024年有部分数据
        
        print(f"🎯 目标: 同步 {len(years_to_sync)} 年的日线数据")
        print(f"📅 年份: {years_to_sync}")
        
        success_count = 0
        total_records = 0
        
        for i, year in enumerate(years_to_sync):
            start_date = f"{year}0101"
            end_date = f"{year}1231"
            
            print(f"\n📈 [{i+1}/{len(years_to_sync)}] 同步 {year} 年日线数据...")
            print(f"   日期范围: {start_date} - {end_date}")
            
            try:
                result = syncer.sync_table_enhanced(
                    'daily', 
                    start_date=start_date, 
                    end_date=end_date,
                    force_full=False
                )
                
                if result['status'] == 'success':
                    records = result['record_count']
                    total_records += records
                    success_count += 1
                    print(f"✅ {year} 年日线数据同步成功: {records:,} 条记录")
                else:
                    error_msg = result.get('error_message', '未知错误')
                    print(f"❌ {year} 年日线数据同步失败: {error_msg}")
                    
                    # 如果是API限制，等待更长时间
                    if 'limit' in error_msg.lower() or 'quota' in error_msg.lower():
                        print("   ⏱️  API限制，等待60秒...")
                        time.sleep(60)
                
            except Exception as e:
                print(f"❌ {year} 年日线数据同步异常: {e}")
            
            # 每年之间休息，避免API限制
            if i < len(years_to_sync) - 1:
                print("   ⏱️  休息5秒...")
                time.sleep(5)
        
        print(f"\n📈 日线数据同步总结:")
        print(f"   成功年份: {success_count}/{len(years_to_sync)}")
        print(f"   总记录数: {total_records:,}")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 日线数据同步失败: {e}")
        return False

def verify_data_after_sync():
    """同步后验证数据"""
    print("\n🔍 验证同步后的数据...")
    print("=" * 60)
    
    try:
        from database.models import db_config
        from sqlalchemy import text
        
        session = db_config.get_session()
        
        # 检查各年份的数据情况
        print("📊 各年份数据统计:")
        
        for year in range(2015, 2026):
            daily_count = session.execute(text(f"""
                SELECT COUNT(*) FROM daily 
                WHERE trade_date >= '{year}0101' AND trade_date <= '{year}1231'
            """)).scalar()
            
            basic_count = session.execute(text(f"""
                SELECT COUNT(*) FROM daily_basic 
                WHERE trade_date >= '{year}0101' AND trade_date <= '{year}1231'
            """)).scalar()
            
            # 计算共同日期
            common_count = session.execute(text(f"""
                SELECT COUNT(DISTINCT d.trade_date) 
                FROM daily d 
                INNER JOIN daily_basic db ON d.trade_date = db.trade_date 
                WHERE d.trade_date >= '{year}0101' AND d.trade_date <= '{year}1231'
            """)).scalar()
            
            status = "✅" if common_count > 100 else "❌" if common_count == 0 else "⚠️"
            print(f"   {status} {year}: 日线{daily_count:,}, 基本面{basic_count:,}, 共同{common_count:,}天")
        
        # 计算总的共同交易日
        total_common = session.execute(text("""
            SELECT COUNT(DISTINCT d.trade_date) 
            FROM daily d 
            INNER JOIN daily_basic db ON d.trade_date = db.trade_date 
            WHERE d.trade_date >= '20150101' AND d.trade_date <= '20251231'
        """)).scalar()
        
        print(f"\n📊 总体数据统计:")
        print(f"   共同交易日总数: {total_common:,} 天")
        
        session.close()
        
        # 评估数据是否足够
        if total_common >= 1000:  # 约4年的交易日
            print(f"✅ 数据充足，可以进行有效回测")
            return True
        elif total_common >= 250:  # 约1年的交易日
            print(f"⚠️  数据基本够用，但建议继续同步")
            return True
        else:
            print(f"❌ 数据仍然不足，需要继续同步")
            return False
        
    except Exception as e:
        print(f"❌ 数据验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🔄 缺失数据同步工具")
    print("=" * 60)
    print("🎯 目标: 解决基本面数据缺失问题")
    print("📊 当前问题: 只有2025年7-8月有完整数据")
    print("🔧 解决方案: 同步2015-2024年的历史数据")
    
    # 询问用户确认
    print("\n" + "=" * 60)
    print("⚠️  注意: 这个过程可能需要2-4小时，会消耗较多API调用次数")
    response = input("🤔 是否开始同步缺失的历史数据？(y/N): ").strip().lower()
    
    if response != 'y':
        print("👋 用户取消同步")
        return
    
    start_time = time.time()
    
    # 1. 同步基本面数据（优先级最高）
    print("\n🎯 第1步: 同步基本面数据（最重要）")
    basic_success = sync_missing_basic_data()
    
    # 2. 同步日线数据
    print("\n🎯 第2步: 同步日线数据")
    daily_success = sync_missing_daily_data()
    
    # 3. 验证数据
    print("\n🎯 第3步: 验证数据完整性")
    data_ready = verify_data_after_sync()
    
    end_time = time.time()
    duration = (end_time - start_time) / 60
    
    print("\n" + "=" * 60)
    print(f"⏱️  总耗时: {duration:.1f} 分钟")
    
    if data_ready:
        print("🎉 数据同步成功！现在可以进行完整的回测了。")
        print("\n💡 下一步:")
        print("   python run_strategy_backtest.py")
    else:
        print("⚠️  数据同步不完整，可能需要重试或检查API限制。")
        print("\n💡 建议:")
        print("   1. 检查Tushare API调用次数限制")
        print("   2. 等待一段时间后重试")
        print("   3. 分批次同步数据")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户中断同步")
    except Exception as e:
        print(f"\n❌ 同步过程异常: {e}")
        import traceback
        traceback.print_exc()
