#!/usr/bin/env python3
"""
测试修复后的策略回测系统
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_data_types():
    """测试数据类型问题"""
    print("🔍 测试数据类型...")
    
    try:
        from database.models import db_config
        from sqlalchemy import text
        
        session = db_config.get_session()
        
        # 检查数据类型
        print("  📊 检查daily表数据类型...")
        daily_sample = pd.read_sql("SELECT * FROM daily LIMIT 5", session.bind)
        print(f"    trade_date类型: {daily_sample['trade_date'].dtype}")
        print(f"    样本数据: {daily_sample['trade_date'].tolist()}")
        
        print("  📊 检查daily_basic表数据类型...")
        basic_sample = pd.read_sql("SELECT * FROM daily_basic LIMIT 5", session.bind)
        print(f"    trade_date类型: {basic_sample['trade_date'].dtype}")
        print(f"    样本数据: {basic_sample['trade_date'].tolist()}")
        
        session.close()
        
        # 测试类型转换
        print("  🔧 测试类型转换...")
        daily_sample['trade_date'] = daily_sample['trade_date'].astype(str)
        basic_sample['trade_date'] = basic_sample['trade_date'].astype(str)
        
        # 测试合并
        merged = pd.merge(
            daily_sample[['ts_code', 'trade_date', 'close']], 
            basic_sample[['ts_code', 'trade_date', 'pe_ttm', 'pb']], 
            on=['ts_code', 'trade_date'], 
            how='inner'
        )
        
        print(f"    合并结果: {len(merged)} 条记录")
        print("  ✅ 数据类型测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据类型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_strategy_components():
    """测试策略组件"""
    print("\n🔍 测试策略组件...")
    
    try:
        from strategy_backtest import StrategyBacktest
        
        # 创建回测实例
        backtest = StrategyBacktest()
        print("✅ 策略实例创建成功")
        
        # 测试数据加载
        print("  📊 测试数据加载...")
        if backtest.load_data():
            print("  ✅ 数据加载成功")
            
            # 检查数据
            print(f"    股票数量: {len(backtest.stock_basic)}")
            print(f"    日线数据: {len(backtest.daily_data)} 条")
            print(f"    基本面数据: {len(backtest.daily_basic)} 条")
            print(f"    合并数据: {len(backtest.stock_data)} 条")
            print(f"    交易日数: {len(backtest.trade_dates)}")
            
            # 测试单日计算
            if len(backtest.trade_dates) > 0:
                test_date = backtest.trade_dates[0]
                print(f"\n  🧪 测试单日计算 ({test_date})...")
                
                # 测试大盘PB计算
                market_pb = backtest.calculate_market_pb(test_date)
                print(f"    大盘PB: {market_pb:.2f}")
                
                # 测试股票评分
                scored_stocks = backtest.calculate_stock_scores(test_date)
                print(f"    评分股票数: {len(scored_stocks)}")
                
                if len(scored_stocks) > 0:
                    top_stock = scored_stocks.iloc[0]
                    print(f"    最高分股票: {top_stock['ts_code']} (分数: {top_stock['total_score']:.1f})")
                
                # 测试仓位计算
                position_weight = backtest.get_position_weight(test_date)
                print(f"    目标仓位: {position_weight:.1%}")
                
                print("  ✅ 单日计算测试通过")
            
            return True
        else:
            print("  ❌ 数据加载失败")
            return False
            
    except Exception as e:
        print(f"❌ 策略组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mini_backtest():
    """测试迷你回测（只测试几天）"""
    print("\n🔍 测试迷你回测...")
    
    try:
        from strategy_backtest import StrategyBacktest
        
        # 创建回测实例
        backtest = StrategyBacktest()
        
        # 修改为短期回测
        backtest.start_date = '20240101'
        backtest.end_date = '20240110'
        
        print(f"  📅 迷你回测期间: {backtest.start_date} - {backtest.end_date}")
        
        # 加载数据
        if not backtest.load_data():
            print("  ❌ 数据加载失败")
            return False
        
        # 限制交易日数量
        if len(backtest.trade_dates) > 5:
            backtest.trade_dates = backtest.trade_dates[:5]
        
        print(f"  📊 测试交易日: {len(backtest.trade_dates)} 天")
        
        # 运行迷你回测
        success = True
        for i, date in enumerate(backtest.trade_dates):
            print(f"    测试第 {i+1} 天: {date}")
            
            try:
                # 1. 检查卖出信号
                sell_orders = backtest.check_sell_signals(date)
                
                # 2. 计算目标仓位
                target_weight = backtest.get_position_weight(date)
                
                # 3. 选股
                scored_stocks = backtest.calculate_stock_scores(date)
                buy_orders = backtest.select_stocks(scored_stocks, target_weight)
                
                # 4. 执行交易
                backtest.execute_trades(date, buy_orders, sell_orders)
                
                # 5. 计算组合价值
                portfolio_value = backtest.calculate_portfolio_value(date)
                
                print(f"      组合价值: {portfolio_value:,.0f} 元")
                
            except Exception as e:
                print(f"      ❌ 第 {i+1} 天计算失败: {e}")
                success = False
                break
        
        if success:
            print("  ✅ 迷你回测测试通过")
            
            # 显示最终结果
            final_value = backtest.calculate_portfolio_value(backtest.trade_dates[-1])
            total_return = (final_value / backtest.initial_capital - 1) * 100
            print(f"    最终价值: {final_value:,.0f} 元")
            print(f"    总收益率: {total_return:.2f}%")
            print(f"    交易次数: {len(backtest.trade_records)}")
            
        return success
        
    except Exception as e:
        print(f"❌ 迷你回测失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 策略回测系统修复测试")
    print("=" * 50)
    
    # 测试1: 数据类型
    if not test_data_types():
        print("❌ 数据类型测试失败，无法继续")
        return
    
    # 测试2: 策略组件
    if not test_strategy_components():
        print("❌ 策略组件测试失败，无法继续")
        return
    
    # 测试3: 迷你回测
    if not test_mini_backtest():
        print("❌ 迷你回测失败")
        return
    
    print("\n🎉 所有测试通过！")
    print("✅ 策略回测系统已修复，可以正常运行")
    print("\n💡 现在可以运行完整回测:")
    print("   python strategy_backtest.py")
    print("   python run_strategy_backtest.py")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
