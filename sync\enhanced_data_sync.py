"""
增强版数据同步系统
优化下载性能、错误处理和用户体验
"""

import tushare as ts
import pandas as pd
from datetime import datetime, timedelta
import time
import logging
import traceback
from typing import Optional, List, Dict, Any, Callable
from sqlalchemy import text
from sqlalchemy.dialects.sqlite import insert as sqlite_insert
import threading

# 尝试导入可选依赖
try:
    import requests
except ImportError:
    requests = None

try:
    from concurrent.futures import ThreadPoolExecutor, as_completed
except ImportError:
    ThreadPoolExecutor = None
    as_completed = None

try:
    import queue
except ImportError:
    import Queue as queue

import json

from database.models import db_config, SyncStatus
from config import get_config, TUSHARE_TABLES

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedTushareDataSync:
    """增强版Tushare数据同步器"""
    
    def __init__(self, token: str = None, progress_callback: Callable = None):
        self.config = get_config()
        self.token = token or self.config.TUSHARE_TOKEN
        self.progress_callback = progress_callback
        
        # 初始化Tushare
        ts.set_token(self.token)
        self.pro = ts.pro_api()
        
        # 同步配置
        self.batch_size = self.config.SYNC_CONFIG.get('batch_size', 1000)
        self.request_delay = self.config.SYNC_CONFIG.get('request_delay', 0.3)
        self.max_retries = self.config.SYNC_CONFIG.get('max_retries', 3)
        self.retry_delay = self.config.SYNC_CONFIG.get('retry_delay', 1.0)
        
        # 状态跟踪
        self.is_cancelled = False
        self.current_progress = 0
        self.total_progress = 100
        self.lock = threading.Lock()
        try:
            self.error_queue = queue.Queue()
        except:
            self.error_queue = None
        
        # 网络重试配置
        if requests:
            try:
                self.session = requests.Session()
                self.session.mount('http://', requests.adapters.HTTPAdapter(max_retries=3))
                self.session.mount('https://', requests.adapters.HTTPAdapter(max_retries=3))
            except:
                self.session = None
        else:
            self.session = None
        
        # 验证连接
        self._validate_connection()
    
    def _validate_connection(self):
        """验证Tushare连接"""
        try:
            logger.info("验证Tushare API连接...")
            test_df = self.pro.stock_basic(limit=1)
            if test_df.empty:
                raise Exception("Tushare API返回空数据")
            logger.info("✓ Tushare API连接验证成功")
        except Exception as e:
            logger.error(f"✗ Tushare API连接失败: {e}")
            raise Exception(f"无法连接到Tushare API: {e}")
    
    def cancel_sync(self):
        """取消同步"""
        with self.lock:
            self.is_cancelled = True
            logger.info("🛑 同步已被用户取消")
    
    def _update_progress(self, current: int, total: int, message: str = "", table_name: str = ""):
        """更新进度"""
        with self.lock:
            if total > 0:
                self.current_progress = int((current / total) * 100)
            else:
                self.current_progress = 0
            
            progress_data = {
                'progress': self.current_progress,
                'current': current,
                'total': total,
                'message': message,
                'table_name': table_name,
                'is_cancelled': self.is_cancelled,
                'timestamp': datetime.now().isoformat()
            }
            
            if self.progress_callback:
                try:
                    self.progress_callback(progress_data)
                except Exception as e:
                    logger.warning(f"进度回调失败: {e}")
    
    def _check_cancellation(self):
        """检查是否被取消"""
        with self.lock:
            if self.is_cancelled:
                raise Exception("同步已被用户取消")
    
    def sync_table_enhanced(self, table_name: str, start_date: str = None, 
                           end_date: str = None, force_full: bool = False) -> Dict[str, Any]:
        """增强版表同步"""
        if table_name not in TUSHARE_TABLES:
            raise ValueError(f"不支持的数据表: {table_name}")
        
        table_config = TUSHARE_TABLES[table_name]
        
        try:
            logger.info(f"🚀 开始同步表: {table_name} ({table_config['name']})")
            self._update_progress(0, 100, f"初始化同步 {table_config['name']}", table_name)
            
            # 重置状态
            with self.lock:
                self.is_cancelled = False
                if self.error_queue:
                    try:
                        while not self.error_queue.empty():
                            self.error_queue.get()
                    except:
                        pass
            
            # 更新数据库状态
            self._update_sync_status(table_name, 'syncing', None, None, None)
            
            # 参数验证
            self._validate_sync_parameters(table_name, start_date, end_date)
            
            # 确定同步策略
            if table_config['sync_type'] == 'full' or force_full:
                result = self._sync_full_table_enhanced(table_name, table_config)
            else:
                result = self._sync_incremental_table_enhanced(table_name, table_config, start_date, end_date)
            
            # 检查取消状态
            self._check_cancellation()
            
            # 数据质量验证
            self._validate_sync_result(table_name, result)
            
            # 更新成功状态
            self._update_sync_status(
                table_name, 'success', 
                result.get('sync_date'), 
                result['record_count'], 
                None
            )
            
            self._update_progress(100, 100, f"✅ 同步完成: {result['record_count']} 条记录", table_name)
            logger.info(f"✅ 表 {table_name} 同步完成: {result['record_count']} 条记录")
            
            return result
            
        except Exception as e:
            error_msg = str(e)
            logger.error(f"❌ 表 {table_name} 同步失败: {error_msg}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            
            # 更新错误状态
            self._update_sync_status(table_name, 'error', None, None, error_msg)
            self._update_progress(0, 100, f"❌ 同步失败: {error_msg}", table_name)
            
            return {
                'status': 'error',
                'record_count': 0,
                'sync_date': None,
                'error_message': error_msg,
                'table_name': table_name
            }
    
    def _validate_sync_parameters(self, table_name: str, start_date: str, end_date: str):
        """验证同步参数"""
        if start_date and end_date:
            try:
                start_dt = datetime.strptime(start_date, '%Y%m%d')
                end_dt = datetime.strptime(end_date, '%Y%m%d')
                
                if start_dt > end_dt:
                    raise ValueError("开始日期不能大于结束日期")
                
                if end_dt > datetime.now():
                    raise ValueError("结束日期不能大于当前日期")
                    
                # 检查日期范围是否合理（不超过5年）
                if (end_dt - start_dt).days > 1825:
                    logger.warning(f"日期范围较大: {(end_dt - start_dt).days} 天，建议分批同步")
                    
            except ValueError as e:
                if "time data" in str(e):
                    raise ValueError("日期格式错误，请使用YYYYMMDD格式")
                raise e
    
    def _sync_full_table_enhanced(self, table_name: str, table_config: Dict) -> Dict[str, Any]:
        """增强版全量同步"""
        api_name = table_config['api_name']
        total_records = 0
        
        logger.info(f"📊 执行全量同步: {table_name}")
        self._update_progress(10, 100, "准备全量同步", table_name)
        
        try:
            # 获取API方法
            api_method = getattr(self.pro, api_name)
            
            # 清空现有数据
            self._clear_table_data(table_name)
            self._update_progress(20, 100, "已清空现有数据", table_name)
            
            # 分批获取数据
            offset = 0
            batch_count = 0
            max_empty_batches = 3  # 连续空批次限制
            empty_batch_count = 0
            
            while True:
                self._check_cancellation()
                
                try:
                    # 获取数据
                    df = self._fetch_data_with_retry(api_method, limit=self.batch_size, offset=offset)
                    
                    if df.empty:
                        empty_batch_count += 1
                        if empty_batch_count >= max_empty_batches:
                            logger.info(f"连续 {max_empty_batches} 次获取空数据，结束同步")
                            break
                        continue
                    else:
                        empty_batch_count = 0
                    
                    # 保存数据
                    saved_count = self._save_dataframe_enhanced(table_name, df)
                    total_records += saved_count
                    batch_count += 1
                    
                    # 更新进度
                    progress = min(20 + (batch_count * 2), 90)  # 20-90%的进度
                    self._update_progress(
                        progress, 100, 
                        f"已同步 {total_records} 条记录 (批次 {batch_count})", 
                        table_name
                    )
                    
                    logger.info(f"批次 {batch_count}: 获取 {len(df)} 条，保存 {saved_count} 条，累计 {total_records} 条")
                    
                    # 检查是否还有更多数据
                    if len(df) < self.batch_size:
                        logger.info("已获取所有数据")
                        break
                    
                    offset += self.batch_size
                    
                    # 请求间隔
                    time.sleep(self.request_delay)
                    
                except Exception as e:
                    logger.error(f"批次 {batch_count + 1} 同步失败: {e}")
                    if "超过限制" in str(e) or "limit" in str(e).lower():
                        logger.warning("触发API限制，等待更长时间...")
                        time.sleep(self.retry_delay * 3)
                        continue
                    raise e
            
            self._update_progress(95, 100, "正在完成同步", table_name)
            
            return {
                'status': 'success',
                'record_count': total_records,
                'sync_date': datetime.now().strftime('%Y%m%d'),
                'sync_type': 'full',
                'batch_count': batch_count
            }
            
        except Exception as e:
            logger.error(f"全量同步失败: {e}")
            raise e

    def _sync_incremental_table_enhanced(self, table_name: str, table_config: Dict,
                                        start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """增强版增量同步"""
        api_name = table_config['api_name']
        total_records = 0

        # 确定日期范围
        if not start_date or not end_date:
            start_date, end_date = self._determine_sync_dates(table_name, table_config)

        logger.info(f"📈 执行增量同步: {table_name} ({start_date} - {end_date})")
        self._update_progress(10, 100, f"准备增量同步 {start_date}-{end_date}", table_name)

        try:
            # 获取API方法
            api_method = getattr(self.pro, api_name)

            # 按日期分段同步
            start_dt = datetime.strptime(start_date, '%Y%m%d')
            end_dt = datetime.strptime(end_date, '%Y%m%d')

            total_days = (end_dt - start_dt).days + 1
            current_day = 0

            # 分段处理
            chunk_days = self.config.SYNC_CONFIG.get('days_per_chunk', 30)
            current_date = start_dt

            while current_date <= end_dt:
                self._check_cancellation()

                # 计算当前段的结束日期
                chunk_end = min(current_date + timedelta(days=chunk_days - 1), end_dt)

                chunk_start_str = current_date.strftime('%Y%m%d')
                chunk_end_str = chunk_end.strftime('%Y%m%d')

                logger.info(f"同步日期段: {chunk_start_str} - {chunk_end_str}")

                # 同步当前日期段
                chunk_records = self._sync_date_range_enhanced(
                    api_method, table_name, chunk_start_str, chunk_end_str
                )

                total_records += chunk_records
                current_day += (chunk_end - current_date).days + 1

                # 更新进度
                progress = 10 + int((current_day / total_days) * 80)  # 10-90%
                self._update_progress(
                    progress, 100,
                    f"已同步 {total_records} 条记录 ({current_day}/{total_days} 天)",
                    table_name
                )

                # 移动到下一段
                current_date = chunk_end + timedelta(days=1)

                # 段间延迟
                if current_date <= end_dt:
                    time.sleep(self.request_delay * 2)

            self._update_progress(95, 100, "正在完成增量同步", table_name)

            return {
                'status': 'success',
                'record_count': total_records,
                'sync_date': end_date,
                'sync_type': 'incremental',
                'date_range': f"{start_date}-{end_date}"
            }

        except Exception as e:
            logger.error(f"增量同步失败: {e}")
            raise e

    def _sync_date_range_enhanced(self, api_method, table_name: str, start_date: str, end_date: str) -> int:
        """同步指定日期范围的数据"""
        total_records = 0

        try:
            # 构建查询参数
            params = {
                'start_date': start_date,
                'end_date': end_date
            }

            # 分批获取数据
            offset = 0
            while True:
                self._check_cancellation()

                # 添加分页参数
                current_params = params.copy()
                current_params.update({
                    'limit': self.batch_size,
                    'offset': offset
                })

                # 获取数据
                df = self._fetch_data_with_retry(api_method, **current_params)

                if df.empty:
                    break

                # 保存数据
                saved_count = self._save_dataframe_enhanced(table_name, df)
                total_records += saved_count

                logger.debug(f"日期段 {start_date}-{end_date}: 批次获取 {len(df)} 条，保存 {saved_count} 条")

                # 检查是否还有更多数据
                if len(df) < self.batch_size:
                    break

                offset += self.batch_size
                time.sleep(self.request_delay)

            return total_records

        except Exception as e:
            logger.error(f"日期范围 {start_date}-{end_date} 同步失败: {e}")
            raise e

    def _fetch_data_with_retry(self, api_method, **params) -> pd.DataFrame:
        """带重试的数据获取"""
        last_exception = None

        for attempt in range(self.max_retries):
            try:
                self._check_cancellation()

                # 调用API
                df = api_method(**params)

                if df is None:
                    df = pd.DataFrame()

                return df

            except Exception as e:
                last_exception = e
                error_msg = str(e).lower()

                # 检查是否是API限制错误
                if any(keyword in error_msg for keyword in ['limit', '限制', 'quota', 'rate']):
                    wait_time = self.retry_delay * (2 ** attempt) * 2  # 指数退避，API限制时等待更久
                    logger.warning(f"API限制，等待 {wait_time:.1f} 秒后重试 (尝试 {attempt + 1}/{self.max_retries})")
                    time.sleep(wait_time)
                    continue

                # 检查是否是网络错误
                elif any(keyword in error_msg for keyword in ['network', 'connection', 'timeout', '网络']):
                    wait_time = self.retry_delay * (2 ** attempt)
                    logger.warning(f"网络错误，等待 {wait_time:.1f} 秒后重试 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                    time.sleep(wait_time)
                    continue

                # 其他错误直接抛出
                else:
                    logger.error(f"API调用失败: {e}")
                    raise e

        # 所有重试都失败
        logger.error(f"API调用失败，已重试 {self.max_retries} 次")
        raise last_exception or Exception("API调用失败")

    def _save_dataframe_enhanced(self, table_name: str, df: pd.DataFrame) -> int:
        """增强版数据保存"""
        if df.empty:
            return 0

        try:
            # 数据预处理
            df = self._preprocess_dataframe(df, table_name)

            # 保存到数据库
            session = db_config.get_session()

            try:
                # 使用批量插入或更新
                saved_count = self._bulk_upsert(session, table_name, df)
                session.commit()

                return saved_count

            except Exception as e:
                session.rollback()
                logger.error(f"数据保存失败: {e}")
                raise e
            finally:
                session.close()

        except Exception as e:
            logger.error(f"数据处理失败: {e}")
            raise e

    def _preprocess_dataframe(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """数据预处理"""
        # 处理空值
        df = df.fillna('')

        # 处理日期格式
        date_columns = ['trade_date', 'ann_date', 'list_date', 'delist_date', 'cal_date']
        for col in date_columns:
            if col in df.columns:
                df[col] = df[col].astype(str).str.replace('-', '')

        # 处理数值类型
        numeric_columns = df.select_dtypes(include=['float64', 'int64']).columns
        for col in numeric_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)

        # 去重
        if 'ts_code' in df.columns and 'trade_date' in df.columns:
            df = df.drop_duplicates(subset=['ts_code', 'trade_date'])
        elif 'ts_code' in df.columns:
            df = df.drop_duplicates(subset=['ts_code'])

        return df

    def _bulk_upsert(self, session, table_name: str, df: pd.DataFrame) -> int:
        """批量插入或更新数据"""
        if df.empty:
            return 0

        try:
            # 转换为字典列表
            records = df.to_dict('records')

            # 使用SQLite的INSERT OR REPLACE
            if 'sqlite' in str(session.bind.url):
                # SQLite使用INSERT OR REPLACE
                insert_stmt = text(f"""
                    INSERT OR REPLACE INTO {table_name}
                    ({', '.join(df.columns)})
                    VALUES ({', '.join([f':{col}' for col in df.columns])})
                """)
            else:
                # 其他数据库使用INSERT ... ON DUPLICATE KEY UPDATE
                update_cols = ', '.join([f"{col}=VALUES({col})" for col in df.columns if col != 'id'])
                insert_stmt = text(f"""
                    INSERT INTO {table_name} ({', '.join(df.columns)})
                    VALUES ({', '.join([f':{col}' for col in df.columns])})
                    ON DUPLICATE KEY UPDATE {update_cols}
                """)

            # 批量执行
            session.execute(insert_stmt, records)
            return len(records)

        except Exception as e:
            logger.error(f"批量插入失败: {e}")
            raise e

    def _clear_table_data(self, table_name: str):
        """清空表数据"""
        session = db_config.get_session()
        try:
            session.execute(text(f"DELETE FROM {table_name}"))
            session.commit()
            logger.info(f"已清空表 {table_name} 的数据")
        except Exception as e:
            session.rollback()
            logger.error(f"清空表数据失败: {e}")
            raise e
        finally:
            session.close()

    def _determine_sync_dates(self, table_name: str, table_config: Dict) -> tuple:
        """确定同步日期范围"""
        # 获取最后同步日期
        session = db_config.get_session()
        try:
            sync_status = session.query(SyncStatus).filter_by(table_name=table_name).first()

            if sync_status and sync_status.last_sync_date:
                # 从最后同步日期的下一天开始
                last_date = datetime.strptime(sync_status.last_sync_date, '%Y%m%d')
                start_date = (last_date + timedelta(days=1)).strftime('%Y%m%d')
            else:
                # 首次同步，从30天前开始
                start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')

            # 结束日期为昨天（避免当天数据不完整）
            end_date = (datetime.now() - timedelta(days=1)).strftime('%Y%m%d')

            return start_date, end_date

        finally:
            session.close()

    def _validate_sync_result(self, table_name: str, result: Dict[str, Any]):
        """验证同步结果"""
        if result['record_count'] == 0:
            logger.warning(f"表 {table_name} 同步结果为0条记录，请检查数据源")

        # 检查数据完整性
        if result['record_count'] > 0:
            session = db_config.get_session()
            try:
                # 检查表中的实际记录数
                actual_count = session.execute(text(f"SELECT COUNT(*) FROM {table_name}")).scalar()
                logger.info(f"表 {table_name} 数据库中实际记录数: {actual_count}")

                # 检查数据质量
                self._check_data_quality(session, table_name)

            finally:
                session.close()

    def _check_data_quality(self, session, table_name: str):
        """检查数据质量"""
        try:
            # 检查是否有重复数据
            if table_name in ['daily', 'daily_basic']:
                duplicate_count = session.execute(text(f"""
                    SELECT COUNT(*) FROM (
                        SELECT ts_code, trade_date, COUNT(*) as cnt
                        FROM {table_name}
                        GROUP BY ts_code, trade_date
                        HAVING cnt > 1
                    ) t
                """)).scalar()

                if duplicate_count > 0:
                    logger.warning(f"表 {table_name} 发现 {duplicate_count} 组重复数据")

            # 检查空值情况
            total_records = session.execute(text(f"SELECT COUNT(*) FROM {table_name}")).scalar()
            if total_records > 0:
                # 检查关键字段的空值
                key_fields = ['ts_code'] if table_name != 'trade_cal' else ['cal_date']
                for field in key_fields:
                    try:
                        null_count = session.execute(text(f"""
                            SELECT COUNT(*) FROM {table_name}
                            WHERE {field} IS NULL OR {field} = ''
                        """)).scalar()

                        if null_count > 0:
                            logger.warning(f"表 {table_name} 字段 {field} 有 {null_count} 个空值")
                    except:
                        pass  # 字段可能不存在

        except Exception as e:
            logger.warning(f"数据质量检查失败: {e}")

    def _update_sync_status(self, table_name: str, status: str, sync_date: str = None,
                           record_count: int = None, error_message: str = None):
        """更新同步状态"""
        session = db_config.get_session()
        try:
            sync_status = session.query(SyncStatus).filter_by(table_name=table_name).first()

            if not sync_status:
                sync_status = SyncStatus(table_name=table_name)
                session.add(sync_status)

            sync_status.sync_status = status
            sync_status.last_sync_time = datetime.now()

            if sync_date:
                sync_status.last_sync_date = sync_date
            if record_count is not None:
                sync_status.record_count = record_count
            if error_message:
                sync_status.error_message = error_message

            session.commit()

        except Exception as e:
            session.rollback()
            logger.error(f"更新同步状态失败: {e}")
        finally:
            session.close()

    def get_sync_progress(self) -> Dict[str, Any]:
        """获取同步进度"""
        with self.lock:
            return {
                'progress': self.current_progress,
                'is_cancelled': self.is_cancelled,
                'timestamp': datetime.now().isoformat()
            }

    def batch_sync_tables(self, table_names: List[str], max_workers: int = 2) -> Dict[str, Any]:
        """批量同步多个表"""
        results = {}

        logger.info(f"🚀 开始批量同步 {len(table_names)} 个表")

        # 检查是否支持并发
        if ThreadPoolExecutor and as_completed and max_workers > 1:
            # 使用线程池并发同步（限制并发数避免API限制）
            try:
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    # 提交任务
                    future_to_table = {
                        executor.submit(self.sync_table_enhanced, table_name): table_name
                        for table_name in table_names
                    }

                    # 收集结果
                    for future in as_completed(future_to_table):
                        table_name = future_to_table[future]
                        try:
                            result = future.result()
                            results[table_name] = result
                            logger.info(f"✅ 表 {table_name} 同步完成")
                        except Exception as e:
                            logger.error(f"❌ 表 {table_name} 同步失败: {e}")
                            results[table_name] = {
                                'status': 'error',
                                'error_message': str(e),
                                'record_count': 0
                            }
            except Exception as e:
                logger.warning(f"并发同步失败，切换到串行模式: {e}")
                # 回退到串行同步
                results = self._sync_tables_sequential(table_names)
        else:
            # 串行同步
            logger.info("使用串行同步模式")
            results = self._sync_tables_sequential(table_names)

        # 统计结果
        success_count = sum(1 for r in results.values() if r['status'] == 'success')
        total_records = sum(r['record_count'] for r in results.values())

        logger.info(f"🎉 批量同步完成: {success_count}/{len(table_names)} 个表成功，总计 {total_records} 条记录")

        return {
            'success_count': success_count,
            'total_tables': len(table_names),
            'total_records': total_records,
            'results': results
        }

    def _sync_tables_sequential(self, table_names: List[str]) -> Dict[str, Any]:
        """串行同步多个表"""
        results = {}

        for i, table_name in enumerate(table_names, 1):
            logger.info(f"同步表 {i}/{len(table_names)}: {table_name}")

            try:
                result = self.sync_table_enhanced(table_name)
                results[table_name] = result
                logger.info(f"✅ 表 {table_name} 同步完成")
            except Exception as e:
                logger.error(f"❌ 表 {table_name} 同步失败: {e}")
                results[table_name] = {
                    'status': 'error',
                    'error_message': str(e),
                    'record_count': 0
                }

        return results

# 便捷函数
def sync_table_enhanced(table_name: str, start_date: str = None, end_date: str = None,
                       force_full: bool = False, progress_callback: Callable = None) -> Dict[str, Any]:
    """增强版单表同步便捷函数"""
    syncer = EnhancedTushareDataSync(progress_callback=progress_callback)
    return syncer.sync_table_enhanced(table_name, start_date, end_date, force_full)

def sync_daily_tables_enhanced(progress_callback: Callable = None) -> Dict[str, Any]:
    """增强版每日表同步"""
    daily_tables = [
        name for name, config in TUSHARE_TABLES.items()
        if config['sync_frequency'] == 'daily'
    ]

    syncer = EnhancedTushareDataSync(progress_callback=progress_callback)
    return syncer.batch_sync_tables(daily_tables)

def sync_all_tables_enhanced(progress_callback: Callable = None) -> Dict[str, Any]:
    """增强版全表同步"""
    all_tables = list(TUSHARE_TABLES.keys())

    syncer = EnhancedTushareDataSync(progress_callback=progress_callback)
    return syncer.batch_sync_tables(all_tables, max_workers=1)  # 全表同步使用单线程避免API限制
