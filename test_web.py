#!/usr/bin/env python3
"""
测试Web应用
"""

import sys
import os
import requests
import time
import threading

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def start_app():
    """启动Web应用"""
    from app import app, config
    print(f"启动Web应用在端口 {config.WEB_CONFIG['port']}")
    app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)

def test_api_endpoints():
    """测试API端点"""
    base_url = "http://localhost:5000"
    
    # 等待服务器启动
    print("等待服务器启动...")
    time.sleep(3)
    
    endpoints = [
        "/api/tables",
        "/api/database_status",
        "/api/sync_status"
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(base_url + endpoint, timeout=5)
            if response.status_code == 200:
                print(f"✓ {endpoint} - 状态码: {response.status_code}")
                if endpoint == "/api/tables":
                    data = response.json()
                    print(f"  返回 {len(data)} 个数据表")
            else:
                print(f"✗ {endpoint} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"✗ {endpoint} - 错误: {e}")

def main():
    """主函数"""
    print("=" * 50)
    print("Tushare数据镜像系统Web测试")
    print("=" * 50)
    
    # 在后台线程启动Web应用
    app_thread = threading.Thread(target=start_app, daemon=True)
    app_thread.start()
    
    # 测试API端点
    test_api_endpoints()
    
    print("\n测试完成！")
    print("Web应用正在运行在: http://localhost:5000")
    print("按 Ctrl+C 停止服务器")
    
    try:
        # 保持主线程运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n服务器已停止")

if __name__ == "__main__":
    main()
