#!/usr/bin/env python3
"""
测试增强版数据下载功能
验证优化后的同步系统
"""

import sys
import os
import time
import threading
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from sync.enhanced_data_sync import (
    EnhancedTushareDataSync, 
    sync_table_enhanced,
    sync_daily_tables_enhanced
)
from config import get_config, TUSHARE_TABLES

def test_progress_callback(data):
    """测试进度回调"""
    progress = data.get('progress', 0)
    message = data.get('message', '')
    table_name = data.get('table_name', '')
    
    # 创建进度条
    bar_length = 30
    filled_length = int(bar_length * progress // 100)
    bar = '█' * filled_length + '░' * (bar_length - filled_length)
    
    print(f'\r[{bar}] {progress:3d}% | {table_name} | {message}', end='', flush=True)
    
    if progress >= 100 or data.get('is_cancelled'):
        print()

def test_connection():
    """测试连接"""
    print("🔍 测试Tushare API连接...")
    
    try:
        syncer = EnhancedTushareDataSync()
        print("✅ Tushare API连接成功")
        return True
    except Exception as e:
        print(f"❌ Tushare API连接失败: {e}")
        return False

def test_single_table_sync():
    """测试单表同步"""
    print("\n📊 测试单表同步功能...")
    
    # 选择一个小表进行测试
    test_table = 'trade_cal'  # 交易日历表，数据量较小
    
    try:
        print(f"🔄 开始同步表: {test_table}")
        
        syncer = EnhancedTushareDataSync(progress_callback=test_progress_callback)
        
        # 同步最近30天的数据
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
        
        result = syncer.sync_table_enhanced(
            test_table, 
            start_date=start_date, 
            end_date=end_date
        )
        
        if result['status'] == 'success':
            print(f"✅ 单表同步成功!")
            print(f"   📊 记录数: {result['record_count']:,}")
            print(f"   📅 同步日期: {result.get('sync_date', '未知')}")
            print(f"   🔄 同步类型: {result.get('sync_type', '未知')}")
            return True
        else:
            print(f"❌ 单表同步失败: {result.get('error_message', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 单表同步异常: {e}")
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n🛠️  测试错误处理功能...")
    
    try:
        syncer = EnhancedTushareDataSync(progress_callback=test_progress_callback)
        
        # 测试无效表名
        print("🔍 测试无效表名...")
        try:
            result = syncer.sync_table_enhanced('invalid_table')
            print("❌ 应该抛出异常但没有")
            return False
        except ValueError as e:
            print(f"✅ 正确捕获无效表名错误: {e}")
        
        # 测试无效日期格式
        print("🔍 测试无效日期格式...")
        try:
            result = syncer.sync_table_enhanced('trade_cal', start_date='invalid_date')
            print("❌ 应该抛出异常但没有")
            return False
        except Exception as e:
            print(f"✅ 正确捕获日期格式错误: {e}")
        
        # 测试取消功能
        print("🔍 测试取消功能...")
        
        def cancel_after_delay():
            time.sleep(2)  # 2秒后取消
            syncer.cancel_sync()
            print("\n🛑 已发送取消信号")
        
        cancel_thread = threading.Thread(target=cancel_after_delay)
        cancel_thread.start()
        
        try:
            result = syncer.sync_table_enhanced('stock_basic', force_full=True)
            if 'cancelled' in result.get('error_message', '').lower():
                print("✅ 取消功能正常工作")
            else:
                print("⚠️  取消功能可能未正常工作")
        except Exception as e:
            if 'cancel' in str(e).lower():
                print("✅ 取消功能正常工作")
            else:
                print(f"⚠️  取消功能异常: {e}")
        
        cancel_thread.join()
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试异常: {e}")
        return False

def test_data_quality():
    """测试数据质量检查"""
    print("\n🔍 测试数据质量检查...")
    
    try:
        from database.models import db_config
        from sqlalchemy import text
        
        session = db_config.get_session()
        
        try:
            # 检查是否有数据
            tables_with_data = []
            for table_name in TUSHARE_TABLES.keys():
                try:
                    count = session.execute(text(f"SELECT COUNT(*) FROM {table_name}")).scalar()
                    if count > 0:
                        tables_with_data.append((table_name, count))
                except:
                    pass
            
            if tables_with_data:
                print(f"✅ 发现 {len(tables_with_data)} 个表有数据:")
                for table_name, count in tables_with_data[:5]:  # 只显示前5个
                    chinese_name = TUSHARE_TABLES.get(table_name, {}).get('name', table_name)
                    print(f"   📊 {chinese_name} ({table_name}): {count:,} 条记录")
                
                if len(tables_with_data) > 5:
                    print(f"   ... 还有 {len(tables_with_data) - 5} 个表")
                
                return True
            else:
                print("⚠️  没有发现数据，可能需要先进行同步")
                return False
                
        finally:
            session.close()
            
    except Exception as e:
        print(f"❌ 数据质量检查失败: {e}")
        return False

def test_performance():
    """测试性能"""
    print("\n⚡ 测试同步性能...")
    
    try:
        # 测试小表同步性能
        test_table = 'trade_cal'
        
        start_time = time.time()
        
        syncer = EnhancedTushareDataSync()
        result = syncer.sync_table_enhanced(test_table, force_full=True)
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result['status'] == 'success':
            records_per_second = result['record_count'] / duration if duration > 0 else 0
            print(f"✅ 性能测试完成:")
            print(f"   ⏱️  耗时: {duration:.2f} 秒")
            print(f"   📊 记录数: {result['record_count']:,}")
            print(f"   🚀 速度: {records_per_second:.1f} 记录/秒")
            
            # 性能评估
            if records_per_second > 100:
                print("   🎉 性能优秀!")
            elif records_per_second > 50:
                print("   👍 性能良好")
            else:
                print("   ⚠️  性能一般，可能需要优化")
            
            return True
        else:
            print(f"❌ 性能测试失败: {result.get('error_message')}")
            return False
            
    except Exception as e:
        print(f"❌ 性能测试异常: {e}")
        return False

def test_batch_sync():
    """测试批量同步"""
    print("\n📦 测试批量同步功能...")
    
    try:
        # 选择几个小表进行批量测试
        test_tables = ['trade_cal', 'stock_basic']
        
        syncer = EnhancedTushareDataSync(progress_callback=test_progress_callback)
        
        print(f"🔄 开始批量同步 {len(test_tables)} 个表...")
        
        start_time = time.time()
        result = syncer.batch_sync_tables(test_tables, max_workers=1)
        end_time = time.time()
        
        print(f"\n📊 批量同步结果:")
        print(f"   ✅ 成功: {result['success_count']}/{result['total_tables']} 个表")
        print(f"   📊 总记录: {result['total_records']:,} 条")
        print(f"   ⏱️  总耗时: {end_time - start_time:.2f} 秒")
        
        # 显示详细结果
        for table_name, table_result in result['results'].items():
            chinese_name = TUSHARE_TABLES.get(table_name, {}).get('name', table_name)
            if table_result['status'] == 'success':
                print(f"   ✅ {chinese_name}: {table_result['record_count']:,} 条记录")
            else:
                print(f"   ❌ {chinese_name}: {table_result.get('error_message', '未知错误')}")
        
        return result['success_count'] > 0
        
    except Exception as e:
        print(f"❌ 批量同步测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 增强版数据下载功能测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查配置
    try:
        config = get_config()
        if not config.TUSHARE_TOKEN:
            print("❌ 未配置TUSHARE_TOKEN，请先设置")
            return False
        print(f"✅ 配置检查通过")
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False
    
    # 运行测试
    tests = [
        ("连接测试", test_connection),
        ("单表同步", test_single_table_sync),
        ("错误处理", test_error_handling),
        ("数据质量检查", test_data_quality),
        ("性能测试", test_performance),
        ("批量同步", test_batch_sync),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    # 显示结果
    print("\n" + "=" * 60)
    print("🎯 测试结果汇总")
    print("=" * 60)
    print(f"总测试项: {total}")
    print(f"通过测试: {passed}")
    print(f"成功率: {(passed/total)*100:.1f}%")
    
    if passed >= total * 0.8:  # 80%通过率
        print("\n🎉 恭喜！增强版数据下载功能测试基本通过！")
        print("\n✨ 主要改进:")
        print("  • 🔄 实时进度显示")
        print("  • 🛠️  完善错误处理")
        print("  • 🚀 性能优化")
        print("  • 🔍 数据质量检查")
        print("  • 📦 批量同步支持")
        print("  • 🛑 取消功能")
        
        print("\n💡 使用建议:")
        print("  • 使用 python sync_cli_enhanced.py 进行命令行同步")
        print("  • 使用 python app.py 启动Web界面")
        print("  • 大量数据同步建议分批进行")
        
        return True
    else:
        print(f"\n❌ 部分测试失败 ({total-passed}/{total})")
        print("请检查错误信息并修复问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
