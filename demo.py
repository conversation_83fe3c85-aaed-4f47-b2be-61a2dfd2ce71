#!/usr/bin/env python3
"""
Tushare数据镜像系统演示脚本
展示系统的主要功能
"""

import sys
import os
import time
import threading
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def print_banner():
    """打印系统横幅"""
    print("=" * 80)
    print("🎉 Tushare数据镜像系统 v1.0.0")
    print("=" * 80)
    print("一个完整的Tushare数据镜像解决方案")
    print("提供与Tushare完全兼容的API接口和现代化的Web管理界面")
    print("=" * 80)

def demo_database():
    """演示数据库功能"""
    print("\n📊 数据库系统演示")
    print("-" * 40)
    
    try:
        from database.init_db import check_database
        
        print("✓ 检查数据库状态...")
        tables_info = check_database()
        
        if tables_info:
            print(f"✓ 数据库包含 {len(tables_info)} 个数据表")
            
            # 显示有数据的表
            data_tables = [(name, count) for name, count in tables_info 
                          if isinstance(count, int) and count > 0]
            
            if data_tables:
                print("✓ 已有数据的表:")
                for table_name, count in data_tables[:5]:  # 显示前5个
                    print(f"  • {table_name}: {count:,} 条记录")
                
                total_records = sum(count for _, count in data_tables)
                print(f"✓ 总记录数: {total_records:,}")
            else:
                print("ℹ 数据库已就绪，等待数据同步")
        
        return True
    except Exception as e:
        print(f"✗ 数据库演示失败: {e}")
        return False

def demo_sync():
    """演示数据同步功能"""
    print("\n🔄 数据同步系统演示")
    print("-" * 40)
    
    try:
        from sync.data_sync import TushareDataSync
        from config import TUSHARE_TABLES
        
        print("✓ 创建数据同步器...")
        syncer = TushareDataSync()
        
        print(f"✓ 支持 {len(TUSHARE_TABLES)} 个数据表同步:")
        for i, (table_name, config) in enumerate(list(TUSHARE_TABLES.items())[:5], 1):
            print(f"  {i}. {table_name} - {config['name']} ({config['sync_type']})")
        
        if len(TUSHARE_TABLES) > 5:
            print(f"  ... 还有 {len(TUSHARE_TABLES) - 5} 个表")
        
        # 测试Tushare连接
        print("✓ 测试Tushare API连接...")
        import tushare as ts
        from config import get_config
        
        config = get_config()
        ts.set_token(config.TUSHARE_TOKEN)
        pro = ts.pro_api()
        
        # 获取少量测试数据
        df = pro.stock_basic(limit=3)
        if not df.empty:
            print("✓ Tushare API连接正常")
            print("✓ 示例股票数据:")
            for _, stock in df.iterrows():
                print(f"  • {stock['ts_code']} - {stock['name']}")
        
        return True
    except Exception as e:
        print(f"✗ 数据同步演示失败: {e}")
        return False

def demo_api():
    """演示API功能"""
    print("\n🔌 API系统演示")
    print("-" * 40)
    
    try:
        from api.tushare_api import TushareAPIHandler
        from api.auth import generate_api_token
        from config import API_ENDPOINTS
        
        print("✓ 创建API处理器...")
        handler = TushareAPIHandler()
        
        print("✓ 生成API Token...")
        token = generate_api_token()
        print(f"  示例Token: {token[:20]}...")
        
        print(f"✓ 支持 {len(API_ENDPOINTS)} 个API端点:")
        for endpoint in list(API_ENDPOINTS.keys())[:5]:
            print(f"  • /api/v1/{endpoint}")
        
        if len(API_ENDPOINTS) > 5:
            print(f"  ... 还有 {len(API_ENDPOINTS) - 5} 个端点")
        
        return True
    except Exception as e:
        print(f"✗ API演示失败: {e}")
        return False

def demo_web():
    """演示Web界面"""
    print("\n🌐 Web界面演示")
    print("-" * 40)
    
    try:
        from app import app
        
        print("✓ 创建Flask应用...")
        
        # 检查路由
        routes = []
        for rule in app.url_map.iter_rules():
            if not rule.rule.startswith('/static'):
                routes.append(rule.rule)
        
        print(f"✓ 注册了 {len(routes)} 个路由:")
        main_routes = ['/', '/user', '/docs']
        for route in main_routes:
            if route in routes:
                print(f"  • {route} - 可用")
        
        print("✓ Web界面功能:")
        print("  • 实时数据同步监控")
        print("  • 用户注册和认证")
        print("  • API文档和示例")
        print("  • 数据库状态查看")
        
        return True
    except Exception as e:
        print(f"✗ Web界面演示失败: {e}")
        return False

def start_web_server():
    """启动Web服务器"""
    try:
        from app import app
        print("🌐 启动Web服务器...")
        app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)
    except Exception as e:
        print(f"Web服务器启动失败: {e}")

def show_usage_examples():
    """显示使用示例"""
    print("\n📚 使用示例")
    print("-" * 40)
    
    print("1. 命令行工具:")
    print("   python sync_cli.py init              # 初始化数据库")
    print("   python sync_cli.py sync --table stock_basic  # 同步股票基础信息")
    print("   python sync_cli.py status            # 查看系统状态")
    
    print("\n2. Python API调用:")
    print("""   import requests
   
   # 注册用户
   response = requests.post('http://localhost:5000/auth/register', json={
       "username": "demo_user",
       "email": "<EMAIL>", 
       "password": "demo123456"
   })
   token = response.json()['data']['api_token']
   
   # 获取股票数据
   response = requests.get('http://localhost:5000/api/v1/stock_basic', 
                          params={"token": token, "limit": 10})
   stocks = response.json()['data']['items']""")
    
    print("\n3. Web界面访问:")
    print("   http://localhost:5000      # 主控制台")
    print("   http://localhost:5000/user # 用户管理")
    print("   http://localhost:5000/docs # API文档")

def main():
    """主函数"""
    print_banner()
    
    # 运行演示
    demos = [
        ("数据库系统", demo_database),
        ("数据同步系统", demo_sync),
        ("API系统", demo_api),
        ("Web界面", demo_web),
    ]
    
    success_count = 0
    for demo_name, demo_func in demos:
        if demo_func():
            success_count += 1
        time.sleep(1)
    
    # 显示使用示例
    show_usage_examples()
    
    # 显示结果
    print("\n" + "=" * 80)
    print("🎯 演示结果")
    print("=" * 80)
    print(f"成功演示: {success_count}/{len(demos)} 个模块")
    
    if success_count == len(demos):
        print("\n🎉 所有模块演示成功！")
        print("\n✨ 您的Tushare数据镜像系统已完全就绪！")
        
        # 询问是否启动Web服务器
        print("\n🚀 是否启动Web服务器进行体验？")
        choice = input("输入 'y' 启动，其他键退出: ").lower().strip()
        
        if choice == 'y':
            print("\n🌐 启动Web服务器...")
            app_thread = threading.Thread(target=start_web_server, daemon=True)
            app_thread.start()
            
            time.sleep(2)
            print("\n✅ Web服务器已启动！")
            print("🌟 访问地址:")
            print("  • 主页: http://localhost:5000")
            print("  • 用户管理: http://localhost:5000/user")
            print("  • API文档: http://localhost:5000/docs")
            
            print("\n📝 快速开始:")
            print("  1. 访问用户管理页面注册账户")
            print("  2. 获取API Token")
            print("  3. 使用API或命令行工具同步数据")
            print("  4. 通过API查询数据")
            
            print("\n按 Ctrl+C 停止服务器")
            
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n👋 感谢使用Tushare数据镜像系统！")
        else:
            print("\n👋 演示完成，感谢体验！")
            print("💡 随时运行 'python app.py' 启动Web服务器")
    else:
        print(f"\n⚠ 部分模块演示失败，请检查配置")
    
    return success_count == len(demos)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
