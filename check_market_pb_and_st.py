#!/usr/bin/env python3
"""
检查大盘市净率计算和ST股票识别
"""

import sys
import os
import pandas as pd
import numpy as np

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_market_pb_calculation():
    """检查大盘市净率计算方法"""
    print("🔍 检查大盘市净率计算...")
    
    try:
        from database.models import db_config
        
        session = db_config.get_session()
        
        # 获取最近的交易日
        recent_date_sql = """
            SELECT DISTINCT trade_date FROM daily_basic 
            WHERE trade_date >= '20240101'
            ORDER BY trade_date DESC 
            LIMIT 1
        """
        recent_date = pd.read_sql(recent_date_sql, session.bind)
        
        if len(recent_date) > 0:
            test_date = recent_date.iloc[0]['trade_date']
            print(f"测试日期: {test_date}")
            
            # 获取该日期的所有股票数据
            data_sql = f"""
                SELECT d.ts_code, d.close, b.pb, b.total_mv, s.name, s.industry
                FROM daily d
                INNER JOIN daily_basic b ON d.ts_code = b.ts_code AND d.trade_date = b.trade_date
                LEFT JOIN stock_basic s ON d.ts_code = s.ts_code
                WHERE d.trade_date = '{test_date}'
                AND b.pb > 0 AND b.total_mv > 0
            """
            data = pd.read_sql(data_sql, session.bind)
            print(f"有效数据: {len(data)} 只股票")
            
            if len(data) > 0:
                # 转换数据类型
                data['pb'] = pd.to_numeric(data['pb'], errors='coerce')
                data['total_mv'] = pd.to_numeric(data['total_mv'], errors='coerce')
                data = data.dropna(subset=['pb', 'total_mv'])
                
                print(f"清理后数据: {len(data)} 只股票")
                
                # 方法1: 简单中位数（当前方法）
                simple_median = data['pb'].median()
                print(f"\n📊 方法1 - 简单中位数: {simple_median:.2f}")
                
                # 方法2: 市值加权平均
                total_mv = data['total_mv'].sum()
                weighted_pb = (data['pb'] * data['total_mv']).sum() / total_mv
                print(f"📊 方法2 - 市值加权平均: {weighted_pb:.2f}")
                
                # 方法3: 去除极值后的市值加权平均
                # 去除PB前5%和后5%的极值
                pb_5th = data['pb'].quantile(0.05)
                pb_95th = data['pb'].quantile(0.95)
                filtered_data = data[(data['pb'] >= pb_5th) & (data['pb'] <= pb_95th)]
                
                if len(filtered_data) > 0:
                    filtered_total_mv = filtered_data['total_mv'].sum()
                    filtered_weighted_pb = (filtered_data['pb'] * filtered_data['total_mv']).sum() / filtered_total_mv
                    print(f"📊 方法3 - 去极值市值加权: {filtered_weighted_pb:.2f}")
                    print(f"    去除极值后股票数: {len(filtered_data)}")
                
                # 方法4: 主要指数成分股加权（如果有沪深300数据）
                # 这里简化为选择市值最大的300只股票
                top_300 = data.nlargest(300, 'total_mv')
                if len(top_300) >= 100:
                    top_300_total_mv = top_300['total_mv'].sum()
                    top_300_weighted_pb = (top_300['pb'] * top_300['total_mv']).sum() / top_300_total_mv
                    print(f"📊 方法4 - 市值前300只加权: {top_300_weighted_pb:.2f}")
                
                # 显示PB分布
                print(f"\n📈 PB分布统计:")
                print(f"    最小值: {data['pb'].min():.2f}")
                print(f"    25%分位: {data['pb'].quantile(0.25):.2f}")
                print(f"    中位数: {data['pb'].median():.2f}")
                print(f"    75%分位: {data['pb'].quantile(0.75):.2f}")
                print(f"    最大值: {data['pb'].max():.2f}")
                print(f"    平均值: {data['pb'].mean():.2f}")
                
                # 推荐使用的方法
                print(f"\n💡 推荐使用方法3 (去极值市值加权): {filtered_weighted_pb:.2f}")
                
                return filtered_weighted_pb
        
        session.close()
        
    except Exception as e:
        print(f"❌ 大盘市净率检查失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def check_st_stock_identification():
    """检查ST股票识别方法"""
    print("\n🔍 检查ST股票识别...")
    
    try:
        from database.models import db_config
        
        session = db_config.get_session()
        
        # 检查股票名称中包含ST的情况
        st_check_sql = """
            SELECT ts_code, name, industry, list_status, list_date
            FROM stock_basic 
            WHERE name LIKE '%ST%' OR name LIKE '%退市%'
            ORDER BY name
        """
        st_stocks = pd.read_sql(st_check_sql, session.bind)
        print(f"当前名称包含ST的股票: {len(st_stocks)} 只")
        
        if len(st_stocks) > 0:
            print("ST股票样本:")
            print(st_stocks.head(10)[['ts_code', 'name', 'industry']].to_string())
        
        # 检查是否有曾用名表
        try:
            namechange_sql = "SELECT name FROM sqlite_master WHERE type='table' AND name='namechange'"
            namechange_exists = pd.read_sql(namechange_sql, session.bind)
            
            if len(namechange_exists) > 0:
                print(f"\n✅ 发现namechange表，检查曾用名...")
                
                # 检查曾用名中的ST股票
                namechange_st_sql = """
                    SELECT DISTINCT n.ts_code, s.name as current_name, n.name as old_name, n.start_date, n.end_date
                    FROM namechange n
                    LEFT JOIN stock_basic s ON n.ts_code = s.ts_code
                    WHERE n.name LIKE '%ST%' OR n.name LIKE '%退市%'
                    ORDER BY n.ts_code, n.start_date
                """
                namechange_st = pd.read_sql(namechange_st_sql, session.bind)
                print(f"曾用名包含ST的股票: {len(namechange_st)} 只")
                
                if len(namechange_st) > 0:
                    print("曾用名ST股票样本:")
                    print(namechange_st.head(10).to_string())
                    
                    # 统计曾经是ST但现在不是的股票
                    current_st_codes = set(st_stocks['ts_code'].tolist())
                    historical_st_codes = set(namechange_st['ts_code'].tolist())
                    recovered_st_codes = historical_st_codes - current_st_codes
                    
                    print(f"\n📊 ST股票统计:")
                    print(f"    当前ST股票: {len(current_st_codes)} 只")
                    print(f"    历史ST股票: {len(historical_st_codes)} 只")
                    print(f"    已摘帽ST股票: {len(recovered_st_codes)} 只")
                    
                    if len(recovered_st_codes) > 0:
                        recovered_info_sql = f"""
                            SELECT ts_code, name, industry
                            FROM stock_basic 
                            WHERE ts_code IN ('{"','".join(list(recovered_st_codes)[:10])}')
                        """
                        recovered_info = pd.read_sql(recovered_info_sql, session.bind)
                        print("已摘帽ST股票样本:")
                        print(recovered_info.to_string())
                
            else:
                print(f"\n⚠️  未发现namechange表，无法检查曾用名")
                
        except Exception as e:
            print(f"⚠️  检查namechange表失败: {e}")
        
        # 检查其他风险股票标识
        risk_patterns = ['退市', '暂停', '终止', '*ST', 'ST', 'PT']
        
        print(f"\n🔍 检查其他风险股票标识...")
        for pattern in risk_patterns:
            pattern_sql = f"""
                SELECT COUNT(*) as count
                FROM stock_basic 
                WHERE name LIKE '%{pattern}%'
            """
            count = pd.read_sql(pattern_sql, session.bind).iloc[0]['count']
            print(f"    包含'{pattern}'的股票: {count} 只")
        
        session.close()
        
        return True
        
    except Exception as e:
        print(f"❌ ST股票识别检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def suggest_improvements():
    """建议改进方案"""
    print("\n💡 改进建议:")
    
    print("\n🎯 大盘市净率计算改进:")
    print("1. 使用市值加权平均，而不是简单中位数")
    print("2. 去除PB极值（前5%和后5%）")
    print("3. 优先使用主要指数成分股（如沪深300）")
    print("4. 考虑行业分布，避免某个行业权重过大")
    
    print("\n🎯 ST股票识别改进:")
    print("1. 检查当前股票名称是否包含ST、*ST、退市等")
    print("2. 如果有namechange表，检查历史曾用名")
    print("3. 考虑已摘帽但风险仍较高的股票")
    print("4. 添加其他风险标识：PT、暂停上市等")
    print("5. 考虑财务指标筛选：连续亏损、资不抵债等")

def main():
    """主函数"""
    print("🔍 大盘市净率和ST股票识别检查")
    print("=" * 50)
    
    # 检查1: 大盘市净率计算
    market_pb = check_market_pb_calculation()
    
    # 检查2: ST股票识别
    check_st_stock_identification()
    
    # 建议改进
    suggest_improvements()
    
    print("\n🎉 检查完成！")
    print("💡 建议根据上述分析改进策略算法")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户中断检查")
    except Exception as e:
        print(f"\n❌ 检查异常: {e}")
        import traceback
        traceback.print_exc()
