#!/usr/bin/env python3
"""
调试数据问题
"""

import sys
import os
import pandas as pd

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_database():
    """调试数据库问题"""
    print("🔍 调试数据库问题...")
    
    try:
        from database.models import db_config
        from sqlalchemy import text
        
        session = db_config.get_session()
        
        # 1. 检查表是否存在
        print("\n📊 检查表结构...")
        tables = ['stock_basic', 'daily', 'daily_basic', 'trade_cal']
        
        for table in tables:
            try:
                result = session.execute(text(f"SELECT COUNT(*) FROM {table}"))
                count = result.scalar()
                print(f"  ✅ {table}: {count:,} 条记录")
            except Exception as e:
                print(f"  ❌ {table}: 错误 - {e}")
        
        # 2. 检查stock_basic表的详细信息
        print("\n📊 检查stock_basic表...")
        try:
            # 检查字段
            result = session.execute(text("PRAGMA table_info(stock_basic)"))
            columns = result.fetchall()
            print("  字段列表:")
            for col in columns:
                print(f"    {col[1]} ({col[2]})")
            
            # 检查数据样本
            sample = pd.read_sql("SELECT * FROM stock_basic LIMIT 5", session.bind)
            print(f"\n  样本数据 ({len(sample)} 条):")
            if len(sample) > 0:
                print(sample[['ts_code', 'name', 'industry', 'list_status']].to_string())
            else:
                print("    无数据")
                
        except Exception as e:
            print(f"  ❌ stock_basic表错误: {e}")
        
        # 3. 检查daily_basic表的详细信息
        print("\n📊 检查daily_basic表...")
        try:
            # 检查字段
            result = session.execute(text("PRAGMA table_info(daily_basic)"))
            columns = result.fetchall()
            print("  字段列表:")
            for col in columns:
                print(f"    {col[1]} ({col[2]})")
            
            # 检查数据样本
            sample = pd.read_sql("SELECT * FROM daily_basic LIMIT 5", session.bind)
            print(f"\n  样本数据 ({len(sample)} 条):")
            if len(sample) > 0:
                print("  字段:", sample.columns.tolist())
                print("  数据类型:")
                for col in ['ts_code', 'trade_date', 'pe_ttm', 'pb']:
                    if col in sample.columns:
                        print(f"    {col}: {sample[col].dtype}")
                        print(f"    样本值: {sample[col].head(3).tolist()}")
            else:
                print("    无数据")
                
        except Exception as e:
            print(f"  ❌ daily_basic表错误: {e}")
        
        # 4. 检查数据合并问题
        print("\n📊 检查数据合并...")
        try:
            # 获取最近的交易日
            trade_date_sql = """
                SELECT cal_date FROM trade_cal 
                WHERE is_open = 1 
                ORDER BY cal_date DESC 
                LIMIT 1
            """
            latest_date = pd.read_sql(trade_date_sql, session.bind)
            if len(latest_date) > 0:
                test_date = latest_date.iloc[0]['cal_date']
                print(f"  测试日期: {test_date}")
                
                # 检查该日期的数据
                daily_count = pd.read_sql(
                    f"SELECT COUNT(*) as cnt FROM daily WHERE trade_date = '{test_date}'", 
                    session.bind
                ).iloc[0]['cnt']
                
                basic_count = pd.read_sql(
                    f"SELECT COUNT(*) as cnt FROM daily_basic WHERE trade_date = '{test_date}'", 
                    session.bind
                ).iloc[0]['cnt']
                
                print(f"  该日期daily数据: {daily_count} 条")
                print(f"  该日期daily_basic数据: {basic_count} 条")
                
                # 测试合并
                if daily_count > 0 and basic_count > 0:
                    merge_sql = f"""
                        SELECT d.ts_code, d.trade_date, d.close, b.pe_ttm, b.pb
                        FROM daily d
                        INNER JOIN daily_basic b ON d.ts_code = b.ts_code AND d.trade_date = b.trade_date
                        WHERE d.trade_date = '{test_date}'
                        LIMIT 5
                    """
                    merged = pd.read_sql(merge_sql, session.bind)
                    print(f"  合并结果: {len(merged)} 条")
                    if len(merged) > 0:
                        print("  合并样本:")
                        print(merged.to_string())
                else:
                    print("  ⚠️  该日期数据不足，无法测试合并")
            else:
                print("  ❌ 没有找到交易日数据")
                
        except Exception as e:
            print(f"  ❌ 数据合并测试错误: {e}")
        
        session.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_strategy_data_loading():
    """调试策略数据加载"""
    print("\n🔍 调试策略数据加载...")
    
    try:
        from database.models import db_config
        import pandas as pd
        
        session = db_config.get_session()
        
        # 模拟策略的数据加载过程
        print("  📋 加载股票基础信息...")
        stock_basic_sql = "SELECT * FROM stock_basic"
        stock_basic = pd.read_sql(stock_basic_sql, session.bind)
        print(f"    原始数据: {len(stock_basic)} 条")
        
        if len(stock_basic) > 0:
            print("    字段:", stock_basic.columns.tolist())
            
            # 检查list_status字段
            if 'list_status' in stock_basic.columns:
                status_counts = stock_basic['list_status'].value_counts()
                print("    上市状态分布:")
                for status, count in status_counts.items():
                    print(f"      {status}: {count} 只")
                
                # 过滤上市股票
                listed_stocks = stock_basic[stock_basic['list_status'] == 'L']
                print(f"    过滤后上市股票: {len(listed_stocks)} 只")
            else:
                print("    ⚠️  没有list_status字段")
        
        # 检查日线数据
        print("\n  📈 加载日线数据...")
        daily_sql = "SELECT * FROM daily LIMIT 10"
        daily_data = pd.read_sql(daily_sql, session.bind)
        print(f"    样本数据: {len(daily_data)} 条")
        if len(daily_data) > 0:
            print("    字段:", daily_data.columns.tolist())
            print("    日期范围:", daily_data['trade_date'].min(), "-", daily_data['trade_date'].max())
        
        # 检查基本面数据
        print("\n  📊 加载基本面数据...")
        basic_sql = "SELECT * FROM daily_basic LIMIT 10"
        basic_data = pd.read_sql(basic_sql, session.bind)
        print(f"    样本数据: {len(basic_data)} 条")
        if len(basic_data) > 0:
            print("    字段:", basic_data.columns.tolist())
            print("    日期范围:", basic_data['trade_date'].min(), "-", basic_data['trade_date'].max())
            
            # 检查PE、PB数据
            pe_valid = basic_data['pe_ttm'].notna().sum()
            pb_valid = basic_data['pb'].notna().sum()
            print(f"    有效PE数据: {pe_valid}/{len(basic_data)}")
            print(f"    有效PB数据: {pb_valid}/{len(basic_data)}")
        
        session.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 策略数据加载调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 数据调试工具")
    print("=" * 40)
    
    # 调试1: 数据库基本信息
    if not debug_database():
        print("❌ 数据库调试失败")
        return
    
    # 调试2: 策略数据加载
    if not debug_strategy_data_loading():
        print("❌ 策略数据加载调试失败")
        return
    
    print("\n🎉 调试完成！")
    print("💡 请检查上述输出，找出数据问题的根源")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户中断调试")
    except Exception as e:
        print(f"\n❌ 调试异常: {e}")
        import traceback
        traceback.print_exc()
