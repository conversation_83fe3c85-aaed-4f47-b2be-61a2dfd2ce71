{"database_file": {"path": "data\\tushare_mirror.db", "exists": true, "size_mb": 46.6328125}, "tables": {"stock_basic": {"columns": 17, "records": 5419, "column_details": [["ts_code", "VARCHAR(20)"], ["symbol", "VARCHAR(10)"], ["name", "VARCHAR(20)"], ["area", "VARCHAR(20)"], ["industry", "VARCHAR(50)"], ["fullname", "VARCHAR(100)"], ["enname", "VARCHAR(200)"], ["cnspell", "VARCHAR(50)"], ["market", "VARCHAR(10)"], ["exchange", "VARCHAR(10)"], ["curr_type", "VARCHAR(10)"], ["list_status", "VARCHAR(1)"], ["list_date", "VARCHAR(8)"], ["delist_date", "VARCHAR(8)"], ["is_hs", "VARCHAR(1)"], ["act_name", "VARCHAR(100)"], ["act_ent_type", "VARCHAR(100)"]]}, "trade_cal": {"columns": 4, "records": 12797, "column_details": [["exchange", "VARCHAR(10)"], ["cal_date", "VARCHAR(8)"], ["is_open", "INTEGER"], ["pretrade_date", "VARCHAR(8)"]]}, "daily": {"columns": 11, "records": 106362, "column_details": [["ts_code", "VARCHAR(20)"], ["trade_date", "VARCHAR(8)"], ["open", "FLOAT"], ["high", "FLOAT"], ["low", "FLOAT"], ["close", "FLOAT"], ["pre_close", "FLOAT"], ["change", "FLOAT"], ["pct_chg", "FLOAT"], ["vol", "FLOAT"], ["amount", "FLOAT"]]}, "daily_basic": {"columns": 18, "records": 101000, "column_details": [["ts_code", "VARCHAR(20)"], ["trade_date", "VARCHAR(8)"], ["close", "FLOAT"], ["turnover_rate", "FLOAT"], ["turnover_rate_f", "FLOAT"], ["volume_ratio", "FLOAT"], ["pe", "FLOAT"], ["pe_ttm", "FLOAT"], ["pb", "FLOAT"], ["ps", "FLOAT"], ["ps_ttm", "FLOAT"], ["dv_ratio", "FLOAT"], ["dv_ttm", "FLOAT"], ["total_share", "FLOAT"], ["float_share", "FLOAT"], ["free_share", "FLOAT"], ["total_mv", "FLOAT"], ["circ_mv", "FLOAT"]]}, "income": {"columns": 30, "records": 0, "column_details": [["ts_code", "VARCHAR(20)"], ["ann_date", "VARCHAR(8)"], ["f_ann_date", "VARCHAR(8)"], ["end_date", "VARCHAR(8)"], ["report_type", "VARCHAR(20)"], ["comp_type", "VARCHAR(20)"], ["basic_eps", "FLOAT"], ["diluted_eps", "FLOAT"], ["total_revenue", "FLOAT"], ["revenue", "FLOAT"], ["int_income", "FLOAT"], ["prem_earned", "FLOAT"], ["comm_income", "FLOAT"], ["n_commis_income", "FLOAT"], ["n_oth_income", "FLOAT"], ["n_oth_b_income", "FLOAT"], ["prem_income", "FLOAT"], ["out_prem", "FLOAT"], ["une_prem_reser", "FLOAT"], ["reins_income", "FLOAT"], ["n_sec_tb_income", "FLOAT"], ["n_sec_uw_income", "FLOAT"], ["n_asset_mg_income", "FLOAT"], ["oth_b_income", "FLOAT"], ["fv_value_chg_gain", "FLOAT"], ["invest_income", "FLOAT"], ["ass_invest_income", "FLOAT"], ["forex_gain", "FLOAT"], ["total_cogs", "FLOAT"], ["oper_cost", "FLOAT"]]}, "balancesheet": {"columns": 30, "records": 0, "column_details": [["ts_code", "VARCHAR(20)"], ["ann_date", "VARCHAR(8)"], ["f_ann_date", "VARCHAR(8)"], ["end_date", "VARCHAR(8)"], ["report_type", "VARCHAR(20)"], ["comp_type", "VARCHAR(20)"], ["total_share", "FLOAT"], ["cap_rese", "FLOAT"], ["undistr_porfit", "FLOAT"], ["surplus_rese", "FLOAT"], ["special_rese", "FLOAT"], ["money_cap", "FLOAT"], ["trad_asset", "FLOAT"], ["notes_receiv", "FLOAT"], ["accounts_receiv", "FLOAT"], ["oth_receiv", "FLOAT"], ["prepayment", "FLOAT"], ["div_receiv", "FLOAT"], ["int_receiv", "FLOAT"], ["inventories", "FLOAT"], ["amor_exp", "FLOAT"], ["nca_within_1y", "FLOAT"], ["sett_rsrv", "FLOAT"], ["loanto_oth_bank_fi", "FLOAT"], ["premium_receiv", "FLOAT"], ["reinsur_receiv", "FLOAT"], ["reinsur_res_receiv", "FLOAT"], ["pur_resale_fa", "FLOAT"], ["oth_cur_assets", "FLOAT"], ["total_cur_assets", "FLOAT"]]}, "index_basic": {"columns": 13, "records": 11945, "column_details": [["ts_code", "VARCHAR(20)"], ["name", "VARCHAR(100)"], ["fullname", "VARCHAR(200)"], ["market", "VARCHAR(20)"], ["publisher", "VARCHAR(100)"], ["index_type", "VARCHAR(20)"], ["category", "VARCHAR(20)"], ["base_date", "VARCHAR(8)"], ["base_point", "FLOAT"], ["list_date", "VARCHAR(8)"], ["weight_rule", "VARCHAR(100)"], ["desc", "TEXT"], ["exp_date", "VARCHAR(8)"]]}, "index_daily": {"columns": 11, "records": 0, "column_details": [["ts_code", "VARCHAR(20)"], ["trade_date", "VARCHAR(8)"], ["close", "FLOAT"], ["open", "FLOAT"], ["high", "FLOAT"], ["low", "FLOAT"], ["pre_close", "FLOAT"], ["change", "FLOAT"], ["pct_chg", "FLOAT"], ["vol", "FLOAT"], ["amount", "FLOAT"]]}, "sync_status": {"columns": 7, "records": 8, "column_details": [["id", "INTEGER"], ["table_name", "VARCHAR(50)"], ["last_sync_date", "VARCHAR(8)"], ["last_sync_time", "DATETIME"], ["sync_status", "VARCHAR(20)"], ["error_message", "TEXT"], ["record_count", "INTEGER"]]}, "users": {"columns": 11, "records": 2, "column_details": [["id", "INTEGER"], ["username", "VARCHAR(50)"], ["email", "VARCHAR(100)"], ["password_hash", "VARCHAR(255)"], ["api_token", "VARCHAR(100)"], ["is_active", "BOOLEAN"], ["is_premium", "BOOLEAN"], ["created_at", "DATETIME"], ["last_login", "DATETIME"], ["api_calls_today", "INTEGER"], ["api_limit_daily", "INTEGER"]]}}, "tushare_config": {"stock_basic": {"name": "股票基础信息", "api_name": "stock_basic", "primary_key": ["ts_code"], "date_field": null, "sync_type": "full", "sync_frequency": "weekly"}, "trade_cal": {"name": "交易日历", "api_name": "trade_cal", "primary_key": ["exchange", "cal_date"], "date_field": "cal_date", "sync_type": "incremental", "sync_frequency": "daily"}, "daily": {"name": "日线行情", "api_name": "daily", "primary_key": ["ts_code", "trade_date"], "date_field": "trade_date", "sync_type": "incremental", "sync_frequency": "daily"}, "daily_basic": {"name": "每日指标", "api_name": "daily_basic", "primary_key": ["ts_code", "trade_date"], "date_field": "trade_date", "sync_type": "incremental", "sync_frequency": "daily"}, "income": {"name": "利润表", "api_name": "income", "primary_key": ["ts_code", "ann_date", "f_ann_date"], "date_field": "ann_date", "sync_type": "incremental", "sync_frequency": "daily"}, "balancesheet": {"name": "资产负债表", "api_name": "balancesheet", "primary_key": ["ts_code", "ann_date", "f_ann_date"], "date_field": "ann_date", "sync_type": "incremental", "sync_frequency": "daily"}, "index_basic": {"name": "指数基础信息", "api_name": "index_basic", "primary_key": ["ts_code"], "date_field": null, "sync_type": "full", "sync_frequency": "weekly"}, "index_daily": {"name": "指数日线行情", "api_name": "index_daily", "primary_key": ["ts_code", "trade_date"], "date_field": "trade_date", "sync_type": "incremental", "sync_frequency": "daily"}}, "summary": {"total_tables": 10, "total_records": 237533, "configured_tables": 8}}