"""
用户认证和管理系统
"""

from flask import Blueprint, request, jsonify, session
from werkzeug.security import generate_password_hash, check_password_hash
import secrets
import string
from datetime import datetime, timedelta
import re
from typing import Optional

from database.models import db_config, User
from config import get_config

# 创建认证蓝图
auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

# 配置
config = get_config()

def generate_api_token() -> str:
    """生成API token"""
    # 生成32位随机字符串
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(32))

def validate_email(email: str) -> bool:
    """验证邮箱格式"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_password(password: str) -> tuple:
    """验证密码强度"""
    if len(password) < 6:
        return False, "密码长度至少6位"
    
    if len(password) > 50:
        return False, "密码长度不能超过50位"
    
    # 检查是否包含字母和数字
    has_letter = any(c.isalpha() for c in password)
    has_digit = any(c.isdigit() for c in password)
    
    if not (has_letter and has_digit):
        return False, "密码必须包含字母和数字"
    
    return True, "密码格式正确"

@auth_bp.route('/register', methods=['POST'])
def register():
    """用户注册"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'code': -1,
                'msg': '请求数据为空',
                'data': None
            }), 400
        
        username = data.get('username', '').strip()
        email = data.get('email', '').strip()
        password = data.get('password', '')
        
        # 验证输入
        if not username or not email or not password:
            return jsonify({
                'code': -1,
                'msg': '用户名、邮箱和密码不能为空',
                'data': None
            }), 400
        
        # 验证用户名
        if len(username) < 3 or len(username) > 20:
            return jsonify({
                'code': -1,
                'msg': '用户名长度必须在3-20位之间',
                'data': None
            }), 400
        
        if not re.match(r'^[a-zA-Z0-9_]+$', username):
            return jsonify({
                'code': -1,
                'msg': '用户名只能包含字母、数字和下划线',
                'data': None
            }), 400
        
        # 验证邮箱
        if not validate_email(email):
            return jsonify({
                'code': -1,
                'msg': '邮箱格式不正确',
                'data': None
            }), 400
        
        # 验证密码
        is_valid, msg = validate_password(password)
        if not is_valid:
            return jsonify({
                'code': -1,
                'msg': msg,
                'data': None
            }), 400
        
        session = db_config.get_session()
        
        try:
            # 检查用户名是否已存在
            existing_user = session.query(User).filter_by(username=username).first()
            if existing_user:
                return jsonify({
                    'code': -1,
                    'msg': '用户名已存在',
                    'data': None
                }), 400
            
            # 检查邮箱是否已存在
            existing_email = session.query(User).filter_by(email=email).first()
            if existing_email:
                return jsonify({
                    'code': -1,
                    'msg': '邮箱已被注册',
                    'data': None
                }), 400
            
            # 创建新用户
            password_hash = generate_password_hash(password)
            api_token = generate_api_token()
            
            new_user = User(
                username=username,
                email=email,
                password_hash=password_hash,
                api_token=api_token,
                is_active=True,
                is_premium=False,
                created_at=datetime.now(),
                api_calls_today=0,
                api_limit_daily=config.API_RATE_LIMIT['free_user']
            )
            
            session.add(new_user)
            session.commit()
            
            return jsonify({
                'code': 0,
                'msg': '注册成功',
                'data': {
                    'user_id': new_user.id,
                    'username': new_user.username,
                    'email': new_user.email,
                    'api_token': new_user.api_token,
                    'is_premium': new_user.is_premium,
                    'api_limit_daily': new_user.api_limit_daily
                }
            })
            
        except Exception as e:
            session.rollback()
            return jsonify({
                'code': -1,
                'msg': f'注册失败: {str(e)}',
                'data': None
            }), 500
        finally:
            session.close()
            
    except Exception as e:
        return jsonify({
            'code': -1,
            'msg': f'服务器错误: {str(e)}',
            'data': None
        }), 500

@auth_bp.route('/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'code': -1,
                'msg': '请求数据为空',
                'data': None
            }), 400
        
        username = data.get('username', '').strip()
        password = data.get('password', '')
        
        if not username or not password:
            return jsonify({
                'code': -1,
                'msg': '用户名和密码不能为空',
                'data': None
            }), 400
        
        session_db = db_config.get_session()
        
        try:
            # 查找用户（支持用户名或邮箱登录）
            user = session_db.query(User).filter(
                (User.username == username) | (User.email == username)
            ).first()
            
            if not user:
                return jsonify({
                    'code': -1,
                    'msg': '用户不存在',
                    'data': None
                }), 401
            
            if not user.is_active:
                return jsonify({
                    'code': -1,
                    'msg': '账户已被禁用',
                    'data': None
                }), 401
            
            # 验证密码
            if not check_password_hash(user.password_hash, password):
                return jsonify({
                    'code': -1,
                    'msg': '密码错误',
                    'data': None
                }), 401
            
            # 更新最后登录时间
            user.last_login = datetime.now()
            
            # 重置每日API调用计数（如果是新的一天）
            today = datetime.now().date()
            if user.last_login and user.last_login.date() != today:
                user.api_calls_today = 0
            
            session_db.commit()
            
            # 设置会话
            session['user_id'] = user.id
            session['username'] = user.username
            
            return jsonify({
                'code': 0,
                'msg': '登录成功',
                'data': {
                    'user_id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'api_token': user.api_token,
                    'is_premium': user.is_premium,
                    'api_limit_daily': user.api_limit_daily,
                    'api_calls_today': user.api_calls_today
                }
            })
            
        except Exception as e:
            session_db.rollback()
            return jsonify({
                'code': -1,
                'msg': f'登录失败: {str(e)}',
                'data': None
            }), 500
        finally:
            session_db.close()
            
    except Exception as e:
        return jsonify({
            'code': -1,
            'msg': f'服务器错误: {str(e)}',
            'data': None
        }), 500

@auth_bp.route('/logout', methods=['POST'])
def logout():
    """用户登出"""
    session.clear()
    return jsonify({
        'code': 0,
        'msg': '登出成功',
        'data': None
    })

@auth_bp.route('/profile', methods=['GET'])
def profile():
    """获取用户信息"""
    try:
        # 从session或token获取用户信息
        user_id = session.get('user_id')
        token = request.headers.get('Authorization') or request.args.get('token')
        
        session_db = db_config.get_session()
        
        try:
            user = None
            
            if user_id:
                user = session_db.query(User).filter_by(id=user_id).first()
            elif token:
                user = session_db.query(User).filter_by(api_token=token).first()
            
            if not user:
                return jsonify({
                    'code': -1,
                    'msg': '用户未登录或token无效',
                    'data': None
                }), 401
            
            return jsonify({
                'code': 0,
                'msg': 'success',
                'data': {
                    'user_id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'is_premium': user.is_premium,
                    'api_limit_daily': user.api_limit_daily,
                    'api_calls_today': user.api_calls_today,
                    'created_at': user.created_at.isoformat() if user.created_at else None,
                    'last_login': user.last_login.isoformat() if user.last_login else None
                }
            })
            
        finally:
            session_db.close()
            
    except Exception as e:
        return jsonify({
            'code': -1,
            'msg': f'服务器错误: {str(e)}',
            'data': None
        }), 500

@auth_bp.route('/reset_token', methods=['POST'])
def reset_token():
    """重置API token"""
    try:
        user_id = session.get('user_id')
        
        if not user_id:
            return jsonify({
                'code': -1,
                'msg': '用户未登录',
                'data': None
            }), 401
        
        session_db = db_config.get_session()
        
        try:
            user = session_db.query(User).filter_by(id=user_id).first()
            
            if not user:
                return jsonify({
                    'code': -1,
                    'msg': '用户不存在',
                    'data': None
                }), 404
            
            # 生成新的API token
            user.api_token = generate_api_token()
            session_db.commit()
            
            return jsonify({
                'code': 0,
                'msg': 'Token重置成功',
                'data': {
                    'api_token': user.api_token
                }
            })
            
        except Exception as e:
            session_db.rollback()
            return jsonify({
                'code': -1,
                'msg': f'Token重置失败: {str(e)}',
                'data': None
            }), 500
        finally:
            session_db.close()
            
    except Exception as e:
        return jsonify({
            'code': -1,
            'msg': f'服务器错误: {str(e)}',
            'data': None
        }), 500

@auth_bp.route('/change_password', methods=['POST'])
def change_password():
    """修改密码"""
    try:
        user_id = session.get('user_id')
        
        if not user_id:
            return jsonify({
                'code': -1,
                'msg': '用户未登录',
                'data': None
            }), 401
        
        data = request.get_json()
        
        if not data:
            return jsonify({
                'code': -1,
                'msg': '请求数据为空',
                'data': None
            }), 400
        
        old_password = data.get('old_password', '')
        new_password = data.get('new_password', '')
        
        if not old_password or not new_password:
            return jsonify({
                'code': -1,
                'msg': '旧密码和新密码不能为空',
                'data': None
            }), 400
        
        # 验证新密码
        is_valid, msg = validate_password(new_password)
        if not is_valid:
            return jsonify({
                'code': -1,
                'msg': msg,
                'data': None
            }), 400
        
        session_db = db_config.get_session()
        
        try:
            user = session_db.query(User).filter_by(id=user_id).first()
            
            if not user:
                return jsonify({
                    'code': -1,
                    'msg': '用户不存在',
                    'data': None
                }), 404
            
            # 验证旧密码
            if not check_password_hash(user.password_hash, old_password):
                return jsonify({
                    'code': -1,
                    'msg': '旧密码错误',
                    'data': None
                }), 401
            
            # 更新密码
            user.password_hash = generate_password_hash(new_password)
            session_db.commit()
            
            return jsonify({
                'code': 0,
                'msg': '密码修改成功',
                'data': None
            })
            
        except Exception as e:
            session_db.rollback()
            return jsonify({
                'code': -1,
                'msg': f'密码修改失败: {str(e)}',
                'data': None
            }), 500
        finally:
            session_db.close()
            
    except Exception as e:
        return jsonify({
            'code': -1,
            'msg': f'服务器错误: {str(e)}',
            'data': None
        }), 500
