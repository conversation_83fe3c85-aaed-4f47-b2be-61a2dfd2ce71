"""
项目配置文件
包含所有系统配置参数
"""

import os
from datetime import timedelta

class Config:
    """基础配置类"""
    
    # 基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-change-in-production'
    
    # Tushare配置
    TUSHARE_TOKEN = '2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211'
    
    # 数据库配置
    DATABASE_URL = os.environ.get('DATABASE_URL') or 'sqlite:///data/tushare_mirror.db'
    
    # Redis配置（用于缓存和任务队列）
    REDIS_URL = os.environ.get('REDIS_URL') or 'redis://localhost:6379/0'
    
    # API配置
    API_RATE_LIMIT = {
        'free_user': 1000,      # 免费用户每日调用限制
        'premium_user': 10000,  # 高级用户每日调用限制
        'per_minute': 60        # 每分钟调用限制
    }
    
    # 数据同步配置
    SYNC_CONFIG = {
        'batch_size': 1000,           # 每批次同步记录数
        'max_offset': 99000,          # 最大offset限制
        'request_delay': 0.3,         # 请求间隔（秒）
        'segment_delay': 1,           # 分段间隔（秒）
        'days_per_chunk': 30,         # 每段天数
        'max_retries': 3,             # 最大重试次数
        'retry_delay': 5,             # 重试间隔（秒）
    }
    
    # 定时任务配置
    SCHEDULER_CONFIG = {
        'daily_sync_time': '06:00',   # 每日同步时间
        'weekly_full_sync': 'sunday', # 每周全量同步日
        'cleanup_days': 30,           # 日志清理天数
    }
    
    # 日志配置
    LOG_CONFIG = {
        'level': 'INFO',
        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'file': 'logs/tushare_mirror.log',
        'max_size': 10 * 1024 * 1024,  # 10MB
        'backup_count': 5,
    }
    
    # Web界面配置
    WEB_CONFIG = {
        'host': '0.0.0.0',
        'port': 5000,
        'debug': False,
        'page_size': 50,              # 默认分页大小
        'max_page_size': 1000,        # 最大分页大小
    }
    
    # 文件存储配置
    STORAGE_CONFIG = {
        'data_dir': 'data',
        'export_dir': 'exports',
        'log_dir': 'logs',
        'backup_dir': 'backups',
    }

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    WEB_CONFIG = Config.WEB_CONFIG.copy()
    WEB_CONFIG['debug'] = True
    LOG_CONFIG = Config.LOG_CONFIG.copy()
    LOG_CONFIG['level'] = 'DEBUG'

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'production-secret-key'
    DATABASE_URL = os.environ.get('DATABASE_URL') or 'postgresql://user:pass@localhost/tushare_mirror'
    
    # 生产环境更严格的限制
    API_RATE_LIMIT = {
        'free_user': 500,
        'premium_user': 5000,
        'per_minute': 30
    }

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    DATABASE_URL = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False

# 配置映射
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

# Tushare数据表配置
TUSHARE_TABLES = {
    # 基础数据
    'stock_basic': {
        'name': '股票基础信息',
        'api_name': 'stock_basic',
        'primary_key': ['ts_code'],
        'date_field': None,
        'sync_type': 'full',  # full: 全量同步, incremental: 增量同步
        'sync_frequency': 'weekly',  # daily, weekly, monthly
    },
    'trade_cal': {
        'name': '交易日历',
        'api_name': 'trade_cal',
        'primary_key': ['exchange', 'cal_date'],
        'date_field': 'cal_date',
        'sync_type': 'incremental',
        'sync_frequency': 'daily',
    },
    
    # 行情数据
    'daily': {
        'name': '日线行情',
        'api_name': 'daily',
        'primary_key': ['ts_code', 'trade_date'],
        'date_field': 'trade_date',
        'sync_type': 'incremental',
        'sync_frequency': 'daily',
    },
    'daily_basic': {
        'name': '每日指标',
        'api_name': 'daily_basic',
        'primary_key': ['ts_code', 'trade_date'],
        'date_field': 'trade_date',
        'sync_type': 'incremental',
        'sync_frequency': 'daily',
    },
    
    # 财务数据
    'income': {
        'name': '利润表',
        'api_name': 'income',
        'primary_key': ['ts_code', 'ann_date', 'f_ann_date'],
        'date_field': 'ann_date',
        'sync_type': 'incremental',
        'sync_frequency': 'daily',
    },
    'balancesheet': {
        'name': '资产负债表',
        'api_name': 'balancesheet',
        'primary_key': ['ts_code', 'ann_date', 'f_ann_date'],
        'date_field': 'ann_date',
        'sync_type': 'incremental',
        'sync_frequency': 'daily',
    },
    
    # 指数数据
    'index_basic': {
        'name': '指数基础信息',
        'api_name': 'index_basic',
        'primary_key': ['ts_code'],
        'date_field': None,
        'sync_type': 'full',
        'sync_frequency': 'weekly',
    },
    'index_daily': {
        'name': '指数日线行情',
        'api_name': 'index_daily',
        'primary_key': ['ts_code', 'trade_date'],
        'date_field': 'trade_date',
        'sync_type': 'incremental',
        'sync_frequency': 'daily',
    },
}

# API接口映射（与Tushare保持一致）
API_ENDPOINTS = {
    # 基础数据
    'stock_basic': {
        'table': 'stock_basic',
        'required_params': [],
        'optional_params': ['is_hs', 'list_status', 'exchange', 'market'],
    },
    'trade_cal': {
        'table': 'trade_cal',
        'required_params': [],
        'optional_params': ['exchange', 'start_date', 'end_date', 'is_open'],
    },
    
    # 行情数据
    'daily': {
        'table': 'daily',
        'required_params': [],
        'optional_params': ['ts_code', 'trade_date', 'start_date', 'end_date'],
    },
    'daily_basic': {
        'table': 'daily_basic',
        'required_params': [],
        'optional_params': ['ts_code', 'trade_date', 'start_date', 'end_date'],
    },
    
    # 财务数据
    'income': {
        'table': 'income',
        'required_params': [],
        'optional_params': ['ts_code', 'ann_date', 'start_date', 'end_date', 'period', 'report_type'],
    },
    'balancesheet': {
        'table': 'balancesheet',
        'required_params': [],
        'optional_params': ['ts_code', 'ann_date', 'start_date', 'end_date', 'period', 'report_type'],
    },
    
    # 指数数据
    'index_basic': {
        'table': 'index_basic',
        'required_params': [],
        'optional_params': ['market', 'publisher', 'category'],
    },
    'index_daily': {
        'table': 'index_daily',
        'required_params': [],
        'optional_params': ['ts_code', 'trade_date', 'start_date', 'end_date'],
    },
}

def get_config():
    """获取当前配置"""
    env = os.environ.get('FLASK_ENV', 'development')
    return config.get(env, config['default'])

def ensure_directories():
    """确保必要的目录存在"""
    current_config = get_config()
    
    for dir_key, dir_path in current_config.STORAGE_CONFIG.items():
        if not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)
            print(f"创建目录: {dir_path}")

if __name__ == "__main__":
    # 创建必要的目录
    ensure_directories()
    print("配置初始化完成")
