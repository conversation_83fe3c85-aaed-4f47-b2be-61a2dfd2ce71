#!/usr/bin/env python3
"""
快速测试修复后的策略
"""

import sys
import os
import pandas as pd

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def quick_test():
    """快速测试"""
    print("🧪 快速测试修复后的策略...")
    
    try:
        from strategy_backtest import StrategyBacktest
        
        # 创建回测实例，使用较短的时间范围
        backtest = StrategyBacktest()
        backtest.start_date = '20241201'  # 使用有数据的日期
        backtest.end_date = '20241202'
        
        print(f"📅 测试期间: {backtest.start_date} - {backtest.end_date}")
        
        # 测试数据加载
        print("\n📊 测试数据加载...")
        if backtest.load_data():
            print("✅ 数据加载成功")
            
            print(f"  股票基础信息: {len(backtest.stock_basic)} 只")
            print(f"  日线数据: {len(backtest.daily_data)} 条")
            print(f"  基本面数据: {len(backtest.daily_basic)} 条")
            print(f"  合并后数据: {len(backtest.stock_data)} 条")
            print(f"  交易日数: {len(backtest.trade_dates)}")
            
            if len(backtest.stock_data) > 0:
                print("\n📊 数据样本:")
                sample = backtest.stock_data.head(3)
                print(f"  PE数据类型: {sample['pe_ttm'].dtype}")
                print(f"  PB数据类型: {sample['pb'].dtype}")
                print(f"  PE样本值: {sample['pe_ttm'].tolist()}")
                print(f"  PB样本值: {sample['pb'].tolist()}")
                
                # 测试单日计算
                if len(backtest.trade_dates) > 0:
                    test_date = backtest.trade_dates[0]
                    print(f"\n🧪 测试单日计算 ({test_date})...")
                    
                    try:
                        # 测试大盘PB计算
                        market_pb = backtest.calculate_market_pb(test_date)
                        print(f"  ✅ 大盘PB: {market_pb:.2f}")
                        
                        # 测试股票评分
                        scored_stocks = backtest.calculate_stock_scores(test_date)
                        print(f"  ✅ 评分股票数: {len(scored_stocks)}")
                        
                        if len(scored_stocks) > 0:
                            top_stock = scored_stocks.iloc[0]
                            print(f"  ✅ 最高分股票: {top_stock['ts_code']} (分数: {top_stock['total_score']:.1f})")
                        
                        # 测试仓位计算
                        position_weight = backtest.get_position_weight(test_date)
                        print(f"  ✅ 目标仓位: {position_weight:.1%}")
                        
                        print("🎉 单日计算测试通过！")
                        return True
                        
                    except Exception as e:
                        print(f"❌ 单日计算失败: {e}")
                        import traceback
                        traceback.print_exc()
                        return False
                else:
                    print("⚠️  没有交易日数据")
                    return False
            else:
                print("❌ 没有合并后的股票数据")
                return False
        else:
            print("❌ 数据加载失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 策略修复快速测试")
    print("=" * 30)
    
    if quick_test():
        print("\n🎉 测试成功！")
        print("✅ 策略系统已修复，可以正常运行")
        print("\n💡 现在可以运行完整回测:")
        print("   python run_strategy_backtest.py")
    else:
        print("\n❌ 测试失败")
        print("💡 需要进一步检查数据问题")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
