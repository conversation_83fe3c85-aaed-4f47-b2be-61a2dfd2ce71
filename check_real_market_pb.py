#!/usr/bin/env python3
"""
检查真正的大盘市净率计算方法
大盘市净率 = 全市场总市值 ÷ 全市场净资产总额
"""

import sys
import os
import pandas as pd
import numpy as np

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def calculate_real_market_pb():
    """计算真正的大盘市净率"""
    print("🔍 计算真正的大盘市净率...")
    print("公式: 大盘市净率 = 全市场总市值 ÷ 全市场净资产总额")
    
    try:
        from database.models import db_config
        
        session = db_config.get_session()
        
        # 获取最近的交易日
        recent_date_sql = """
            SELECT DISTINCT trade_date FROM daily_basic 
            WHERE trade_date >= '20240101'
            ORDER BY trade_date DESC 
            LIMIT 1
        """
        recent_date = pd.read_sql(recent_date_sql, session.bind)
        
        if len(recent_date) > 0:
            test_date = recent_date.iloc[0]['trade_date']
            print(f"\n📅 计算日期: {test_date}")
            
            # 获取该日期的所有A股数据
            # 需要: 总市值(total_mv) 和 市净率(pb)
            # 净资产 = 总市值 ÷ 市净率
            data_sql = f"""
                SELECT d.ts_code, d.close, b.pb, b.total_mv, s.name, s.market
                FROM daily d
                INNER JOIN daily_basic b ON d.ts_code = b.ts_code AND d.trade_date = b.trade_date
                LEFT JOIN stock_basic s ON d.ts_code = s.ts_code
                WHERE d.trade_date = '{test_date}'
                AND b.pb > 0 AND b.total_mv > 0
                AND (s.market = 'main' OR s.market IS NULL OR d.ts_code LIKE '%.SH' OR d.ts_code LIKE '%.SZ')
            """
            data = pd.read_sql(data_sql, session.bind)
            print(f"📊 A股数据: {len(data)} 只股票")
            
            if len(data) > 0:
                # 转换数据类型
                data['pb'] = pd.to_numeric(data['pb'], errors='coerce')
                data['total_mv'] = pd.to_numeric(data['total_mv'], errors='coerce')
                
                # 过滤有效数据
                valid_data = data[
                    (data['pb'] > 0) & 
                    (data['total_mv'] > 0) & 
                    (data['pb'].notna()) & 
                    (data['total_mv'].notna())
                ]
                print(f"📊 有效数据: {len(valid_data)} 只股票")
                
                if len(valid_data) > 0:
                    # 计算净资产 = 总市值 ÷ 市净率
                    valid_data['net_assets'] = valid_data['total_mv'] / valid_data['pb']
                    
                    # 计算全市场汇总
                    total_market_value = valid_data['total_mv'].sum()  # 全市场总市值
                    total_net_assets = valid_data['net_assets'].sum()  # 全市场净资产总额
                    
                    # 真正的大盘市净率
                    real_market_pb = total_market_value / total_net_assets
                    
                    print(f"\n📊 计算结果:")
                    print(f"  全市场总市值: {total_market_value:,.0f} 万元")
                    print(f"  全市场净资产: {total_net_assets:,.0f} 万元")
                    print(f"  真正大盘市净率: {real_market_pb:.3f}")
                    
                    # 对比我之前的错误方法
                    # 方法1: 简单中位数
                    simple_median = valid_data['pb'].median()
                    
                    # 方法2: 市值加权平均
                    weighted_avg = (valid_data['pb'] * valid_data['total_mv']).sum() / valid_data['total_mv'].sum()
                    
                    print(f"\n📊 对比错误方法:")
                    print(f"  简单中位数PB: {simple_median:.3f}")
                    print(f"  市值加权平均PB: {weighted_avg:.3f}")
                    print(f"  真正大盘PB: {real_market_pb:.3f}")
                    
                    print(f"\n📊 差异分析:")
                    print(f"  真实值 vs 中位数: {((real_market_pb/simple_median-1)*100):+.1f}%")
                    print(f"  真实值 vs 加权平均: {((real_market_pb/weighted_avg-1)*100):+.1f}%")
                    
                    # 分析为什么会有差异
                    print(f"\n🔍 差异原因分析:")
                    
                    # 按市值分组分析
                    valid_data['mv_group'] = pd.qcut(valid_data['total_mv'], 
                                                   q=5, 
                                                   labels=['小盘', '中小盘', '中盘', '中大盘', '大盘'])
                    
                    group_analysis = valid_data.groupby('mv_group').agg({
                        'total_mv': ['sum', 'count'],
                        'pb': 'mean',
                        'net_assets': 'sum'
                    }).round(3)
                    
                    print("  按市值分组分析:")
                    print(group_analysis)
                    
                    # 计算各组对大盘PB的贡献
                    print(f"\n  各组对大盘PB的贡献:")
                    for group in ['小盘', '中小盘', '中盘', '中大盘', '大盘']:
                        group_data = valid_data[valid_data['mv_group'] == group]
                        if len(group_data) > 0:
                            group_mv = group_data['total_mv'].sum()
                            group_na = group_data['net_assets'].sum()
                            group_pb = group_mv / group_na if group_na > 0 else 0
                            weight = group_mv / total_market_value
                            print(f"    {group}: PB={group_pb:.3f}, 权重={weight:.1%}")
                    
                    return real_market_pb
                else:
                    print("❌ 没有有效数据")
                    return None
            else:
                print("❌ 没有找到数据")
                return None
        else:
            print("❌ 没有找到交易日")
            return None
        
        session.close()
        
    except Exception as e:
        print(f"❌ 计算失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def check_data_availability():
    """检查数据可用性"""
    print("\n🔍 检查数据可用性...")
    
    try:
        from database.models import db_config
        
        session = db_config.get_session()
        
        # 检查daily_basic表是否有净资产相关字段
        columns_sql = "PRAGMA table_info(daily_basic)"
        columns_info = pd.read_sql(columns_sql, session.bind)
        
        print("daily_basic表字段:")
        for _, row in columns_info.iterrows():
            print(f"  {row['name']}: {row['type']}")
        
        # 检查是否有直接的净资产字段
        net_asset_fields = ['net_assets', 'total_assets', 'book_value']
        available_fields = columns_info['name'].tolist()
        
        print(f"\n净资产相关字段检查:")
        for field in net_asset_fields:
            if field in available_fields:
                print(f"  ✅ {field}: 可用")
            else:
                print(f"  ❌ {field}: 不可用")
        
        # 检查是否可以通过PB和市值计算净资产
        if 'pb' in available_fields and 'total_mv' in available_fields:
            print(f"  ✅ 可以通过 总市值÷市净率 计算净资产")
        else:
            print(f"  ❌ 无法计算净资产")
        
        session.close()
        
    except Exception as e:
        print(f"❌ 数据检查失败: {e}")

def main():
    """主函数"""
    print("🔍 真正的大盘市净率计算")
    print("=" * 50)
    
    # 检查数据可用性
    check_data_availability()
    
    # 计算真正的大盘市净率
    real_pb = calculate_real_market_pb()
    
    if real_pb:
        print(f"\n🎯 结论:")
        print(f"真正的大盘市净率 = {real_pb:.3f}")
        print(f"\n💡 这个值才是正确的大盘市净率！")
        print(f"应该用这个值来进行择时判断:")
        
        if real_pb < 1.2:
            signal = "🟢 满仓信号"
        elif real_pb < 2.0:
            signal = "🟡 买入信号"
        else:
            signal = "🔴 观望信号"
        
        print(f"当前择时信号: {signal}")
    else:
        print(f"\n❌ 无法计算真正的大盘市净率")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户中断计算")
    except Exception as e:
        print(f"\n❌ 计算异常: {e}")
        import traceback
        traceback.print_exc()
