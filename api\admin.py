"""
管理员功能模块
提供用户管理、系统监控、数据管理等高级功能
"""

from flask import Blueprint, request, jsonify, session
from sqlalchemy import text, func, desc
from datetime import datetime, timedelta
import json
from typing import Dict, List, Any, Optional

from database.models import db_config, User, SyncStatus
from config import get_config, TUSHARE_TABLES
from api.auth import generate_api_token

# 创建管理员蓝图
admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

# 配置
config = get_config()

def require_admin(func):
    """管理员权限装饰器"""
    def wrapper(*args, **kwargs):
        user_id = session.get('user_id')
        
        if not user_id:
            return jsonify({
                'code': -1,
                'msg': '需要登录',
                'data': None
            }), 401
        
        session_db = db_config.get_session()
        try:
            user = session_db.query(User).filter_by(id=user_id).first()
            
            if not user or not user.is_active:
                return jsonify({
                    'code': -1,
                    'msg': '用户不存在或已禁用',
                    'data': None
                }), 401
            
            # 检查管理员权限（这里简化为用户ID=1或用户名包含admin）
            if user.id != 1 and 'admin' not in user.username.lower():
                return jsonify({
                    'code': -1,
                    'msg': '需要管理员权限',
                    'data': None
                }), 403
            
            return func(*args, **kwargs)
            
        finally:
            session_db.close()
    
    wrapper.__name__ = func.__name__
    return wrapper

@admin_bp.route('/users', methods=['GET'])
@require_admin
def get_users():
    """获取用户列表"""
    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        search = request.args.get('search', '').strip()
        
        session_db = db_config.get_session()
        
        try:
            # 构建查询
            query = session_db.query(User)
            
            if search:
                query = query.filter(
                    (User.username.like(f'%{search}%')) |
                    (User.email.like(f'%{search}%'))
                )
            
            # 分页
            total = query.count()
            users = query.order_by(desc(User.created_at)).offset((page-1)*per_page).limit(per_page).all()
            
            # 转换为字典
            user_list = []
            for user in users:
                user_list.append({
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'is_active': user.is_active,
                    'is_premium': user.is_premium,
                    'api_calls_today': user.api_calls_today,
                    'api_limit_daily': user.api_limit_daily,
                    'created_at': user.created_at.isoformat() if user.created_at else None,
                    'last_login': user.last_login.isoformat() if user.last_login else None
                })
            
            return jsonify({
                'code': 0,
                'msg': 'success',
                'data': {
                    'users': user_list,
                    'pagination': {
                        'page': page,
                        'per_page': per_page,
                        'total': total,
                        'pages': (total + per_page - 1) // per_page
                    }
                }
            })
            
        finally:
            session_db.close()
            
    except Exception as e:
        return jsonify({
            'code': -1,
            'msg': f'获取用户列表失败: {str(e)}',
            'data': None
        }), 500

@admin_bp.route('/users/<int:user_id>', methods=['PUT'])
@require_admin
def update_user(user_id):
    """更新用户信息"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'code': -1,
                'msg': '请求数据为空',
                'data': None
            }), 400
        
        session_db = db_config.get_session()
        
        try:
            user = session_db.query(User).filter_by(id=user_id).first()
            
            if not user:
                return jsonify({
                    'code': -1,
                    'msg': '用户不存在',
                    'data': None
                }), 404
            
            # 更新用户信息
            if 'is_active' in data:
                user.is_active = bool(data['is_active'])
            
            if 'is_premium' in data:
                user.is_premium = bool(data['is_premium'])
                # 更新API限制
                if user.is_premium:
                    user.api_limit_daily = config.API_RATE_LIMIT['premium_user']
                else:
                    user.api_limit_daily = config.API_RATE_LIMIT['free_user']
            
            if 'api_limit_daily' in data:
                user.api_limit_daily = int(data['api_limit_daily'])
            
            session_db.commit()
            
            return jsonify({
                'code': 0,
                'msg': '用户信息更新成功',
                'data': {
                    'id': user.id,
                    'username': user.username,
                    'is_active': user.is_active,
                    'is_premium': user.is_premium,
                    'api_limit_daily': user.api_limit_daily
                }
            })
            
        except Exception as e:
            session_db.rollback()
            return jsonify({
                'code': -1,
                'msg': f'更新用户信息失败: {str(e)}',
                'data': None
            }), 500
        finally:
            session_db.close()
            
    except Exception as e:
        return jsonify({
            'code': -1,
            'msg': f'服务器错误: {str(e)}',
            'data': None
        }), 500

@admin_bp.route('/users/<int:user_id>/reset_token', methods=['POST'])
@require_admin
def admin_reset_user_token(user_id):
    """管理员重置用户Token"""
    try:
        session_db = db_config.get_session()
        
        try:
            user = session_db.query(User).filter_by(id=user_id).first()
            
            if not user:
                return jsonify({
                    'code': -1,
                    'msg': '用户不存在',
                    'data': None
                }), 404
            
            # 生成新Token
            user.api_token = generate_api_token()
            session_db.commit()
            
            return jsonify({
                'code': 0,
                'msg': f'用户 {user.username} 的Token已重置',
                'data': {
                    'user_id': user.id,
                    'username': user.username,
                    'new_token': user.api_token
                }
            })
            
        except Exception as e:
            session_db.rollback()
            return jsonify({
                'code': -1,
                'msg': f'重置Token失败: {str(e)}',
                'data': None
            }), 500
        finally:
            session_db.close()
            
    except Exception as e:
        return jsonify({
            'code': -1,
            'msg': f'服务器错误: {str(e)}',
            'data': None
        }), 500

@admin_bp.route('/stats', methods=['GET'])
@require_admin
def get_system_stats():
    """获取系统统计信息"""
    try:
        session_db = db_config.get_session()
        
        try:
            # 用户统计
            total_users = session_db.query(User).count()
            active_users = session_db.query(User).filter_by(is_active=True).count()
            premium_users = session_db.query(User).filter_by(is_premium=True).count()
            
            # 今日活跃用户
            today = datetime.now().date()
            today_active = session_db.query(User).filter(
                func.date(User.last_login) == today
            ).count()
            
            # API调用统计
            total_api_calls = session_db.query(func.sum(User.api_calls_today)).scalar() or 0
            
            # 数据表统计
            table_stats = []
            total_records = 0
            
            for table_name in TUSHARE_TABLES.keys():
                try:
                    result = session_db.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                    count = result.scalar()
                    table_stats.append({
                        'table_name': table_name,
                        'chinese_name': TUSHARE_TABLES[table_name]['name'],
                        'record_count': count
                    })
                    total_records += count
                except:
                    table_stats.append({
                        'table_name': table_name,
                        'chinese_name': TUSHARE_TABLES[table_name]['name'],
                        'record_count': 0
                    })
            
            # 同步状态统计
            sync_statuses = session_db.query(SyncStatus).all()
            sync_stats = {
                'total_tables': len(TUSHARE_TABLES),
                'synced_tables': len(sync_statuses),
                'success_tables': len([s for s in sync_statuses if s.sync_status == 'success']),
                'error_tables': len([s for s in sync_statuses if s.sync_status == 'error']),
                'last_sync': max([s.last_sync_time for s in sync_statuses if s.last_sync_time], default=None)
            }
            
            if sync_stats['last_sync']:
                sync_stats['last_sync'] = sync_stats['last_sync'].isoformat()
            
            return jsonify({
                'code': 0,
                'msg': 'success',
                'data': {
                    'user_stats': {
                        'total_users': total_users,
                        'active_users': active_users,
                        'premium_users': premium_users,
                        'today_active': today_active
                    },
                    'api_stats': {
                        'total_calls_today': total_api_calls,
                        'avg_calls_per_user': total_api_calls / max(active_users, 1)
                    },
                    'data_stats': {
                        'total_records': total_records,
                        'table_count': len(TUSHARE_TABLES),
                        'tables': table_stats
                    },
                    'sync_stats': sync_stats,
                    'generated_at': datetime.now().isoformat()
                }
            })
            
        finally:
            session_db.close()
            
    except Exception as e:
        return jsonify({
            'code': -1,
            'msg': f'获取系统统计失败: {str(e)}',
            'data': None
        }), 500

@admin_bp.route('/logs', methods=['GET'])
@require_admin
def get_system_logs():
    """获取系统日志"""
    try:
        log_type = request.args.get('type', 'sync')  # sync, api, error
        limit = int(request.args.get('limit', 100))
        
        session_db = db_config.get_session()
        
        try:
            if log_type == 'sync':
                # 获取同步日志
                sync_logs = session_db.query(SyncStatus).order_by(
                    desc(SyncStatus.last_sync_time)
                ).limit(limit).all()
                
                logs = []
                for log in sync_logs:
                    logs.append({
                        'table_name': log.table_name,
                        'status': log.sync_status,
                        'sync_date': log.last_sync_date,
                        'sync_time': log.last_sync_time.isoformat() if log.last_sync_time else None,
                        'record_count': log.record_count,
                        'error_message': log.error_message
                    })
                
                return jsonify({
                    'code': 0,
                    'msg': 'success',
                    'data': {
                        'logs': logs,
                        'type': log_type,
                        'count': len(logs)
                    }
                })
            
            else:
                return jsonify({
                    'code': -1,
                    'msg': f'不支持的日志类型: {log_type}',
                    'data': None
                }), 400
            
        finally:
            session_db.close()
            
    except Exception as e:
        return jsonify({
            'code': -1,
            'msg': f'获取系统日志失败: {str(e)}',
            'data': None
        }), 500

@admin_bp.route('/cleanup', methods=['POST'])
@require_admin
def cleanup_system():
    """系统清理"""
    try:
        data = request.get_json() or {}
        cleanup_type = data.get('type', 'api_calls')  # api_calls, old_logs
        
        session_db = db_config.get_session()
        
        try:
            if cleanup_type == 'api_calls':
                # 重置所有用户的API调用计数
                session_db.query(User).update({'api_calls_today': 0})
                session_db.commit()
                
                return jsonify({
                    'code': 0,
                    'msg': '所有用户API调用计数已重置',
                    'data': None
                })
            
            elif cleanup_type == 'old_logs':
                # 清理30天前的错误日志
                cutoff_date = datetime.now() - timedelta(days=30)
                deleted = session_db.query(SyncStatus).filter(
                    SyncStatus.last_sync_time < cutoff_date,
                    SyncStatus.sync_status == 'error'
                ).delete()
                session_db.commit()
                
                return jsonify({
                    'code': 0,
                    'msg': f'已清理 {deleted} 条旧日志记录',
                    'data': None
                })
            
            else:
                return jsonify({
                    'code': -1,
                    'msg': f'不支持的清理类型: {cleanup_type}',
                    'data': None
                }), 400
            
        except Exception as e:
            session_db.rollback()
            return jsonify({
                'code': -1,
                'msg': f'系统清理失败: {str(e)}',
                'data': None
            }), 500
        finally:
            session_db.close()
            
    except Exception as e:
        return jsonify({
            'code': -1,
            'msg': f'服务器错误: {str(e)}',
            'data': None
        }), 500
