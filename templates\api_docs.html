<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API文档 - Tushare数据镜像系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='style.css') }}" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="bi bi-database"></i>
                Tushare数据镜像系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('index') }}">
                    <i class="bi bi-house"></i>
                    首页
                </a>
                <a class="nav-link" href="{{ url_for('user_management') }}">
                    <i class="bi bi-person-gear"></i>
                    用户管理
                </a>
                <a class="nav-link active" href="#">
                    <i class="bi bi-book"></i>
                    API文档
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- API概览 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title mb-0">
                            <i class="bi bi-info-circle"></i>
                            API概览
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>基本信息</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td>API版本:</td>
                                        <td><span class="badge bg-primary">v1.0.0</span></td>
                                    </tr>
                                    <tr>
                                        <td>基础URL:</td>
                                        <td><code>http://localhost:5000/api/v1</code></td>
                                    </tr>
                                    <tr>
                                        <td>认证方式:</td>
                                        <td>API Token</td>
                                    </tr>
                                    <tr>
                                        <td>数据格式:</td>
                                        <td>JSON</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>限制说明</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td>免费用户:</td>
                                        <td>1000次/天</td>
                                    </tr>
                                    <tr>
                                        <td>高级用户:</td>
                                        <td>10000次/天</td>
                                    </tr>
                                    <tr>
                                        <td>单次最大:</td>
                                        <td>5000条记录</td>
                                    </tr>
                                    <tr>
                                        <td>请求方式:</td>
                                        <td>GET / POST</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 认证说明 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-shield-lock"></i>
                            认证方式
                        </h5>
                    </div>
                    <div class="card-body">
                        <p>所有API请求都需要提供有效的API Token进行认证。Token可以通过以下两种方式提供：</p>
                        
                        <h6>方式1: URL参数</h6>
                        <pre><code class="language-http">GET /api/v1/stock_basic?token=your_api_token_here&limit=10</code></pre>
                        
                        <h6>方式2: HTTP头部</h6>
                        <pre><code class="language-http">POST /api/v1/daily_basic
Authorization: your_api_token_here
Content-Type: application/json

{
    "ts_code": "000001.SZ",
    "limit": 5
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- API端点列表 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-list-ul"></i>
                            API端点列表
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>端点</th>
                                        <th>说明</th>
                                        <th>方法</th>
                                        <th>主要参数</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><code>/stock_basic</code></td>
                                        <td>股票基础信息</td>
                                        <td><span class="badge bg-success">GET</span></td>
                                        <td>is_hs, list_status, exchange</td>
                                    </tr>
                                    <tr>
                                        <td><code>/daily</code></td>
                                        <td>日线行情</td>
                                        <td><span class="badge bg-info">POST</span></td>
                                        <td>ts_code, trade_date, start_date, end_date</td>
                                    </tr>
                                    <tr>
                                        <td><code>/daily_basic</code></td>
                                        <td>每日指标</td>
                                        <td><span class="badge bg-info">POST</span></td>
                                        <td>ts_code, trade_date, start_date, end_date</td>
                                    </tr>
                                    <tr>
                                        <td><code>/trade_cal</code></td>
                                        <td>交易日历</td>
                                        <td><span class="badge bg-success">GET</span></td>
                                        <td>exchange, start_date, end_date, is_open</td>
                                    </tr>
                                    <tr>
                                        <td><code>/income</code></td>
                                        <td>利润表</td>
                                        <td><span class="badge bg-info">POST</span></td>
                                        <td>ts_code, ann_date, start_date, end_date</td>
                                    </tr>
                                    <tr>
                                        <td><code>/balancesheet</code></td>
                                        <td>资产负债表</td>
                                        <td><span class="badge bg-info">POST</span></td>
                                        <td>ts_code, ann_date, start_date, end_date</td>
                                    </tr>
                                    <tr>
                                        <td><code>/index_basic</code></td>
                                        <td>指数基础信息</td>
                                        <td><span class="badge bg-success">GET</span></td>
                                        <td>market, publisher, category</td>
                                    </tr>
                                    <tr>
                                        <td><code>/index_daily</code></td>
                                        <td>指数日线行情</td>
                                        <td><span class="badge bg-info">POST</span></td>
                                        <td>ts_code, trade_date, start_date, end_date</td>
                                    </tr>
                                    <tr>
                                        <td><code>/query</code></td>
                                        <td>通用查询接口</td>
                                        <td><span class="badge bg-info">POST</span></td>
                                        <td>api_name, params</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用示例 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-code-slash"></i>
                            使用示例
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- 示例标签页 -->
                        <ul class="nav nav-tabs" id="exampleTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="python-tab" data-bs-toggle="tab" data-bs-target="#python" type="button">Python</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="javascript-tab" data-bs-toggle="tab" data-bs-target="#javascript" type="button">JavaScript</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="curl-tab" data-bs-toggle="tab" data-bs-target="#curl" type="button">cURL</button>
                            </li>
                        </ul>
                        
                        <div class="tab-content" id="exampleTabContent">
                            <!-- Python示例 -->
                            <div class="tab-pane fade show active" id="python" role="tabpanel">
                                <h6 class="mt-3">获取股票基础信息</h6>
                                <pre><code class="language-python">import requests
import pandas as pd

# API配置
base_url = "http://localhost:5000/api/v1"
token = "your_api_token_here"

# 获取股票基础信息
response = requests.get(f"{base_url}/stock_basic", params={
    "token": token,
    "limit": 100,
    "is_hs": "S"  # 沪深港通标的
})

data = response.json()
if data['code'] == 0:
    df = pd.DataFrame(data['data']['items'])
    print(f"获取到 {len(df)} 条股票信息")
    print(df[['ts_code', 'name', 'industry']].head())
else:
    print(f"请求失败: {data['msg']}")
</code></pre>

                                <h6 class="mt-3">获取每日指标</h6>
                                <pre><code class="language-python"># 获取平安银行最近10天的每日指标
response = requests.post(f"{base_url}/daily_basic", 
    headers={"Authorization": token},
    json={
        "ts_code": "000001.SZ",
        "limit": 10
    }
)

data = response.json()
if data['code'] == 0:
    df = pd.DataFrame(data['data']['items'])
    print(df[['trade_date', 'close', 'pe', 'pb']].head())
</code></pre>
                            </div>
                            
                            <!-- JavaScript示例 -->
                            <div class="tab-pane fade" id="javascript" role="tabpanel">
                                <h6 class="mt-3">获取股票基础信息</h6>
                                <pre><code class="language-javascript">const baseUrl = "http://localhost:5000/api/v1";
const token = "your_api_token_here";

// 获取股票基础信息
fetch(`${baseUrl}/stock_basic?token=${token}&limit=100`)
    .then(response => response.json())
    .then(data => {
        if (data.code === 0) {
            console.log(`获取到 ${data.data.items.length} 条股票信息`);
            data.data.items.forEach(stock => {
                console.log(`${stock.ts_code} - ${stock.name}`);
            });
        } else {
            console.error(`请求失败: ${data.msg}`);
        }
    });
</code></pre>

                                <h6 class="mt-3">获取每日指标</h6>
                                <pre><code class="language-javascript">// 获取每日指标
fetch(`${baseUrl}/daily_basic`, {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': token
    },
    body: JSON.stringify({
        ts_code: "000001.SZ",
        limit: 10
    })
})
.then(response => response.json())
.then(data => {
    if (data.code === 0) {
        data.data.items.forEach(item => {
            console.log(`${item.trade_date}: PE=${item.pe}, PB=${item.pb}`);
        });
    }
});
</code></pre>
                            </div>
                            
                            <!-- cURL示例 -->
                            <div class="tab-pane fade" id="curl" role="tabpanel">
                                <h6 class="mt-3">获取股票基础信息</h6>
                                <pre><code class="language-bash">curl -X GET "http://localhost:5000/api/v1/stock_basic?token=your_api_token_here&limit=10"</code></pre>

                                <h6 class="mt-3">获取每日指标</h6>
                                <pre><code class="language-bash">curl -X POST "http://localhost:5000/api/v1/daily_basic" \
  -H "Authorization: your_api_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "ts_code": "000001.SZ",
    "limit": 5
  }'</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 响应格式 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-file-earmark-code"></i>
                            响应格式
                        </h5>
                    </div>
                    <div class="card-body">
                        <p>所有API响应都采用统一的JSON格式：</p>
                        
                        <h6>成功响应</h6>
                        <pre><code class="language-json">{
  "code": 0,
  "msg": "success",
  "data": {
    "fields": ["ts_code", "name", "industry", "..."],
    "items": [
      {
        "ts_code": "000001.SZ",
        "name": "平安银行",
        "industry": "银行",
        "..."
      }
    ],
    "has_more": false
  }
}</code></pre>

                        <h6>错误响应</h6>
                        <pre><code class="language-json">{
  "code": -1,
  "msg": "错误描述信息",
  "data": null
}</code></pre>

                        <h6>状态码说明</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><code>0</code></td>
                                <td>请求成功</td>
                            </tr>
                            <tr>
                                <td><code>-1</code></td>
                                <td>请求失败</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Prism.js for syntax highlighting -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
</body>
</html>
