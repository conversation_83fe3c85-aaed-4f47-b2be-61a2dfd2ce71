#!/usr/bin/env python3
"""
数据同步命令行工具
用于管理和执行数据同步任务
"""

import click
import sys
import os
from datetime import datetime, timedelta
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.init_db import init_database, check_database, reset_database
from sync.data_sync import TushareDataSync, sync_all_tables, sync_daily_tables
from config import TUSHARE_TABLES, ensure_directories

@click.group()
def cli():
    """Tushare数据同步工具"""
    # 确保必要的目录存在
    ensure_directories()

@cli.command()
def init():
    """初始化数据库"""
    click.echo("正在初始化数据库...")
    
    if init_database():
        click.echo("✓ 数据库初始化成功")
    else:
        click.echo("✗ 数据库初始化失败")
        sys.exit(1)

@cli.command()
def status():
    """检查数据库和同步状态"""
    click.echo("检查数据库状态...")
    
    tables_info = check_database()
    if tables_info is None:
        click.echo("✗ 无法连接到数据库")
        sys.exit(1)
    
    # 显示同步状态
    from database.models import db_config, SyncStatus
    session = db_config.get_session()
    
    try:
        sync_statuses = session.query(SyncStatus).all()
        
        if sync_statuses:
            click.echo("\n同步状态:")
            click.echo("=" * 80)
            click.echo(f"{'表名':<20} {'状态':<10} {'最后同步':<12} {'记录数':<10} {'错误信息'}")
            click.echo("-" * 80)
            
            for status in sync_statuses:
                error_msg = status.error_message[:30] + "..." if status.error_message and len(status.error_message) > 30 else (status.error_message or "")
                click.echo(f"{status.table_name:<20} {status.sync_status:<10} {status.last_sync_date or 'N/A':<12} {status.record_count:<10} {error_msg}")
        else:
            click.echo("\n暂无同步记录")
    
    finally:
        session.close()

@cli.command()
@click.option('--table', help='指定要同步的表名')
@click.option('--start-date', help='开始日期 (YYYYMMDD)')
@click.option('--end-date', help='结束日期 (YYYYMMDD)')
@click.option('--force-full', is_flag=True, help='强制全量同步')
def sync(table, start_date, end_date, force_full):
    """同步数据"""
    
    if table and table not in TUSHARE_TABLES:
        click.echo(f"错误: 不支持的表名 '{table}'")
        click.echo(f"支持的表名: {', '.join(TUSHARE_TABLES.keys())}")
        sys.exit(1)
    
    # 验证日期格式
    if start_date:
        try:
            datetime.strptime(start_date, '%Y%m%d')
        except ValueError:
            click.echo("错误: 开始日期格式不正确，请使用 YYYYMMDD 格式")
            sys.exit(1)
    
    if end_date:
        try:
            datetime.strptime(end_date, '%Y%m%d')
        except ValueError:
            click.echo("错误: 结束日期格式不正确，请使用 YYYYMMDD 格式")
            sys.exit(1)
    
    syncer = TushareDataSync()
    
    try:
        if table:
            # 同步指定表
            click.echo(f"开始同步表: {table}")
            result = syncer.sync_table(table, start_date, end_date, force_full)
            
            if result['status'] == 'success':
                click.echo(f"✓ 同步完成: {result['record_count']} 条记录")
            else:
                click.echo(f"✗ 同步失败: {result.get('error_message', '未知错误')}")
                sys.exit(1)
        else:
            # 同步所有表
            click.echo("开始同步所有表...")
            results = sync_all_tables(force_full)
            
            success_count = 0
            total_records = 0
            
            click.echo("\n同步结果:")
            click.echo("=" * 60)
            
            for table_name, result in results.items():
                status_icon = "✓" if result['status'] == 'success' else "✗"
                record_count = result.get('record_count', 0)
                
                click.echo(f"{status_icon} {table_name:<20} : {record_count:>8} 条记录")
                
                if result['status'] == 'success':
                    success_count += 1
                    total_records += record_count
                else:
                    click.echo(f"   错误: {result.get('error_message', '未知错误')}")
            
            click.echo("=" * 60)
            click.echo(f"成功: {success_count}/{len(results)} 个表")
            click.echo(f"总记录数: {total_records:,}")
    
    except Exception as e:
        click.echo(f"✗ 同步过程中发生错误: {e}")
        sys.exit(1)

@cli.command()
def sync_daily():
    """同步每日更新的表"""
    click.echo("开始同步每日更新的表...")
    
    try:
        results = sync_daily_tables()
        
        success_count = 0
        total_records = 0
        
        click.echo("\n每日同步结果:")
        click.echo("=" * 60)
        
        for table_name, result in results.items():
            status_icon = "✓" if result['status'] == 'success' else "✗"
            record_count = result.get('record_count', 0)
            
            click.echo(f"{status_icon} {table_name:<20} : {record_count:>8} 条记录")
            
            if result['status'] == 'success':
                success_count += 1
                total_records += record_count
            else:
                click.echo(f"   错误: {result.get('error_message', '未知错误')}")
        
        click.echo("=" * 60)
        click.echo(f"成功: {success_count}/{len(results)} 个表")
        click.echo(f"总记录数: {total_records:,}")
    
    except Exception as e:
        click.echo(f"✗ 每日同步失败: {e}")
        sys.exit(1)

@cli.command()
def list_tables():
    """列出所有支持的数据表"""
    click.echo("支持的数据表:")
    click.echo("=" * 80)
    click.echo(f"{'表名':<20} {'中文名':<25} {'同步类型':<10} {'同步频率':<10}")
    click.echo("-" * 80)
    
    for table_name, config in TUSHARE_TABLES.items():
        click.echo(f"{table_name:<20} {config['name']:<25} {config['sync_type']:<10} {config['sync_frequency']:<10}")

@cli.command()
@click.confirmation_option(prompt='确认要重置数据库吗？这将删除所有数据！')
def reset():
    """重置数据库（删除所有数据）"""
    click.echo("正在重置数据库...")
    
    if reset_database():
        click.echo("✓ 数据库重置成功")
    else:
        click.echo("✗ 数据库重置失败")
        sys.exit(1)

@cli.command()
@click.option('--output', '-o', default='sync_report.json', help='输出文件名')
def report(output):
    """生成同步报告"""
    from database.models import db_config, SyncStatus
    
    session = db_config.get_session()
    
    try:
        sync_statuses = session.query(SyncStatus).all()
        
        report_data = {
            'generated_at': datetime.now().isoformat(),
            'total_tables': len(TUSHARE_TABLES),
            'synced_tables': len(sync_statuses),
            'tables': []
        }
        
        for status in sync_statuses:
            table_info = {
                'table_name': status.table_name,
                'chinese_name': TUSHARE_TABLES.get(status.table_name, {}).get('name', '未知'),
                'sync_status': status.sync_status,
                'last_sync_date': status.last_sync_date,
                'last_sync_time': status.last_sync_time.isoformat() if status.last_sync_time else None,
                'record_count': status.record_count,
                'error_message': status.error_message
            }
            report_data['tables'].append(table_info)
        
        # 保存报告
        with open(output, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        click.echo(f"✓ 同步报告已保存到: {output}")
        
        # 显示摘要
        success_count = sum(1 for s in sync_statuses if s.sync_status == 'success')
        total_records = sum(s.record_count for s in sync_statuses if s.record_count)
        
        click.echo(f"\n报告摘要:")
        click.echo(f"总表数: {len(TUSHARE_TABLES)}")
        click.echo(f"已同步: {len(sync_statuses)}")
        click.echo(f"成功: {success_count}")
        click.echo(f"总记录数: {total_records:,}")
    
    finally:
        session.close()

if __name__ == '__main__':
    cli()
