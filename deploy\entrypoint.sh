#!/bin/bash

# Tushare数据镜像系统启动脚本

set -e

echo "🚀 启动Tushare数据镜像系统..."

# 等待依赖服务
echo "⏳ 等待依赖服务启动..."
sleep 5

# 初始化数据库
echo "📊 初始化数据库..."
python sync_cli.py init --quiet || echo "数据库已存在，跳过初始化"

# 检查系统状态
echo "🔍 检查系统状态..."
python sync_cli.py status

# 启动定时任务调度器
echo "⏰ 启动定时任务调度器..."
python -c "from scheduler.tasks import start_scheduler; start_scheduler()" &

# 启动Web应用
echo "🌐 启动Web应用..."
exec python app.py
