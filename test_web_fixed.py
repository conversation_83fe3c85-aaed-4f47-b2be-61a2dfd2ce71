#!/usr/bin/env python3
"""
测试修复后的Web应用
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_web_app():
    """测试Web应用"""
    print("🌐 测试修复后的Web应用...")
    
    try:
        from app import app
        print("✅ app.py 导入成功")
        
        # 测试数据库状态API
        with app.test_client() as client:
            print("\n🔍 测试数据库状态API...")
            response = client.get('/api/database_status')
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.get_json()
                if data and data.get('success'):
                    summary = data.get('summary', {})
                    tables = data.get('tables', [])
                    
                    print(f"✅ 数据库状态API正常")
                    print(f"📊 总表数: {summary.get('total_tables', 0)}")
                    print(f"📊 总记录数: {summary.get('total_records', 0):,}")
                    print(f"📊 有数据表: {summary.get('tables_with_data', 0)}")
                    
                    # 显示前几个表的状态
                    print("\n📋 表状态:")
                    for table in tables[:5]:
                        name = table.get('table_name', '')
                        chinese_name = table.get('chinese_name', '')
                        count = table.get('record_count', 0)
                        status = table.get('status', '')
                        print(f"  {name} ({chinese_name}): {count:,} 条记录 [{status}]")
                    
                    if len(tables) > 5:
                        print(f"  ... 还有 {len(tables) - 5} 个表")
                        
                else:
                    error = data.get('error', '未知错误') if data else '无响应数据'
                    print(f"⚠️  数据库状态API返回错误: {error}")
            else:
                print(f"❌ 数据库状态API失败: {response.status_code}")
            
            # 测试其他API端点
            print("\n🔍 测试其他API端点...")
            
            # 测试表列表API
            response = client.get('/api/tables')
            print(f"表列表API: {response.status_code}")
            
            # 测试主页
            response = client.get('/')
            print(f"主页: {response.status_code}")
        
        print("\n✅ Web应用测试完成")
        return True
        
    except Exception as e:
        print(f"❌ Web应用测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def start_test_server():
    """启动测试服务器"""
    print("🚀 启动测试Web服务器...")
    
    try:
        from app import app
        
        print("🌐 启动Web服务器...")
        print("📍 访问地址: http://localhost:5001")
        print("💡 现在可以在浏览器中查看数据库状态和字段信息")
        print("按 Ctrl+C 停止服务器")
        
        app.run(
            debug=True, 
            host='127.0.0.1', 
            port=5001, 
            use_reloader=False
        )
        
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔧 Web应用修复测试工具")
    print("=" * 40)
    
    # 先测试功能
    success = test_web_app()
    
    if success:
        print("\n" + "=" * 40)
        response = input("🤔 是否启动测试服务器？(y/N): ").strip().lower()
        
        if response in ['y', 'yes']:
            start_test_server()
        else:
            print("\n💡 您可以运行以下命令启动服务器:")
            print("python test_web_fixed.py start")
    else:
        print("\n❌ 测试失败，请检查错误信息")
    
    return success

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "start":
        start_test_server()
    else:
        success = main()
        sys.exit(0 if success else 1)
