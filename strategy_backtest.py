#!/usr/bin/env python3
"""
量化投资策略回测系统
基于市净率择时 + 多因子选股策略
回测期间：2015年至今
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class StrategyBacktest:
    """策略回测类"""
    
    def __init__(self):
        self.start_date = '20150101'
        self.end_date = datetime.now().strftime('%Y%m%d')
        self.initial_capital = 1000000  # 初始资金100万
        self.current_capital = self.initial_capital
        self.positions = {}  # 持仓 {stock_code: {'shares': 数量, 'cost': 成本价, 'buy_date': 买入日期}}
        self.cash = self.initial_capital  # 现金
        self.trade_records = []  # 交易记录
        self.daily_returns = []  # 每日收益记录

        # 策略参数
        self.market_pb_buy = 2.0      # 大盘市净率买入阈值
        self.market_pb_full = 1.2     # 大盘市净率满仓阈值
        self.max_industry_weight = 0.03  # 单行业最大权重3%
        self.sell_half_return = 1.0   # 减半卖出涨幅100%
        self.sell_all_return = 2.0    # 清仓涨幅200%

        # 交易成本参数
        self.commission_rate = 0.0003  # 佣金费率0.03%
        self.stamp_tax_rate = 0.001    # 印花税1%（仅卖出）
        self.min_commission = 5        # 最低佣金5元
        self.slippage_rate = 0.001     # 滑点0.1%

        # 流动性约束参数
        self.max_volume_ratio = 0.1    # 最大成交量占比10%

        # 数据延迟参数
        self.data_delay_days = 1       # 基本面数据延迟1天
        
        print(f"🎯 策略回测系统初始化完成")
        print(f"📅 回测期间: {self.start_date} - {self.end_date}")
        print(f"💰 初始资金: {self.initial_capital:,} 元")
    
    def load_data(self):
        """加载回测所需数据"""
        print("\n📊 加载回测数据...")
        
        try:
            from database.models import db_config
            from sqlalchemy import text
            
            session = db_config.get_session()
            
            # 加载股票基础信息（包含历史退市股票以避免生存者偏差）
            print("  📋 加载股票基础信息...")
            stock_basic_sql = "SELECT * FROM stock_basic"
            self.stock_basic = pd.read_sql(stock_basic_sql, session.bind)

            print(f"  📊 原始股票数据: {len(self.stock_basic)} 只")

            # 处理生存者偏差：包含历史上存在的所有股票
            if 'list_status' in self.stock_basic.columns:
                status_counts = self.stock_basic['list_status'].value_counts(dropna=False)
                print(f"  📊 上市状态分布: {dict(status_counts)}")

                # 包含所有曾经上市的股票（L=上市，D=退市，P=暂停）
                # 这样可以避免生存者偏差
                valid_status = ['L', 'D', 'P']
                if any(status in self.stock_basic['list_status'].values for status in valid_status):
                    # 只排除从未上市的股票
                    self.stock_basic = self.stock_basic[
                        self.stock_basic['list_status'].isin(valid_status) |
                        self.stock_basic['list_status'].isna()
                    ]
                    print(f"  📊 包含历史股票后: {len(self.stock_basic)} 只")
                else:
                    print("  ⚠️  list_status字段无有效数据，使用全部股票")

                # 改进的风险股票过滤（但保留历史数据）
                if 'name' in self.stock_basic.columns:
                    # 只排除明显的垃圾股票，保留历史正常股票
                    risk_patterns = [
                        r'测试',      # 测试股票
                        r'模拟',      # 模拟股票
                    ]

                    if risk_patterns:
                        risk_pattern = '|'.join(risk_patterns)
                        before_count = len(self.stock_basic)
                        self.stock_basic = self.stock_basic[
                            ~self.stock_basic['name'].str.contains(risk_pattern, na=False, regex=True)
                        ]
                        print(f"  📊 排除测试股票: {before_count} -> {len(self.stock_basic)} 只")

            print(f"  ✅ 股票基础信息: {len(self.stock_basic)} 只股票")
            
            # 加载日线行情数据
            print("  📈 加载日线行情数据...")
            daily_sql = f"""
                SELECT * FROM daily 
                WHERE trade_date >= '{self.start_date}' 
                AND trade_date <= '{self.end_date}'
                ORDER BY trade_date
            """
            self.daily_data = pd.read_sql(daily_sql, session.bind)
            print(f"  ✅ 日线数据: {len(self.daily_data)} 条记录")
            
            # 加载每日基本面数据
            print("  📊 加载每日基本面数据...")
            daily_basic_sql = f"""
                SELECT * FROM daily_basic
                WHERE trade_date >= '{self.start_date}'
                AND trade_date <= '{self.end_date}'
                ORDER BY trade_date
            """
            self.daily_basic = pd.read_sql(daily_basic_sql, session.bind)
            print(f"  ✅ 基本面数据: {len(self.daily_basic)} 条记录")

            if len(self.daily_basic) > 0:
                basic_date_range = f"{self.daily_basic['trade_date'].min()} - {self.daily_basic['trade_date'].max()}"
                print(f"    日期范围: {basic_date_range}")

            if len(self.daily_data) > 0:
                daily_date_range = f"{self.daily_data['trade_date'].min()} - {self.daily_data['trade_date'].max()}"
                print(f"    日线日期范围: {daily_date_range}")

            # 检查数据日期匹配情况
            if len(self.daily_data) > 0 and len(self.daily_basic) > 0:
                daily_dates = set(self.daily_data['trade_date'].unique())
                basic_dates = set(self.daily_basic['trade_date'].unique())
                common_dates = daily_dates & basic_dates
                print(f"    共同交易日: {len(common_dates)} 天")

                if len(common_dates) < 100:  # 如果共同交易日太少
                    print("    ⚠️  警告: 共同交易日较少，可能影响回测效果")
            
            # 加载交易日历
            print("  📅 加载交易日历...")
            trade_cal_sql = f"""
                SELECT * FROM trade_cal 
                WHERE cal_date >= '{self.start_date}' 
                AND cal_date <= '{self.end_date}'
                AND is_open = 1
                ORDER BY cal_date
            """
            self.trade_calendar = pd.read_sql(trade_cal_sql, session.bind)
            self.trade_dates = self.trade_calendar['cal_date'].tolist()
            print(f"  ✅ 交易日历: {len(self.trade_dates)} 个交易日")
            
            session.close()
            
            # 数据预处理
            self._preprocess_data()
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def _preprocess_data(self):
        """数据预处理"""
        print("\n🔧 数据预处理...")

        # 确保日期格式一致
        self.daily_data['trade_date'] = self.daily_data['trade_date'].astype(str)
        self.daily_basic['trade_date'] = self.daily_basic['trade_date'].astype(str)

        # 合并日线和基本面数据（包含总市值用于大盘PB计算）
        basic_columns = ['ts_code', 'trade_date', 'pe_ttm', 'pb']
        if 'total_mv' in self.daily_basic.columns:
            basic_columns.append('total_mv')

        self.stock_data = pd.merge(
            self.daily_data,
            self.daily_basic[basic_columns],
            on=['ts_code', 'trade_date'],
            how='left'
        )

        # 添加行业信息
        if 'industry' in self.stock_basic.columns:
            self.stock_data = pd.merge(
                self.stock_data,
                self.stock_basic[['ts_code', 'industry']],
                on='ts_code',
                how='left'
            )
        else:
            # 如果没有行业信息，创建默认行业
            self.stock_data['industry'] = '其他'

        # 计算前复权价格（简化处理，使用收盘价）
        self.stock_data['adj_close'] = self.stock_data['close']

        # 过滤无效数据
        self.stock_data = self.stock_data.dropna(subset=['close', 'adj_close'])

        # 按股票分组计算滚动最低价（修正时间窗口偏差）
        print("  📈 计算技术指标...")
        self.stock_data = self.stock_data.sort_values(['ts_code', 'trade_date'])

        # 计算一年内最低价（使用历史数据，避免前瞻性偏差）
        def calculate_historical_min(group):
            """计算历史最低价，避免使用未来数据"""
            group = group.sort_values('trade_date')
            # 使用expanding window而不是rolling window，确保只使用历史数据
            group['min_price_1y'] = group['adj_close'].expanding(min_periods=1).min()
            # 但限制在过去250个交易日内
            group['min_price_1y'] = group['adj_close'].rolling(window=250, min_periods=1).min()
            return group

        self.stock_data = self.stock_data.groupby('ts_code').apply(calculate_historical_min).reset_index(drop=True)

        # 计算涨幅
        self.stock_data['return_from_low'] = (
            self.stock_data['adj_close'] / self.stock_data['min_price_1y'] - 1
        ).fillna(0)

        # 转换数据类型并填充缺失值
        print("  🔧 转换数据类型...")

        # 转换PE、PB为数值类型，处理空字符串和None
        def clean_numeric(series):
            """清理数值字段，将空字符串转换为NaN"""
            return pd.to_numeric(series.replace('', None), errors='coerce')

        self.stock_data['pe_ttm'] = clean_numeric(self.stock_data['pe_ttm'])
        self.stock_data['pb'] = clean_numeric(self.stock_data['pb'])

        # 检查转换结果
        pe_valid = self.stock_data['pe_ttm'].notna().sum()
        pb_valid = self.stock_data['pb'].notna().sum()
        print(f"    有效PE数据: {pe_valid}/{len(self.stock_data)}")
        print(f"    有效PB数据: {pb_valid}/{len(self.stock_data)}")

        # 改进的缺失值处理：使用行业中位数填充
        print("  🔧 智能填充缺失数据...")

        # 按行业和日期计算PE、PB中位数
        industry_medians = self.stock_data.groupby(['industry', 'trade_date']).agg({
            'pe_ttm': 'median',
            'pb': 'median'
        }).reset_index()
        industry_medians.columns = ['industry', 'trade_date', 'pe_industry_median', 'pb_industry_median']

        # 合并行业中位数
        self.stock_data = pd.merge(
            self.stock_data,
            industry_medians,
            on=['industry', 'trade_date'],
            how='left'
        )

        # 使用行业中位数填充，如果行业中位数也缺失则使用全市场中位数
        market_pe_median = self.stock_data['pe_ttm'].median()
        market_pb_median = self.stock_data['pb'].median()

        # 如果市场中位数也是NaN，使用合理默认值
        if pd.isna(market_pe_median):
            market_pe_median = 20
        if pd.isna(market_pb_median):
            market_pb_median = 2.0

        self.stock_data['pe_ttm'] = self.stock_data['pe_ttm'].fillna(
            self.stock_data['pe_industry_median']
        ).fillna(market_pe_median)

        self.stock_data['pb'] = self.stock_data['pb'].fillna(
            self.stock_data['pb_industry_median']
        ).fillna(market_pb_median)

        # 清理临时列
        self.stock_data = self.stock_data.drop(['pe_industry_median', 'pb_industry_median'], axis=1)

        # 确保价格数据为数值类型
        price_columns = ['open', 'high', 'low', 'close', 'pre_close', 'vol', 'amount']
        for col in price_columns:
            if col in self.stock_data.columns:
                self.stock_data[col] = pd.to_numeric(self.stock_data[col], errors='coerce')

        # 过滤无效数据
        self.stock_data = self.stock_data[
            (self.stock_data['close'] > 0) &
            (self.stock_data['pe_ttm'] > 0) &
            (self.stock_data['pb'] > 0)
        ]

        print(f"  ✅ 数据预处理完成: {len(self.stock_data)} 条有效股票日线数据")
    
    def calculate_market_pb(self, date):
        """计算大盘市净率（引入数据延迟，避免前瞻性偏差）"""
        from datetime import datetime, timedelta

        # 引入数据延迟：使用T-1的数据
        date_obj = datetime.strptime(str(date), '%Y%m%d')
        delayed_date_obj = date_obj - timedelta(days=self.data_delay_days)

        # 找到最近的交易日数据
        available_dates = sorted(self.stock_data['trade_date'].unique())
        delayed_date_str = delayed_date_obj.strftime('%Y%m%d')

        # 找到小于等于延迟日期的最近交易日
        valid_dates = [d for d in available_dates if d <= delayed_date_str]
        if not valid_dates:
            return 2.0  # 默认值

        actual_date = max(valid_dates)

        date_data = self.stock_data[
            (self.stock_data['trade_date'] == actual_date) &
            (self.stock_data['pb'] > 0) &
            (self.stock_data['pb'].notna()) &
            (self.stock_data['total_mv'] > 0) &
            (self.stock_data['total_mv'].notna())
        ].copy()

        if len(date_data) == 0:
            return 2.0  # 默认值

        # 计算净资产 = 总市值 ÷ 市净率
        date_data['net_assets'] = date_data['total_mv'] / date_data['pb']

        # 计算全市场汇总
        total_market_value = date_data['total_mv'].sum()  # 全市场总市值
        total_net_assets = date_data['net_assets'].sum()   # 全市场净资产总额

        # 真正的大盘市净率 = 全市场总市值 ÷ 全市场净资产总额
        if total_net_assets > 0:
            market_pb = total_market_value / total_net_assets
        else:
            market_pb = 2.0  # 默认值

        return market_pb
    
    def get_tradable_stocks_on_date(self, date):
        """获取指定日期可交易的股票（解决股票池动态变化问题）"""
        date_str = str(date)

        # 过滤出在指定日期可交易的股票
        tradable_stocks = self.stock_basic[
            # 已经上市（上市日期 <= 当前日期）
            (self.stock_basic['list_date'].fillna('99991231') <= date_str) &
            # 还未退市（退市日期为空 或 退市日期 > 当前日期）
            ((self.stock_basic['delist_date'].isna()) |
             (self.stock_basic['delist_date'].fillna('99991231') > date_str))
        ].copy()

        return tradable_stocks['ts_code'].tolist()

    def handle_delisting(self, date):
        """处理退市股票（强制卖出）"""
        date_str = str(date)

        # 找出今日退市的股票
        delisted_today = self.stock_basic[
            self.stock_basic['delist_date'] == date_str
        ]['ts_code'].tolist()

        delisted_positions = []

        # 强制卖出退市股票
        for ts_code in delisted_today:
            if ts_code in self.positions:
                position = self.positions[ts_code]

                # 获取退市前最后价格
                last_price_data = self.stock_data[
                    (self.stock_data['ts_code'] == ts_code) &
                    (self.stock_data['trade_date'] <= date_str)
                ].sort_values('trade_date').tail(1)

                if len(last_price_data) > 0:
                    last_price = last_price_data.iloc[0]['close']
                    shares = position['shares']

                    # 退市通常有较大损失，按最后价格的50%计算
                    delisting_price = last_price * 0.5
                    gross_amount = shares * delisting_price * 100

                    # 退市不收交易费用，但有损失
                    self.cash += gross_amount

                    # 记录退市交易
                    delisted_positions.append({
                        'date': date,
                        'ts_code': ts_code,
                        'action': 'delisting',
                        'shares': shares,
                        'last_price': last_price,
                        'delisting_price': delisting_price,
                        'amount': gross_amount,
                        'loss_rate': -0.5  # 50%损失
                    })

                    # 移除持仓
                    del self.positions[ts_code]

                    print(f"  ⚠️  {ts_code} 退市，强制卖出 {shares} 手，损失50%")

        # 记录退市交易
        for delisted in delisted_positions:
            self.trade_records.append(delisted)

        return len(delisted_positions)

    def calculate_stock_scores(self, date):
        """计算股票评分（只考虑可交易股票，引入数据延迟）"""
        from datetime import datetime, timedelta

        # 首先获取可交易股票池
        tradable_codes = self.get_tradable_stocks_on_date(date)

        if not tradable_codes:
            return pd.DataFrame()

        # 引入数据延迟：使用T-1的基本面数据，但当日的价格数据
        date_obj = datetime.strptime(str(date), '%Y%m%d')
        delayed_date_obj = date_obj - timedelta(days=self.data_delay_days)

        # 找到最近的交易日数据
        available_dates = sorted(self.stock_data['trade_date'].unique())
        delayed_date_str = delayed_date_obj.strftime('%Y%m%d')
        current_date_str = str(date)

        # 找到小于等于延迟日期的最近交易日（用于基本面数据）
        valid_dates = [d for d in available_dates if d <= delayed_date_str]
        if not valid_dates:
            return pd.DataFrame()

        fundamental_date = max(valid_dates)

        # 获取延迟的基本面数据（只包含可交易股票）
        fundamental_data = self.stock_data[
            (self.stock_data['trade_date'] == fundamental_date) &
            (self.stock_data['ts_code'].isin(tradable_codes)) &  # 只考虑可交易股票
            (self.stock_data['pe_ttm'] > 0) &
            (self.stock_data['pb'] > 0) &
            (self.stock_data['pe_ttm'].notna()) &
            (self.stock_data['pb'].notna())
        ][['ts_code', 'pe_ttm', 'pb', 'industry']].copy()

        # 获取当日的价格数据（只包含可交易股票）
        price_data = self.stock_data[
            (self.stock_data['trade_date'] == current_date_str) &
            (self.stock_data['ts_code'].isin(tradable_codes)) &  # 只考虑可交易股票
            (self.stock_data['return_from_low'].notna())
        ][['ts_code', 'close', 'return_from_low', 'vol']].copy()

        # 合并数据
        date_data = pd.merge(fundamental_data, price_data, on='ts_code', how='inner')

        if len(date_data) == 0:
            return pd.DataFrame()

        # PE评分：25为0分，10为100分
        date_data['pe_score'] = np.clip(
            100 - (date_data['pe_ttm'] - 10) * 100 / (25 - 10), 0, 100
        )

        # PB评分：2.5为0分，1为100分
        date_data['pb_score'] = np.clip(
            100 - (date_data['pb'] - 1) * 100 / (2.5 - 1), 0, 100
        )

        # 涨幅评分：50%为0分，0%为100分
        date_data['return_score'] = np.clip(
            100 - date_data['return_from_low'] * 100 / 0.5, 0, 100
        )

        # 总分
        date_data['total_score'] = (
            date_data['pe_score'] +
            date_data['pb_score'] +
            date_data['return_score']
        )

        return date_data.sort_values('total_score', ascending=False)
    
    def get_position_weight(self, date):
        """根据大盘市净率确定目标仓位（考虑已有持仓）"""
        market_pb = self.calculate_market_pb(date)

        # 计算目标仓位权重
        if market_pb >= self.market_pb_buy:
            target_weight = 0.0  # 不买入
        elif market_pb <= self.market_pb_full:
            target_weight = 1.0  # 满仓
        else:
            # 线性插值
            target_weight = (self.market_pb_buy - market_pb) / (self.market_pb_buy - self.market_pb_full)
            target_weight = min(max(target_weight, 0), 1)

        # 计算当前持仓权重
        current_position_value = 0
        date_str = str(date)

        for ts_code, position in self.positions.items():
            # 获取当前价格
            current_data = self.stock_data[
                (self.stock_data['ts_code'] == ts_code) &
                (self.stock_data['trade_date'] == date_str)
            ]

            if len(current_data) > 0:
                current_price = current_data.iloc[0]['close']
                position_value = position['shares'] * current_price * 100
                current_position_value += position_value

        current_weight = current_position_value / self.current_capital if self.current_capital > 0 else 0

        # 返回需要调整的权重（目标权重 - 当前权重）
        adjustment_weight = target_weight - current_weight

        # 只返回需要买入的权重（不考虑减仓，减仓通过止盈逻辑处理）
        return max(adjustment_weight, 0)
    
    def select_stocks(self, scored_stocks, target_weight):
        """选股逻辑（添加流动性约束）"""
        if target_weight <= 0 or len(scored_stocks) == 0:
            return []

        selected_stocks = []
        industry_weights = {}
        total_weight = 0

        for _, stock in scored_stocks.iterrows():
            industry = stock['industry']

            # 检查行业权重限制
            current_industry_weight = industry_weights.get(industry, 0)
            if current_industry_weight >= self.max_industry_weight:
                continue

            # 流动性检查：确保有足够的成交量
            daily_volume = stock.get('vol', 0)  # 日成交量（手）
            if daily_volume <= 0:
                continue

            # 计算基于流动性的最大可买入权重
            # 假设不能超过日成交量的10%
            max_shares_by_liquidity = daily_volume * 100 * self.max_volume_ratio  # 转换为股数
            max_amount_by_liquidity = max_shares_by_liquidity * stock['close']
            max_weight_by_liquidity = max_amount_by_liquidity / self.current_capital

            # 计算可分配权重（考虑多重约束）
            available_weight = min(
                target_weight - total_weight,
                self.max_industry_weight - current_industry_weight,
                0.02,  # 单只股票最大权重2%
                max_weight_by_liquidity  # 流动性约束
            )

            if available_weight > 0.005:  # 最小权重0.5%
                selected_stocks.append({
                    'ts_code': stock['ts_code'],
                    'weight': available_weight,
                    'price': stock['close'],
                    'score': stock['total_score'],
                    'industry': industry,
                    'daily_volume': daily_volume
                })

                industry_weights[industry] = current_industry_weight + available_weight
                total_weight += available_weight

                if total_weight >= target_weight * 0.95:  # 95%仓位即可
                    break

        return selected_stocks
    
    def check_sell_signals(self, date):
        """检查卖出信号"""
        sell_orders = []
        date_str = str(date)

        for ts_code, position in self.positions.items():
            # 获取当前价格
            current_data = self.stock_data[
                (self.stock_data['ts_code'] == ts_code) &
                (self.stock_data['trade_date'].astype(str) == date_str)
            ]

            if len(current_data) == 0:
                continue

            current_price = current_data.iloc[0]['close']
            cost_price = position['cost']
            return_rate = (current_price / cost_price - 1)

            if return_rate >= self.sell_all_return:
                # 清仓
                sell_orders.append({
                    'ts_code': ts_code,
                    'action': 'sell_all',
                    'shares': position['shares'],
                    'price': current_price,
                    'return': return_rate
                })
            elif return_rate >= self.sell_half_return:
                # 减半
                sell_orders.append({
                    'ts_code': ts_code,
                    'action': 'sell_half',
                    'shares': position['shares'] // 2,
                    'price': current_price,
                    'return': return_rate
                })

        return sell_orders
    
    def calculate_trading_cost(self, amount, is_sell=False):
        """计算交易成本"""
        # 佣金
        commission = max(amount * self.commission_rate, self.min_commission)

        # 印花税（仅卖出时收取）
        stamp_tax = amount * self.stamp_tax_rate if is_sell else 0

        # 滑点成本
        slippage_cost = amount * self.slippage_rate

        total_cost = commission + stamp_tax + slippage_cost
        return total_cost, commission, stamp_tax, slippage_cost

    def execute_trades(self, date, buy_list, sell_list):
        """执行交易（包含交易成本）"""
        # 执行卖出
        for sell_order in sell_list:
            ts_code = sell_order['ts_code']
            shares = sell_order['shares']
            price = sell_order['price']

            # 考虑滑点的实际成交价格
            actual_price = price * (1 - self.slippage_rate)  # 卖出时滑点向下
            gross_amount = shares * actual_price * 100  # 假设每手100股

            # 计算交易成本
            total_cost, commission, stamp_tax, slippage_cost = self.calculate_trading_cost(
                gross_amount, is_sell=True
            )

            # 实际到账金额
            net_amount = gross_amount - total_cost

            # 更新现金
            self.cash += net_amount

            # 更新持仓
            if sell_order['action'] == 'sell_all':
                del self.positions[ts_code]
            else:
                self.positions[ts_code]['shares'] -= shares

            # 记录交易
            self.trade_records.append({
                'date': date,
                'ts_code': ts_code,
                'action': 'sell',
                'shares': shares,
                'price': price,
                'actual_price': actual_price,
                'gross_amount': gross_amount,
                'trading_cost': total_cost,
                'net_amount': net_amount,
                'commission': commission,
                'stamp_tax': stamp_tax,
                'slippage_cost': slippage_cost,
                'return': sell_order['return']
            })
        
        # 执行买入
        for buy_order in buy_list:
            ts_code = buy_order['ts_code']
            weight = buy_order['weight']
            price = buy_order['price']

            # 考虑滑点的实际成交价格
            actual_price = price * (1 + self.slippage_rate)  # 买入时滑点向上

            # 计算买入金额
            target_amount = self.current_capital * weight
            shares = int(target_amount / (actual_price * 100))  # 每手100股
            gross_amount = shares * actual_price * 100

            # 计算交易成本
            total_cost, commission, stamp_tax, slippage_cost = self.calculate_trading_cost(
                gross_amount, is_sell=False
            )

            # 实际需要的现金
            total_required = gross_amount + total_cost

            if shares > 0 and total_required <= self.cash:
                # 更新现金
                self.cash -= total_required

                # 更新持仓
                if ts_code in self.positions:
                    # 加仓（重新计算平均成本，包含交易成本）
                    old_shares = self.positions[ts_code]['shares']
                    old_cost = self.positions[ts_code]['cost']
                    new_shares = old_shares + shares
                    # 成本价包含交易费用
                    cost_per_share = (gross_amount + total_cost) / (shares * 100)
                    new_cost = (old_shares * old_cost + shares * cost_per_share) / new_shares

                    self.positions[ts_code] = {
                        'shares': new_shares,
                        'cost': new_cost,
                        'buy_date': date
                    }
                else:
                    # 新建仓位
                    cost_per_share = (gross_amount + total_cost) / (shares * 100)
                    self.positions[ts_code] = {
                        'shares': shares,
                        'cost': cost_per_share,
                        'buy_date': date
                    }

                # 记录交易
                self.trade_records.append({
                    'date': date,
                    'ts_code': ts_code,
                    'action': 'buy',
                    'shares': shares,
                    'price': price,
                    'actual_price': actual_price,
                    'gross_amount': gross_amount,
                    'trading_cost': total_cost,
                    'total_cost': total_required,
                    'commission': commission,
                    'stamp_tax': stamp_tax,
                    'slippage_cost': slippage_cost,
                    'weight': weight
                })
    
    def calculate_portfolio_value(self, date):
        """计算组合总价值"""
        portfolio_value = self.cash
        date_str = str(date)

        for ts_code, position in self.positions.items():
            # 获取当前价格
            current_data = self.stock_data[
                (self.stock_data['ts_code'] == ts_code) &
                (self.stock_data['trade_date'].astype(str) == date_str)
            ]

            if len(current_data) > 0:
                current_price = current_data.iloc[0]['close']
                portfolio_value += position['shares'] * current_price * 100

        return portfolio_value
    
    def run_backtest(self):
        """运行回测"""
        print(f"\n🚀 开始策略回测...")
        print(f"📅 回测期间: {self.start_date} - {self.end_date}")
        
        if not self.load_data():
            return False
        
        # 按交易日逐日回测
        for i, date in enumerate(self.trade_dates):
            if i % 50 == 0:  # 每50个交易日显示进度
                progress = (i / len(self.trade_dates)) * 100
                tradable_count = len(self.get_tradable_stocks_on_date(date))
                print(f"  📊 回测进度: {progress:.1f}% ({date}) - 可交易股票: {tradable_count} 只")

            # 0. 处理退市股票（必须在其他操作之前）
            delisted_count = self.handle_delisting(date)
            if delisted_count > 0:
                print(f"  ⚠️  {date}: {delisted_count} 只股票退市")

            # 1. 检查卖出信号
            sell_orders = self.check_sell_signals(date)

            # 2. 计算目标仓位
            target_weight = self.get_position_weight(date)

            # 3. 选股（只从可交易股票中选择）
            buy_orders = []
            if target_weight > 0:
                scored_stocks = self.calculate_stock_scores(date)
                if len(scored_stocks) > 0:
                    buy_orders = self.select_stocks(scored_stocks, target_weight)
                else:
                    # 如果没有可交易股票，记录这个情况
                    if i % 100 == 0:  # 偶尔提醒
                        print(f"  ⚠️  {date}: 没有符合条件的可交易股票")

            # 4. 执行交易
            self.execute_trades(date, buy_orders, sell_orders)

            # 5. 计算当日组合价值
            portfolio_value = self.calculate_portfolio_value(date)
            daily_return = (portfolio_value / self.current_capital - 1) if self.current_capital > 0 else 0

            # 6. 记录每日数据（包含股票池信息）
            tradable_stocks_count = len(self.get_tradable_stocks_on_date(date))

            self.daily_returns.append({
                'date': date,
                'portfolio_value': portfolio_value,
                'cash': self.cash,
                'position_count': len(self.positions),
                'tradable_stocks_count': tradable_stocks_count,  # 新增：可交易股票数量
                'daily_return': daily_return,
                'cumulative_return': (portfolio_value / self.initial_capital - 1),
                'delisted_count': delisted_count  # 新增：当日退市股票数量
            })

            self.current_capital = portfolio_value
        
        print(f"✅ 回测完成!")
        return True
    
    def generate_report(self):
        """生成回测报告"""
        if not self.daily_returns:
            print("❌ 没有回测数据")
            return
        
        print(f"\n📊 策略回测报告")
        print(f"=" * 60)
        
        # 基本统计
        final_value = self.daily_returns[-1]['portfolio_value']
        total_return = (final_value / self.initial_capital - 1) * 100
        
        print(f"💰 初始资金: {self.initial_capital:,.0f} 元")
        print(f"💰 最终资金: {final_value:,.0f} 元")
        print(f"📈 总收益率: {total_return:.2f}%")
        
        # 年化收益率
        years = len(self.trade_dates) / 250  # 假设每年250个交易日
        annual_return = (pow(final_value / self.initial_capital, 1/years) - 1) * 100
        print(f"📈 年化收益率: {annual_return:.2f}%")
        
        # 交易统计
        print(f"\n📊 交易统计:")
        print(f"🔄 总交易次数: {len(self.trade_records)}")
        
        buy_trades = [t for t in self.trade_records if t['action'] == 'buy']
        sell_trades = [t for t in self.trade_records if t['action'] == 'sell']
        
        print(f"📈 买入次数: {len(buy_trades)}")
        print(f"📉 卖出次数: {len(sell_trades)}")
        
        if sell_trades:
            profitable_trades = [t for t in sell_trades if t['return'] > 0]
            win_rate = len(profitable_trades) / len(sell_trades) * 100
            avg_return = np.mean([t['return'] for t in sell_trades]) * 100
            
            print(f"🎯 胜率: {win_rate:.1f}%")
            print(f"📊 平均收益: {avg_return:.2f}%")
        
        # 保存详细结果
        self._save_results()
        
        print(f"\n✅ 回测报告生成完成!")
        print(f"📁 详细结果已保存到 backtest_results.xlsx")
    
    def _save_results(self):
        """保存回测结果"""
        try:
            # 保存到Excel文件
            with pd.ExcelWriter('backtest_results.xlsx', engine='openpyxl') as writer:
                # 每日收益
                pd.DataFrame(self.daily_returns).to_excel(
                    writer, sheet_name='每日收益', index=False
                )
                
                # 交易记录
                pd.DataFrame(self.trade_records).to_excel(
                    writer, sheet_name='交易记录', index=False
                )
                
                # 策略参数
                params_df = pd.DataFrame([{
                    '参数名': '大盘PB买入阈值',
                    '参数值': self.market_pb_buy
                }, {
                    '参数名': '大盘PB满仓阈值', 
                    '参数值': self.market_pb_full
                }, {
                    '参数名': '单行业最大权重',
                    '参数值': f"{self.max_industry_weight*100}%"
                }, {
                    '参数名': '减半卖出涨幅',
                    '参数值': f"{self.sell_half_return*100}%"
                }, {
                    '参数名': '清仓涨幅',
                    '参数值': f"{self.sell_all_return*100}%"
                }])
                
                params_df.to_excel(writer, sheet_name='策略参数', index=False)
            
            print("💾 结果已保存到 backtest_results.xlsx")
            
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")

def run_parameter_optimization():
    """参数优化"""
    print("🔧 策略参数优化...")

    # 参数组合
    pb_buy_values = [1.8, 2.0, 2.2]
    pb_full_values = [1.0, 1.2, 1.4]
    industry_weights = [0.02, 0.03, 0.05]

    best_return = -999
    best_params = None
    results = []

    total_combinations = len(pb_buy_values) * len(pb_full_values) * len(industry_weights)
    current_combination = 0

    for pb_buy in pb_buy_values:
        for pb_full in pb_full_values:
            for industry_weight in industry_weights:
                if pb_buy <= pb_full:
                    continue

                current_combination += 1
                print(f"\n📊 测试参数组合 {current_combination}/{total_combinations}")
                print(f"   PB买入: {pb_buy}, PB满仓: {pb_full}, 行业权重: {industry_weight*100}%")

                # 创建回测实例
                backtest = StrategyBacktest()
                backtest.market_pb_buy = pb_buy
                backtest.market_pb_full = pb_full
                backtest.max_industry_weight = industry_weight

                # 运行回测
                if backtest.run_backtest():
                    final_value = backtest.daily_returns[-1]['portfolio_value']
                    total_return = (final_value / backtest.initial_capital - 1) * 100

                    results.append({
                        'pb_buy': pb_buy,
                        'pb_full': pb_full,
                        'industry_weight': industry_weight,
                        'total_return': total_return,
                        'final_value': final_value
                    })

                    if total_return > best_return:
                        best_return = total_return
                        best_params = {
                            'pb_buy': pb_buy,
                            'pb_full': pb_full,
                            'industry_weight': industry_weight
                        }

                    print(f"   📈 收益率: {total_return:.2f}%")

    # 保存优化结果
    if results:
        results_df = pd.DataFrame(results)
        results_df.to_excel('parameter_optimization.xlsx', index=False)

        print(f"\n🎯 参数优化完成!")
        print(f"🏆 最佳参数组合:")
        print(f"   PB买入阈值: {best_params['pb_buy']}")
        print(f"   PB满仓阈值: {best_params['pb_full']}")
        print(f"   行业权重限制: {best_params['industry_weight']*100}%")
        print(f"   最佳收益率: {best_return:.2f}%")
        print(f"📁 详细结果保存到 parameter_optimization.xlsx")

def main():
    """主函数"""
    print("🎯 量化投资策略回测系统")
    print("=" * 50)
    print("请选择运行模式:")
    print("1. 单次回测")
    print("2. 参数优化")

    try:
        choice = input("\n请输入选择 (1/2): ").strip()

        if choice == "1":
            # 单次回测
            backtest = StrategyBacktest()

            if backtest.run_backtest():
                backtest.generate_report()
            else:
                print("❌ 回测失败")

        elif choice == "2":
            # 参数优化
            run_parameter_optimization()

        else:
            print("❌ 无效选择")

    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
    except Exception as e:
        print(f"❌ 运行失败: {e}")

if __name__ == "__main__":
    main()
