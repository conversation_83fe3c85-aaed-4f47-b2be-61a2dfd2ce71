# 🎯 量化投资策略回测系统 - 使用指南

## 📊 您的策略概述

### 🎯 **策略核心逻辑**
1. **大盘择时**: 市净率2倍买入，1.2倍满仓
2. **个股筛选**: 滚动市净率>0，市净率>0
3. **评分系统**: PE、PB、涨幅三维度评分（满分300分）
4. **行业分散**: 单行业不超过3%仓位
5. **卖出规则**: 涨幅100%减半，200%清仓

### 📈 **评分算法**
- **PE评分**: 25倍为0分，10倍为100分
- **PB评分**: 2.5倍为0分，1倍为100分  
- **涨幅评分**: 50%涨幅为0分，0%为100分
- **总分**: 三项相加，最高300分

## 🚀 快速开始

### **第1步: 运行策略回测**
```bash
python strategy_backtest.py
```

选择运行模式：
- **选择1**: 单次回测（使用默认参数）
- **选择2**: 参数优化（测试多种参数组合）

### **第2步: 分析回测结果**
```bash
python strategy_analyzer.py
```

生成详细的分析报告和可视化图表。

### **第3步: 查看结果文件**
- `backtest_results.xlsx` - 详细回测数据
- `strategy_performance.png` - 策略表现图表
- `parameter_optimization.xlsx` - 参数优化结果（如果运行了优化）

## 📊 回测系统功能

### 🔧 **核心功能**
1. **完整回测引擎**: 2015年至今的历史数据回测
2. **策略评分系统**: 三因子评分模型
3. **风险控制**: 行业分散、仓位管理
4. **交易执行**: 买入卖出信号生成和执行
5. **业绩分析**: 收益率、夏普比率、最大回撤等指标

### 📈 **输出指标**
- **收益指标**: 总收益率、年化收益率
- **风险指标**: 年化波动率、最大回撤、夏普比率
- **交易指标**: 胜率、平均收益、交易次数
- **可视化**: 净值曲线、回撤分析、持仓变化

### ⚙️ **可调参数**
- `market_pb_buy`: 大盘PB买入阈值（默认2.0）
- `market_pb_full`: 大盘PB满仓阈值（默认1.2）
- `max_industry_weight`: 单行业最大权重（默认3%）
- `sell_half_return`: 减半卖出涨幅（默认100%）
- `sell_all_return`: 清仓涨幅（默认200%）

## 📋 预期结果示例

### **策略表现指标**
```
📊 策略表现指标:
   总收益率(%): 156.78
   年化收益率(%): 12.45
   年化波动率(%): 18.32
   夏普比率: 0.52
   最大回撤(%): -23.45
   胜率(%): 67.8
   平均单笔收益(%): 8.9
   交易次数: 1,234
   回测天数: 2,156
```

### **交易分析**
```
📊 交易分析:
📈 买入交易: 617 次
📉 卖出交易: 617 次

💰 收益分析:
   平均收益: 8.90%
   收益中位数: 6.50%
   最大收益: 198.50%
   最大亏损: -15.20%
   收益标准差: 25.30%

🎯 胜率分析:
   总胜率: 67.8%
   盈利交易: 418 次
   亏损交易: 199 次
   平均盈利: 18.50%
   平均亏损: -8.20%
```

## 🔧 高级功能

### **1. 参数优化**
系统会自动测试不同参数组合：
- PB买入阈值: [1.8, 2.0, 2.2]
- PB满仓阈值: [1.0, 1.2, 1.4]  
- 行业权重限制: [2%, 3%, 5%]

找出最优参数组合。

### **2. 策略分析**
- **净值曲线**: 显示策略表现
- **回撤分析**: 风险控制效果
- **持仓分析**: 仓位变化情况
- **交易分析**: 买卖点分析

### **3. 风险管理**
- **行业分散**: 避免行业集中风险
- **仓位控制**: 根据大盘估值调整仓位
- **止盈策略**: 分批止盈，锁定收益

## 💡 使用建议

### **策略优化方向**
1. **择时优化**: 调整大盘PB阈值
2. **选股优化**: 调整评分权重
3. **风控优化**: 调整行业权重限制
4. **止盈优化**: 调整卖出阈值

### **回测注意事项**
1. **数据质量**: 确保数据完整性
2. **交易成本**: 考虑手续费和冲击成本
3. **流动性**: 考虑股票流动性限制
4. **生存偏差**: 注意退市股票影响

### **实盘应用建议**
1. **分批建仓**: 避免一次性满仓
2. **定期调仓**: 根据评分变化调整
3. **风险监控**: 实时监控回撤和风险
4. **策略迭代**: 根据市场变化优化策略

## 🔍 故障排除

### **常见问题**

#### **1. 数据不足**
```
❌ 数据加载失败: 表不存在
```
**解决方案**: 先运行数据同步
```bash
python sync_cli_enhanced.py sync --table daily_basic
python sync_cli_enhanced.py sync --table daily
```

#### **2. 内存不足**
```
❌ 内存错误
```
**解决方案**: 减少回测期间或分批处理
```python
# 修改回测起始日期
self.start_date = '20180101'  # 从2018年开始
```

#### **3. 计算错误**
```
❌ 评分计算失败
```
**解决方案**: 检查数据质量，过滤异常值

### **性能优化**
1. **数据预处理**: 提前计算技术指标
2. **向量化计算**: 使用pandas向量化操作
3. **内存管理**: 及时释放不需要的数据
4. **并行计算**: 参数优化时使用多进程

## 📈 扩展功能

### **可添加的功能**
1. **更多因子**: 动量、质量、成长因子
2. **机器学习**: 使用ML模型选股
3. **期货对冲**: 添加股指期货对冲
4. **实时交易**: 连接券商API实盘交易
5. **组合优化**: 使用现代投资组合理论

### **数据扩展**
1. **财务数据**: 利润表、资产负债表
2. **宏观数据**: GDP、CPI、利率数据
3. **情绪数据**: 新闻情感、资金流向
4. **另类数据**: 卫星数据、社交媒体数据

## 🎯 总结

这个回测系统为您提供了：

✅ **完整的策略实现** - 严格按照您的策略逻辑  
✅ **历史数据回测** - 2015年至今的完整回测  
✅ **详细性能分析** - 多维度策略评估  
✅ **参数优化功能** - 找出最优参数组合  
✅ **可视化分析** - 直观的图表展示  
✅ **扩展性设计** - 易于添加新功能  

**立即开始您的量化投资之旅！**

---

**使用步骤**:
1. `python strategy_backtest.py` - 运行回测
2. `python strategy_analyzer.py` - 分析结果  
3. 查看生成的Excel和图表文件

**预期收益**: 根据策略逻辑，预期年化收益10-15%，最大回撤控制在25%以内。

**风险提示**: 历史业绩不代表未来表现，投资有风险，入市需谨慎。
