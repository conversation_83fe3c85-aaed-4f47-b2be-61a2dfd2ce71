"""
数据导出功能
支持多种格式的数据导出
"""

from flask import Blueprint, request, jsonify, send_file
from sqlalchemy import text
import pandas as pd
import json
import os
import zipfile
from datetime import datetime
from io import BytesIO
import tempfile
from typing import Dict, List, Any, Optional

from database.models import db_config
from config import get_config, TUSHARE_TABLES
from api.tushare_api import rate_limit, TushareAPIHandler

# 创建导出蓝图
export_bp = Blueprint('export', __name__, url_prefix='/api/v1/export')

# 配置
config = get_config()

@export_bp.route('/tables', methods=['GET'])
@rate_limit
def get_exportable_tables():
    """获取可导出的数据表列表"""
    try:
        session = db_config.get_session()
        
        try:
            tables_info = []
            
            for table_name, table_config in TUSHARE_TABLES.items():
                try:
                    # 获取表的记录数
                    result = session.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                    count = result.scalar()
                    
                    # 获取表的列信息
                    columns_result = session.execute(text(f"PRAGMA table_info({table_name})"))
                    columns = [row[1] for row in columns_result.fetchall()]
                    
                    tables_info.append({
                        'table_name': table_name,
                        'chinese_name': table_config['name'],
                        'record_count': count,
                        'column_count': len(columns),
                        'columns': columns[:10],  # 只返回前10个列名
                        'sync_type': table_config['sync_type'],
                        'exportable': count > 0
                    })
                    
                except Exception as e:
                    tables_info.append({
                        'table_name': table_name,
                        'chinese_name': table_config['name'],
                        'record_count': 0,
                        'column_count': 0,
                        'columns': [],
                        'sync_type': table_config['sync_type'],
                        'exportable': False,
                        'error': str(e)
                    })
            
            return jsonify({
                'code': 0,
                'msg': 'success',
                'data': {
                    'tables': tables_info,
                    'total_tables': len(tables_info),
                    'exportable_tables': len([t for t in tables_info if t['exportable']])
                }
            })
            
        finally:
            session.close()
            
    except Exception as e:
        return jsonify({
            'code': -1,
            'msg': f'获取表列表失败: {str(e)}',
            'data': None
        }), 500

@export_bp.route('/data', methods=['POST'])
@rate_limit
def export_data():
    """导出数据"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'code': -1,
                'msg': '请求数据为空',
                'data': None
            }), 400
        
        table_name = data.get('table_name')
        export_format = data.get('format', 'csv').lower()  # csv, excel, json
        limit = data.get('limit', 10000)
        conditions = data.get('conditions', {})
        
        # 验证参数
        if not table_name:
            return jsonify({
                'code': -1,
                'msg': '缺少table_name参数',
                'data': None
            }), 400
        
        if table_name not in TUSHARE_TABLES:
            return jsonify({
                'code': -1,
                'msg': f'不支持的表名: {table_name}',
                'data': None
            }), 400
        
        if export_format not in ['csv', 'excel', 'json']:
            return jsonify({
                'code': -1,
                'msg': f'不支持的导出格式: {export_format}',
                'data': None
            }), 400
        
        # 限制导出数量
        limit = min(limit, 50000)  # 最大5万条
        
        # 使用API处理器查询数据
        handler = TushareAPIHandler()
        
        # 构建查询参数
        query_params = conditions.copy()
        query_params['limit'] = limit
        
        # 查询数据
        result = handler.query_data(table_name, query_params)
        
        if result['code'] != 0:
            return jsonify(result)
        
        items = result['data']['items']
        
        if not items:
            return jsonify({
                'code': -1,
                'msg': '没有数据可导出',
                'data': None
            }), 400
        
        # 创建DataFrame
        df = pd.DataFrame(items)
        
        # 生成文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename_base = f"{table_name}_{timestamp}"
        
        if export_format == 'csv':
            # 导出CSV
            output = BytesIO()
            df.to_csv(output, index=False, encoding='utf-8-sig')
            output.seek(0)
            
            return send_file(
                output,
                mimetype='text/csv',
                as_attachment=True,
                download_name=f"{filename_base}.csv"
            )
        
        elif export_format == 'excel':
            # 导出Excel
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name=table_name, index=False)
            output.seek(0)
            
            return send_file(
                output,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=f"{filename_base}.xlsx"
            )
        
        elif export_format == 'json':
            # 导出JSON
            output = BytesIO()
            json_data = {
                'table_name': table_name,
                'chinese_name': TUSHARE_TABLES[table_name]['name'],
                'export_time': datetime.now().isoformat(),
                'record_count': len(items),
                'data': items
            }
            output.write(json.dumps(json_data, ensure_ascii=False, indent=2).encode('utf-8'))
            output.seek(0)
            
            return send_file(
                output,
                mimetype='application/json',
                as_attachment=True,
                download_name=f"{filename_base}.json"
            )
        
    except Exception as e:
        return jsonify({
            'code': -1,
            'msg': f'导出数据失败: {str(e)}',
            'data': None
        }), 500

@export_bp.route('/batch', methods=['POST'])
@rate_limit
def export_batch():
    """批量导出多个表"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'code': -1,
                'msg': '请求数据为空',
                'data': None
            }), 400
        
        table_names = data.get('table_names', [])
        export_format = data.get('format', 'csv').lower()
        limit_per_table = data.get('limit_per_table', 5000)
        
        if not table_names:
            return jsonify({
                'code': -1,
                'msg': '缺少table_names参数',
                'data': None
            }), 400
        
        # 验证表名
        invalid_tables = [name for name in table_names if name not in TUSHARE_TABLES]
        if invalid_tables:
            return jsonify({
                'code': -1,
                'msg': f'不支持的表名: {", ".join(invalid_tables)}',
                'data': None
            }), 400
        
        # 限制批量导出的表数量
        if len(table_names) > 10:
            return jsonify({
                'code': -1,
                'msg': '批量导出最多支持10个表',
                'data': None
            }), 400
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            handler = TushareAPIHandler()
            exported_files = []
            
            # 导出每个表
            for table_name in table_names:
                try:
                    # 查询数据
                    result = handler.query_data(table_name, {'limit': limit_per_table})
                    
                    if result['code'] == 0 and result['data']['items']:
                        df = pd.DataFrame(result['data']['items'])
                        
                        # 保存文件
                        if export_format == 'csv':
                            file_path = os.path.join(temp_dir, f"{table_name}.csv")
                            df.to_csv(file_path, index=False, encoding='utf-8-sig')
                        elif export_format == 'excel':
                            file_path = os.path.join(temp_dir, f"{table_name}.xlsx")
                            df.to_excel(file_path, index=False)
                        elif export_format == 'json':
                            file_path = os.path.join(temp_dir, f"{table_name}.json")
                            json_data = {
                                'table_name': table_name,
                                'chinese_name': TUSHARE_TABLES[table_name]['name'],
                                'export_time': datetime.now().isoformat(),
                                'record_count': len(result['data']['items']),
                                'data': result['data']['items']
                            }
                            with open(file_path, 'w', encoding='utf-8') as f:
                                json.dump(json_data, f, ensure_ascii=False, indent=2)
                        
                        exported_files.append(file_path)
                        
                except Exception as e:
                    # 记录错误但继续处理其他表
                    print(f"导出表 {table_name} 失败: {e}")
            
            if not exported_files:
                return jsonify({
                    'code': -1,
                    'msg': '没有成功导出任何表',
                    'data': None
                }), 400
            
            # 创建ZIP文件
            zip_buffer = BytesIO()
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                for file_path in exported_files:
                    zip_file.write(file_path, os.path.basename(file_path))
            
            zip_buffer.seek(0)
            
            return send_file(
                zip_buffer,
                mimetype='application/zip',
                as_attachment=True,
                download_name=f"tushare_data_batch_{timestamp}.zip"
            )
        
    except Exception as e:
        return jsonify({
            'code': -1,
            'msg': f'批量导出失败: {str(e)}',
            'data': None
        }), 500

@export_bp.route('/template', methods=['GET'])
def get_export_template():
    """获取导出模板"""
    try:
        table_name = request.args.get('table_name')
        
        if not table_name:
            return jsonify({
                'code': -1,
                'msg': '缺少table_name参数',
                'data': None
            }), 400
        
        if table_name not in TUSHARE_TABLES:
            return jsonify({
                'code': -1,
                'msg': f'不支持的表名: {table_name}',
                'data': None
            }), 400
        
        # 获取表结构
        session = db_config.get_session()
        
        try:
            columns_result = session.execute(text(f"PRAGMA table_info({table_name})"))
            columns_info = []
            
            for row in columns_result.fetchall():
                columns_info.append({
                    'name': row[1],
                    'type': row[2],
                    'not_null': bool(row[3]),
                    'default_value': row[4],
                    'primary_key': bool(row[5])
                })
            
            # 创建空的DataFrame作为模板
            df = pd.DataFrame(columns=[col['name'] for col in columns_info])
            
            # 导出为Excel模板
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='数据', index=False)
                
                # 添加字段说明sheet
                field_info = pd.DataFrame(columns_info)
                field_info.to_excel(writer, sheet_name='字段说明', index=False)
            
            output.seek(0)
            
            return send_file(
                output,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=f"{table_name}_template.xlsx"
            )
            
        finally:
            session.close()
        
    except Exception as e:
        return jsonify({
            'code': -1,
            'msg': f'获取导出模板失败: {str(e)}',
            'data': None
        }), 500
