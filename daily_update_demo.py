#!/usr/bin/env python3
"""
日常数据更新演示
展示正常使用时的数据更新流程
"""

import sys
import os
import time
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_data_status():
    """检查当前数据状态"""
    print("🔍 检查当前数据状态...")
    
    try:
        from database.models import db_config
        from sqlalchemy import text
        
        session = db_config.get_session()
        
        # 检查数据的最新日期
        latest_daily = session.execute(text(
            "SELECT MAX(trade_date) FROM daily"
        )).scalar()
        
        latest_basic = session.execute(text(
            "SELECT MAX(trade_date) FROM daily_basic"
        )).scalar()
        
        # 检查数据总量
        total_daily = session.execute(text(
            "SELECT COUNT(*) FROM daily WHERE trade_date >= '20150101'"
        )).scalar()
        
        total_basic = session.execute(text(
            "SELECT COUNT(*) FROM daily_basic WHERE trade_date >= '20150101'"
        )).scalar()
        
        # 检查共同交易日
        common_days = session.execute(text("""
            SELECT COUNT(DISTINCT d.trade_date) 
            FROM daily d 
            INNER JOIN daily_basic db ON d.trade_date = db.trade_date 
            WHERE d.trade_date >= '20150101'
        """)).scalar()
        
        session.close()
        
        print(f"📊 数据状态:")
        print(f"   日线数据: {total_daily:,} 条，最新日期: {latest_daily}")
        print(f"   基本面数据: {total_basic:,} 条，最新日期: {latest_basic}")
        print(f"   共同交易日: {common_days:,} 天")
        
        # 判断数据是否充足
        if common_days >= 1000:
            print(f"✅ 历史数据充足，可以进行回测")
            return True, latest_daily, latest_basic
        else:
            print(f"❌ 历史数据不足，需要首次同步")
            return False, latest_daily, latest_basic
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False, None, None

def simulate_daily_update():
    """模拟日常数据更新"""
    print("\n🔄 模拟日常数据更新...")
    
    # 模拟更新最新1天的数据
    today = datetime.now().strftime('%Y%m%d')
    yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y%m%d')
    
    print(f"📅 更新日期: {yesterday}")
    print(f"⏱️  预计耗时: 2-5分钟")
    
    try:
        from sync.enhanced_data_sync import EnhancedTushareDataSync
        
        def progress_callback(data):
            progress = data.get('progress', 0)
            message = data.get('message', '')
            print(f"\r[{progress:3d}%] {message}", end='', flush=True)
            if progress >= 100:
                print()
        
        syncer = EnhancedTushareDataSync(progress_callback=progress_callback)
        
        # 模拟同步昨日数据
        print(f"📈 更新日线数据...")
        # 这里只是演示，实际会调用：
        # result = syncer.sync_table_enhanced('daily', start_date=yesterday, end_date=yesterday)
        
        print(f"📊 更新基本面数据...")
        # result = syncer.sync_table_enhanced('daily_basic', start_date=yesterday, end_date=yesterday)
        
        print(f"✅ 日常更新完成")
        return True
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        return False

def show_usage_scenarios():
    """展示使用场景"""
    print("\n📋 使用场景说明")
    print("=" * 60)
    
    print("\n🎯 场景1: 首次使用（只需一次）")
    print("   📥 下载历史数据: 2-4小时")
    print("   💾 数据永久保存在本地")
    print("   🔧 运行: python sync_missing_data.py")
    
    print("\n🎯 场景2: 日常使用（每天）")
    print("   📥 更新最新数据: 2-5分钟")
    print("   🔄 只下载昨日新增数据")
    print("   🔧 运行: python daily_update.py")
    
    print("\n🎯 场景3: 策略回测（随时）")
    print("   📊 使用本地数据: 几秒钟")
    print("   🚀 无需重新下载")
    print("   🔧 运行: python run_strategy_backtest.py")
    
    print("\n🎯 场景4: 参数优化（随时）")
    print("   🔧 测试不同参数: 几分钟")
    print("   📈 使用相同历史数据")
    print("   🔧 运行: python optimize_parameters.py")

def create_daily_update_script():
    """创建日常更新脚本"""
    print("\n📝 创建日常更新脚本...")
    
    daily_update_code = '''#!/usr/bin/env python3
"""
日常数据更新脚本
每天运行一次，更新最新的股票数据
"""

import sys
import os
from datetime import datetime, timedelta

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def update_latest_data():
    """更新最新数据"""
    print("🔄 日常数据更新")
    print("=" * 40)
    
    try:
        from sync.enhanced_data_sync import EnhancedTushareDataSync
        
        # 获取昨日日期
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y%m%d')
        
        def progress_callback(data):
            progress = data.get('progress', 0)
            message = data.get('message', '')
            print(f"\\r[{progress:3d}%] {message}", end='', flush=True)
            if progress >= 100:
                print()
        
        syncer = EnhancedTushareDataSync(progress_callback=progress_callback)
        
        # 更新日线数据
        print(f"📈 更新 {yesterday} 日线数据...")
        daily_result = syncer.sync_table_enhanced(
            'daily', 
            start_date=yesterday, 
            end_date=yesterday
        )
        
        # 更新基本面数据
        print(f"📊 更新 {yesterday} 基本面数据...")
        basic_result = syncer.sync_table_enhanced(
            'daily_basic', 
            start_date=yesterday, 
            end_date=yesterday
        )
        
        if daily_result['status'] == 'success' and basic_result['status'] == 'success':
            print(f"✅ 数据更新完成")
            print(f"   日线数据: {daily_result['record_count']:,} 条")
            print(f"   基本面数据: {basic_result['record_count']:,} 条")
        else:
            print(f"⚠️  部分数据更新失败")
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")

if __name__ == "__main__":
    update_latest_data()
'''
    
    with open('daily_update.py', 'w', encoding='utf-8') as f:
        f.write(daily_update_code)
    
    print("✅ 已创建 daily_update.py")

def show_time_comparison():
    """展示时间对比"""
    print("\n⏱️  时间对比")
    print("=" * 60)
    
    print("📊 数据下载时间:")
    print("   首次历史数据 (2015-2024): 2-4小时 (只需一次)")
    print("   日常更新 (1天): 2-5分钟")
    print("   周末补充 (2天): 5-10分钟")
    
    print("\n🚀 回测运行时间:")
    print("   10年完整回测: 1-3分钟")
    print("   参数优化 (10组): 10-30分钟")
    print("   短期回测 (1年): 10-30秒")
    
    print("\n💡 效率提升:")
    print("   首次设置后，99%的时间都是秒级或分钟级")
    print("   历史数据永久保存，无需重复下载")
    print("   可以离线进行回测和分析")

def main():
    """主函数"""
    print("📊 数据同步机制说明")
    print("=" * 60)
    
    # 检查当前数据状态
    has_data, latest_daily, latest_basic = check_data_status()
    
    # 展示使用场景
    show_usage_scenarios()
    
    # 展示时间对比
    show_time_comparison()
    
    # 创建日常更新脚本
    create_daily_update_script()
    
    print("\n" + "=" * 60)
    print("📝 总结:")
    
    if has_data:
        print("✅ 你已经有足够的历史数据")
        print("🔄 日常只需运行: python daily_update.py (2-5分钟)")
        print("🚀 回测随时运行: python run_strategy_backtest.py (1-3分钟)")
    else:
        print("❌ 需要首次同步历史数据")
        print("📥 首次运行: python sync_missing_data.py (2-4小时，只需一次)")
        print("🔄 之后每天: python daily_update.py (2-5分钟)")
    
    print("\n💡 关键点:")
    print("   • 历史数据只下载一次，永久保存")
    print("   • 日常更新很快，只下载新增数据")
    print("   • 回测使用本地数据，无需重新下载")
    print("   • 可以离线进行策略开发和测试")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户中断")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
