#!/usr/bin/env python3
"""
核实数据库字段是否正确
"""

import sys
import os
import pandas as pd
import random

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_fields():
    """核实字段"""
    print("🔍 核实数据库字段...")
    
    try:
        from database.models import db_config
        from sqlalchemy import text
        
        session = db_config.get_session()
        
        # 1. 获取所有可用的交易日
        print("\n📅 获取交易日...")
        trade_dates_sql = """
            SELECT DISTINCT trade_date FROM daily 
            WHERE trade_date >= '20240101'
            ORDER BY trade_date DESC
            LIMIT 20
        """
        trade_dates = pd.read_sql(trade_dates_sql, session.bind)
        print(f"可用交易日: {len(trade_dates)} 天")
        print(f"最近的交易日: {trade_dates['trade_date'].head(5).tolist()}")
        
        # 随机选择一个交易日
        if len(trade_dates) > 0:
            random_date = random.choice(trade_dates['trade_date'].tolist())
            print(f"\n🎲 随机选择日期: {random_date}")
            
            # 2. 检查该日期的daily表数据
            print(f"\n📊 检查 {random_date} 的daily表数据...")
            daily_sql = f"""
                SELECT * FROM daily 
                WHERE trade_date = '{random_date}'
                LIMIT 5
            """
            daily_data = pd.read_sql(daily_sql, session.bind)
            print(f"daily表记录数: {len(daily_data)}")
            
            if len(daily_data) > 0:
                print("daily表字段:")
                for col in daily_data.columns:
                    print(f"  {col}: {daily_data[col].dtype}")
                
                print("\ndaily表样本数据:")
                print(daily_data[['ts_code', 'trade_date', 'open', 'high', 'low', 'close', 'vol']].head(3).to_string())
            
            # 3. 检查该日期的daily_basic表数据
            print(f"\n📊 检查 {random_date} 的daily_basic表数据...")
            basic_sql = f"""
                SELECT * FROM daily_basic 
                WHERE trade_date = '{random_date}'
                LIMIT 5
            """
            basic_data = pd.read_sql(basic_sql, session.bind)
            print(f"daily_basic表记录数: {len(basic_data)}")
            
            if len(basic_data) > 0:
                print("daily_basic表字段:")
                for col in basic_data.columns:
                    print(f"  {col}: {basic_data[col].dtype}")
                
                print("\ndaily_basic表样本数据:")
                print(basic_data[['ts_code', 'trade_date', 'close', 'pe_ttm', 'pb', 'total_mv']].head(3).to_string())
                
                # 检查PE、PB字段的具体值
                print(f"\nPE、PB字段详细检查:")
                print(f"pe_ttm字段类型: {basic_data['pe_ttm'].dtype}")
                print(f"pb字段类型: {basic_data['pb'].dtype}")
                print(f"pe_ttm样本值: {basic_data['pe_ttm'].head(5).tolist()}")
                print(f"pb样本值: {basic_data['pb'].head(5).tolist()}")
                
                # 检查空值情况
                pe_null_count = basic_data['pe_ttm'].isnull().sum()
                pb_null_count = basic_data['pb'].isnull().sum()
                pe_empty_count = (basic_data['pe_ttm'] == '').sum() if basic_data['pe_ttm'].dtype == 'object' else 0
                pb_empty_count = (basic_data['pb'] == '').sum() if basic_data['pb'].dtype == 'object' else 0
                
                print(f"pe_ttm空值数量: {pe_null_count}")
                print(f"pb空值数量: {pb_null_count}")
                print(f"pe_ttm空字符串数量: {pe_empty_count}")
                print(f"pb空字符串数量: {pb_empty_count}")
            
            # 4. 检查数据合并情况
            if len(daily_data) > 0 and len(basic_data) > 0:
                print(f"\n🔗 检查数据合并...")
                merge_sql = f"""
                    SELECT d.ts_code, d.trade_date, d.close as daily_close, 
                           b.close as basic_close, b.pe_ttm, b.pb, b.total_mv
                    FROM daily d
                    INNER JOIN daily_basic b ON d.ts_code = b.ts_code AND d.trade_date = b.trade_date
                    WHERE d.trade_date = '{random_date}'
                    LIMIT 10
                """
                merged_data = pd.read_sql(merge_sql, session.bind)
                print(f"合并后记录数: {len(merged_data)}")
                
                if len(merged_data) > 0:
                    print("合并后样本数据:")
                    print(merged_data.to_string())
                    
                    # 检查价格是否一致
                    price_diff = (merged_data['daily_close'] - merged_data['basic_close']).abs()
                    print(f"\n价格一致性检查:")
                    print(f"价格差异最大值: {price_diff.max()}")
                    print(f"价格差异平均值: {price_diff.mean()}")
            
            # 5. 检查股票基础信息
            print(f"\n📋 检查股票基础信息...")
            if len(daily_data) > 0:
                sample_codes = daily_data['ts_code'].head(3).tolist()
                stock_info_sql = f"""
                    SELECT ts_code, name, industry, list_status, list_date
                    FROM stock_basic 
                    WHERE ts_code IN ('{"','".join(sample_codes)}')
                """
                stock_info = pd.read_sql(stock_info_sql, session.bind)
                print("股票基础信息样本:")
                print(stock_info.to_string())
        
        else:
            print("❌ 没有找到交易日数据")
        
        session.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 字段检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_strategy_fields():
    """检查策略使用的字段是否正确"""
    print("\n🎯 检查策略使用的字段...")
    
    try:
        from database.models import db_config
        
        session = db_config.get_session()
        
        # 检查策略需要的字段是否存在
        required_fields = {
            'daily': ['ts_code', 'trade_date', 'open', 'high', 'low', 'close', 'pre_close', 'vol', 'amount'],
            'daily_basic': ['ts_code', 'trade_date', 'close', 'pe_ttm', 'pb', 'total_mv'],
            'stock_basic': ['ts_code', 'name', 'industry', 'list_status'],
            'trade_cal': ['cal_date', 'is_open']
        }
        
        for table, fields in required_fields.items():
            print(f"\n检查 {table} 表字段...")
            try:
                # 获取表结构
                result = session.execute(text(f"PRAGMA table_info({table})"))
                columns = [col[1] for col in result.fetchall()]
                
                print(f"  实际字段: {columns}")
                print(f"  需要字段: {fields}")
                
                missing_fields = [field for field in fields if field not in columns]
                if missing_fields:
                    print(f"  ❌ 缺失字段: {missing_fields}")
                else:
                    print(f"  ✅ 所有字段都存在")
                    
            except Exception as e:
                print(f"  ❌ 检查 {table} 表失败: {e}")
        
        session.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 策略字段检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 数据字段核实工具")
    print("=" * 40)
    
    # 检查1: 随机日期的实际数据
    if not check_fields():
        print("❌ 字段检查失败")
        return
    
    # 检查2: 策略需要的字段
    if not check_strategy_fields():
        print("❌ 策略字段检查失败")
        return
    
    print("\n🎉 字段核实完成！")
    print("💡 请检查上述输出，确认字段使用是否正确")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户中断检查")
    except Exception as e:
        print(f"\n❌ 检查异常: {e}")
        import traceback
        traceback.print_exc()
