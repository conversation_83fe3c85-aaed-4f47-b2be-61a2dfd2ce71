# 聚宽兼容的优化策略 - 融合投资大师理念
# 基于您原有策略的改进版本

import pandas as pd
import numpy as np
from jqdata import *
import datetime

def initialize(context):
    """初始化策略"""
    # 设置基准
    set_benchmark('000300.XSHG')
    log.set_level('order', 'error')
    set_option('use_real_price', True)
    set_option('avoid_future_data', True)
    set_slippage(FixedSlippage(0.02))
    
    # === 策略参数 ===
    g.stock_num = 12        # 持股数量
    g.rebalance_days = 15   # 调仓间隔天数
    
    # 多因子权重
    g.quality_weight = 0.35   # 质量因子权重（巴菲特风格）
    g.value_weight = 0.25     # 价值因子权重（格雷厄姆风格）
    g.growth_weight = 0.25    # 成长因子权重（彼得林奇风格）
    g.momentum_weight = 0.15  # 动量因子权重（A股特色）
    
    # 风险控制参数
    g.max_single_weight = 0.12  # 单股最大权重
    g.stop_loss = -0.18         # 个股止损
    g.profit_take = 0.40        # 个股止盈
    
    # 市场环境参数
    g.market_pe_high = 18       # 市场高估值
    g.market_pe_low = 12        # 市场低估值
    g.max_position = 0.95       # 最大仓位
    g.min_position = 0.30       # 最小仓位
    
    # 记录变量
    g.position_cost = {}
    g.hold_days = {}
    g.last_rebalance = None
    g.market_env = 'normal'
    
    # 调度任务
    run_daily(before_trading_start, time='before_open')
    run_daily(market_timing, time='09:25')
    run_daily(main_strategy, time='10:00')
    run_daily(risk_management, time='14:30')
    run_daily(after_trading_end, time='after_close')

def before_trading_start(context):
    """开盘前准备"""
    # 更新持仓天数
    for stock in context.portfolio.positions:
        if stock not in g.hold_days:
            g.hold_days[stock] = 0
        g.hold_days[stock] += 1
    
    # 清理已卖出股票的记录
    current_positions = set(context.portfolio.positions.keys())
    g.position_cost = {k: v for k, v in g.position_cost.items() if k in current_positions}
    g.hold_days = {k: v for k, v in g.hold_days.items() if k in current_positions}

def market_timing(context):
    """市场择时和环境判断"""
    # 获取市场PE
    market_pe = get_market_pe()
    
    # 判断市场环境
    if market_pe > g.market_pe_high:
        g.market_env = 'expensive'
        target_position = g.min_position
    elif market_pe < g.market_pe_low:
        g.market_env = 'cheap'
        target_position = g.max_position
    else:
        g.market_env = 'normal'
        target_position = 0.75
    
    # 获取当前仓位
    current_position = get_current_position_ratio(context)
    
    log.info(f"市场PE: {market_pe:.1f}, 环境: {g.market_env}, 目标仓位: {target_position:.1%}, 当前仓位: {current_position:.1%}")
    
    # 如果需要大幅调整仓位（超过20%差异）
    if abs(target_position - current_position) > 0.20:
        adjust_position(context, target_position, current_position)

def get_market_pe():
    """获取市场PE"""
    try:
        # 获取沪深300成分股
        stocks = get_index_stocks('000300.XSHG')
        
        q = query(
            valuation.code,
            valuation.pe_ratio,
            valuation.market_cap
        ).filter(
            valuation.code.in_(stocks),
            valuation.pe_ratio > 0,
            valuation.pe_ratio < 100
        )
        
        df = get_fundamentals(q)
        
        if len(df) == 0:
            return 15.0
        
        # 市值加权PE
        total_cap = df['market_cap'].sum()
        weighted_pe = (df['market_cap'] * df['pe_ratio']).sum() / total_cap
        return weighted_pe
    except:
        return 15.0

def get_current_position_ratio(context):
    """获取当前仓位比例"""
    total_value = context.portfolio.total_value
    cash = context.portfolio.available_cash
    return 1 - cash / total_value

def adjust_position(context, target_position, current_position):
    """调整仓位"""
    if target_position < current_position:
        # 需要减仓
        reduce_ratio = (current_position - target_position) / current_position
        for stock in context.portfolio.positions:
            if context.portfolio.positions[stock].total_amount > 0:
                current_value = context.portfolio.positions[stock].value
                new_value = current_value * (1 - reduce_ratio)
                order_target_value(stock, new_value)
        log.info(f"减仓 {reduce_ratio:.1%}")

def main_strategy(context):
    """主策略逻辑"""
    # 检查是否需要调仓
    if not should_rebalance(context):
        return
    
    log.info("=== 开始选股调仓 ===")
    
    # 获取股票池
    stock_pool = get_stock_universe(context)
    
    if len(stock_pool) < g.stock_num:
        log.info(f"股票池数量不足: {len(stock_pool)}")
        return
    
    # 计算股票评分
    stock_scores = calculate_stock_scores(context, stock_pool)
    
    if len(stock_scores) == 0:
        log.info("没有有效的股票评分")
        return
    
    # 选择目标股票
    target_stocks = select_target_stocks(stock_scores)
    
    # 执行交易
    execute_trades(context, target_stocks)
    
    g.last_rebalance = context.current_dt.date()

def should_rebalance(context):
    """判断是否需要调仓"""
    if g.last_rebalance is None:
        return True
    
    days_since_rebalance = (context.current_dt.date() - g.last_rebalance).days
    return days_since_rebalance >= g.rebalance_days

def get_stock_universe(context):
    """获取股票池"""
    # 获取全部A股
    all_stocks = list(get_all_securities(['stock']).index)
    
    # 基础过滤
    current_data = get_current_data()
    valid_stocks = []
    
    for stock in all_stocks:
        data = current_data[stock]
        security_info = get_security_info(stock)
        
        # 过滤条件
        if (not data.paused and 
            not data.is_st and 
            'ST' not in data.name and
            '*' not in data.name and
            '退' not in data.name and
            stock[:2] not in ['68', '30'] and  # 排除科创板和创业板
            (context.current_dt.date() - security_info.start_date).days > 365):  # 排除次新股
            valid_stocks.append(stock)
    
    return valid_stocks[:800]  # 限制股票池大小

def calculate_stock_scores(context, stock_pool):
    """计算股票评分"""
    if len(stock_pool) == 0:
        return {}
    
    # 分批处理
    batch_size = 200
    all_scores = {}
    
    for i in range(0, len(stock_pool), batch_size):
        batch_stocks = stock_pool[i:i+batch_size]
        batch_scores = calculate_batch_scores(context, batch_stocks)
        all_scores.update(batch_scores)
    
    return all_scores

def calculate_batch_scores(context, stocks):
    """计算批次评分"""
    try:
        # 获取基本面数据
        q = query(
            valuation.code,
            valuation.pe_ratio,
            valuation.pb_ratio,
            valuation.market_cap,
            indicator.roe,
            indicator.roa,
            indicator.inc_revenue_year_on_year,
            indicator.inc_net_profit_year_on_year,
            indicator.gross_profit_margin,
            balance.total_liability,
            balance.total_assets
        ).filter(
            valuation.code.in_(stocks),
            valuation.pe_ratio > 0,
            valuation.pe_ratio < 80,
            valuation.pb_ratio > 0,
            valuation.pb_ratio < 10,
            valuation.market_cap > 50  # 市值>50亿
        )
        
        df = get_fundamentals(q)
        
        if len(df) == 0:
            return {}
        
        scores = {}
        
        for _, row in df.iterrows():
            stock = row['code']
            
            try:
                # 质量因子评分
                quality_score = calculate_quality_score(row)
                
                # 价值因子评分
                value_score = calculate_value_score(row)
                
                # 成长因子评分
                growth_score = calculate_growth_score(row)
                
                # 动量因子评分
                momentum_score = calculate_momentum_score(context, stock)
                
                # 根据市场环境调整权重
                if g.market_env == 'expensive':
                    # 高估值时偏重质量和价值
                    total_score = (quality_score * 0.45 + value_score * 0.35 + 
                                 growth_score * 0.10 + momentum_score * 0.10)
                elif g.market_env == 'cheap':
                    # 低估值时偏重成长和动量
                    total_score = (quality_score * 0.25 + value_score * 0.25 + 
                                 growth_score * 0.35 + momentum_score * 0.15)
                else:
                    # 正常市场均衡配置
                    total_score = (quality_score * g.quality_weight + 
                                 value_score * g.value_weight +
                                 growth_score * g.growth_weight + 
                                 momentum_score * g.momentum_weight)
                
                scores[stock] = {
                    'total_score': total_score,
                    'quality': quality_score,
                    'value': value_score,
                    'growth': growth_score,
                    'momentum': momentum_score
                }
                
            except Exception as e:
                continue
        
        return scores
        
    except Exception as e:
        log.error(f"计算评分出错: {e}")
        return {}

def calculate_quality_score(row):
    """计算质量因子评分（巴菲特风格）"""
    score = 0
    
    # ROE评分
    if row['roe'] > 0:
        score += min(30, row['roe'] * 1.5)
    
    # ROA评分
    if row['roa'] > 0:
        score += min(25, row['roa'] * 3)
    
    # 负债率评分
    if row['total_assets'] > 0:
        debt_ratio = row['total_liability'] / row['total_assets']
        score += max(0, 25 - debt_ratio * 25)
    
    # 毛利率评分
    if row['gross_profit_margin'] > 0:
        score += min(20, row['gross_profit_margin'] * 0.5)
    
    return min(100, score)

def calculate_value_score(row):
    """计算价值因子评分（格雷厄姆风格）"""
    score = 0
    
    # PE评分（越低越好）
    if row['pe_ratio'] > 0:
        score += max(0, 50 - (row['pe_ratio'] - 10) * 2)
    
    # PB评分（越低越好）
    if row['pb_ratio'] > 0:
        score += max(0, 50 - (row['pb_ratio'] - 1) * 15)
    
    return min(100, score)

def calculate_growth_score(row):
    """计算成长因子评分（彼得林奇风格）"""
    score = 0
    
    # 营收增长评分
    if row['inc_revenue_year_on_year'] > 0:
        score += min(40, row['inc_revenue_year_on_year'] * 1.5)
    
    # 净利润增长评分
    if row['inc_net_profit_year_on_year'] > 0:
        score += min(40, row['inc_net_profit_year_on_year'] * 1.2)
    
    # PEG评分
    if row['inc_net_profit_year_on_year'] > 0 and row['pe_ratio'] > 0:
        peg = row['pe_ratio'] / row['inc_net_profit_year_on_year']
        score += max(0, 20 - (peg - 0.5) * 10)
    
    return min(100, score)

def calculate_momentum_score(context, stock):
    """计算动量因子评分"""
    try:
        # 获取价格数据
        price_data = attribute_history(stock, 60, '1d', ['close'], skip_paused=True)
        
        if len(price_data) < 20:
            return 50  # 默认中性评分
        
        prices = price_data['close']
        
        # 计算相对强度
        returns_20d = (prices.iloc[-1] / prices.iloc[-21] - 1) if len(prices) >= 21 else 0
        
        # 获取大盘数据进行比较
        index_data = attribute_history('000300.XSHG', 60, '1d', ['close'], skip_paused=True)
        if len(index_data) >= 21:
            index_returns_20d = index_data['close'].iloc[-1] / index_data['close'].iloc[-21] - 1
            relative_strength = returns_20d - index_returns_20d
        else:
            relative_strength = returns_20d
        
        # 转换为评分
        score = 50 + relative_strength * 200  # 相对强度转换为评分
        
        # 价格趋势评分
        if len(prices) >= 10:
            ma_5 = prices.rolling(5).mean().iloc[-1]
            ma_20 = prices.rolling(20).mean().iloc[-1]
            current_price = prices.iloc[-1]
            
            if current_price > ma_5 > ma_20:
                trend_score = 20
            elif current_price > ma_20:
                trend_score = 10
            elif current_price < ma_5 < ma_20:
                trend_score = -20
            else:
                trend_score = 0
            
            score += trend_score
        
        return max(0, min(100, score))
        
    except Exception as e:
        return 50

def select_target_stocks(stock_scores):
    """选择目标股票"""
    if len(stock_scores) == 0:
        return []
    
    # 转换为DataFrame
    df = pd.DataFrame.from_dict(stock_scores, orient='index')
    
    # 按总评分排序
    df = df.sort_values('total_score', ascending=False)
    
    # 选择前N只股票
    selected = df.head(g.stock_num).index.tolist()
    
    log.info(f"选中股票数量: {len(selected)}")
    return selected

def execute_trades(context, target_stocks):
    """执行交易"""
    current_positions = set(context.portfolio.positions.keys())
    target_positions = set(target_stocks)
    
    # 卖出不在目标列表中的股票
    to_sell = current_positions - target_positions
    for stock in to_sell:
        order_target(stock, 0)
        log.info(f"卖出: {stock}")
        cleanup_stock_records(stock)
    
    # 买入新股票
    to_buy = target_positions - current_positions
    if len(to_buy) > 0:
        # 计算每只股票的目标权重
        weight_per_stock = min(0.90 / len(target_stocks), g.max_single_weight)
        total_value = context.portfolio.total_value
        target_value = total_value * weight_per_stock
        
        for stock in to_buy:
            order_target_value(stock, target_value)
            g.position_cost[stock] = get_current_data()[stock].last_price
            g.hold_days[stock] = 0
            log.info(f"买入: {stock}, 目标权重: {weight_per_stock:.2%}")

def risk_management(context):
    """风险管理"""
    current_data = get_current_data()
    
    for stock in list(context.portfolio.positions.keys()):
        position = context.portfolio.positions[stock]
        if position.total_amount == 0:
            continue
        
        cost = g.position_cost.get(stock, position.avg_cost)
        current_price = current_data[stock].last_price
        
        if current_price > 0 and cost > 0:
            return_rate = current_price / cost - 1
            
            # 止损
            if return_rate <= g.stop_loss:
                order_target(stock, 0)
                log.info(f"止损: {stock}, 亏损: {return_rate:.1%}")
                cleanup_stock_records(stock)
            
            # 止盈
            elif return_rate >= g.profit_take:
                # 部分止盈，卖出一半
                current_amount = position.total_amount
                order_target(stock, current_amount // 2)
                log.info(f"部分止盈: {stock}, 收益: {return_rate:.1%}")

def cleanup_stock_records(stock):
    """清理股票记录"""
    if stock in g.position_cost:
        del g.position_cost[stock]
    if stock in g.hold_days:
        del g.hold_days[stock]

def after_trading_end(context):
    """收盘后统计"""
    total_value = context.portfolio.total_value
    positions = len([p for p in context.portfolio.positions.values() if p.total_amount > 0])
    cash_ratio = context.portfolio.available_cash / total_value
    
    log.info(f"收盘 - 总资产: {total_value:.0f}, 持仓: {positions}只, 现金: {cash_ratio:.1%}, 市场: {g.market_env}")
