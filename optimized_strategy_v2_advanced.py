# 高级优化版量化策略 - 多因子模型
# 融合投资大师理念 + A股特色 + 机器学习思维

import pandas as pd
import numpy as np
from jqdata import *
import datetime
import talib

def initialize(context):
    """初始化策略"""
    set_benchmark('000300.XSHG')
    log.set_level('order', 'error')
    set_option('use_real_price', True)
    set_option('avoid_future_data', True)
    set_slippage(FixedSlippage(0.02))
    
    # === 策略参数 ===
    g.stock_num = 15        # 总持股数量
    g.rebalance_period = 10  # 调仓周期（交易日）
    
    # 多因子权重配置
    g.factor_weights = {
        'quality': 0.30,    # 质量因子（巴菲特风格）
        'value': 0.25,      # 价值因子（格雷厄姆风格）
        'growth': 0.25,     # 成长因子（彼得林奇风格）
        'momentum': 0.20    # 动量因子（A股特色）
    }
    
    # 风险控制参数
    g.max_single_weight = 0.08   # 单股最大权重
    g.max_industry_weight = 0.25 # 单行业最大权重
    g.stop_loss = -0.15          # 个股止损
    g.portfolio_stop_loss = -0.12 # 组合止损
    g.profit_take = 0.50         # 个股止盈
    
    # 市场环境参数
    g.market_regime = 'normal'   # 市场状态
    g.volatility_threshold = 0.02 # 波动率阈值
    
    # 数据存储
    g.position_cost = {}
    g.hold_days = {}
    g.last_rebalance = None
    g.factor_scores = {}
    
    # 调度任务
    run_daily(before_trading_start, time='before_open')
    run_daily(market_regime_detection, time='09:20')
    run_daily(main_strategy, time='10:00')
    run_daily(risk_management, time='14:30')
    run_daily(after_trading_end, time='after_close')

def before_trading_start(context):
    """开盘前准备"""
    # 更新持仓天数
    for stock in context.portfolio.positions:
        if stock not in g.hold_days:
            g.hold_days[stock] = 0
        g.hold_days[stock] += 1
    
    # 清理已卖出股票的记录
    current_positions = set(context.portfolio.positions.keys())
    g.position_cost = {k: v for k, v in g.position_cost.items() if k in current_positions}
    g.hold_days = {k: v for k, v in g.hold_days.items() if k in current_positions}

def market_regime_detection(context):
    """市场状态检测"""
    # 获取市场指标
    index_data = get_price('000300.XSHG', count=30, end_date=context.previous_date, 
                          fields=['close'], frequency='daily')
    
    if len(index_data) < 20:
        g.market_regime = 'normal'
        return
    
    # 计算市场指标
    returns = index_data['close'].pct_change().dropna()
    volatility = returns.std() * np.sqrt(252)  # 年化波动率
    
    # 计算趋势指标
    ma_short = index_data['close'].rolling(5).mean().iloc[-1]
    ma_long = index_data['close'].rolling(20).mean().iloc[-1]
    current_price = index_data['close'].iloc[-1]
    
    # 判断市场状态
    if volatility > 0.25:  # 高波动
        if current_price > ma_short > ma_long:
            g.market_regime = 'bull_volatile'
        else:
            g.market_regime = 'bear_volatile'
    elif current_price > ma_short > ma_long:
        g.market_regime = 'bull_stable'
    elif current_price < ma_short < ma_long:
        g.market_regime = 'bear_stable'
    else:
        g.market_regime = 'sideways'
    
    log.info(f"市场状态: {g.market_regime}, 波动率: {volatility:.2%}")

def main_strategy(context):
    """主策略逻辑"""
    # 检查是否需要调仓
    if not should_rebalance(context):
        return
    
    log.info("=== 开始调仓 ===")
    
    # 获取股票池
    stock_pool = get_stock_universe(context)
    
    if len(stock_pool) < g.stock_num:
        log.info(f"股票池数量不足: {len(stock_pool)}")
        return
    
    # 计算多因子评分
    factor_scores = calculate_multi_factor_scores(context, stock_pool)
    
    if len(factor_scores) == 0:
        log.info("没有有效的因子评分")
        return
    
    # 选择目标股票
    target_stocks = select_target_stocks(factor_scores)
    
    # 执行交易
    execute_trades(context, target_stocks)
    
    g.last_rebalance = context.current_dt.date()

def should_rebalance(context):
    """判断是否需要调仓"""
    if g.last_rebalance is None:
        return True
    
    days_since_rebalance = (context.current_dt.date() - g.last_rebalance).days
    
    # 根据市场状态调整调仓频率
    if g.market_regime in ['bear_volatile', 'bull_volatile']:
        return days_since_rebalance >= 5  # 高波动时更频繁调仓
    else:
        return days_since_rebalance >= g.rebalance_period

def get_stock_universe(context):
    """获取股票池"""
    # 获取全部A股
    all_stocks = list(get_all_securities(['stock']).index)
    
    # 基础过滤
    current_data = get_current_data()
    valid_stocks = []
    
    for stock in all_stocks:
        data = current_data[stock]
        security_info = get_security_info(stock)
        
        # 过滤条件
        if (not data.paused and 
            not data.is_st and 
            'ST' not in data.name and
            '*' not in data.name and
            '退' not in data.name and
            stock[:2] not in ['68', '30'] and  # 排除科创板和创业板
            (context.current_dt.date() - security_info.start_date).days > 365):  # 排除次新股
            valid_stocks.append(stock)
    
    # 流动性过滤
    if len(valid_stocks) > 1000:
        # 获取成交量数据进行流动性筛选
        volume_data = get_price(valid_stocks[:1000], count=20, end_date=context.previous_date,
                               fields=['volume'], frequency='daily')
        
        # 计算平均成交量
        avg_volumes = volume_data['volume'].mean()
        liquid_stocks = avg_volumes[avg_volumes > avg_volumes.quantile(0.3)].index.tolist()
        valid_stocks = liquid_stocks
    
    return valid_stocks[:500]  # 限制股票池大小

def calculate_multi_factor_scores(context, stock_pool):
    """计算多因子评分"""
    if len(stock_pool) == 0:
        return {}
    
    # 分批处理，避免数据量过大
    batch_size = 200
    all_scores = {}
    
    for i in range(0, len(stock_pool), batch_size):
        batch_stocks = stock_pool[i:i+batch_size]
        batch_scores = calculate_batch_scores(context, batch_stocks)
        all_scores.update(batch_scores)
    
    return all_scores

def calculate_batch_scores(context, stocks):
    """计算批次评分"""
    # 获取基本面数据
    q = query(
        valuation.code,
        valuation.pe_ratio,
        valuation.pb_ratio,
        valuation.market_cap,
        valuation.circulating_market_cap,
        indicator.roe,
        indicator.roa,
        indicator.inc_revenue_year_on_year,
        indicator.inc_net_profit_year_on_year,
        indicator.gross_profit_margin,
        balance.total_liability,
        balance.total_assets,
        cash_flow.net_operate_cash_flow,
        income.net_profit
    ).filter(
        valuation.code.in_(stocks),
        valuation.pe_ratio > 0,
        valuation.pb_ratio > 0,
        valuation.market_cap > 30  # 市值>30亿
    )
    
    df = get_fundamentals(q)
    
    if len(df) == 0:
        return {}
    
    # 计算各因子评分
    scores = {}
    
    for _, row in df.iterrows():
        stock = row['code']
        
        try:
            # 质量因子评分（巴菲特风格）
            quality_score = calculate_quality_score(row)
            
            # 价值因子评分（格雷厄姆风格）
            value_score = calculate_value_score(row)
            
            # 成长因子评分（彼得林奇风格）
            growth_score = calculate_growth_score(row)
            
            # 动量因子评分（技术分析）
            momentum_score = calculate_momentum_score(context, stock)
            
            # 综合评分
            total_score = (
                quality_score * g.factor_weights['quality'] +
                value_score * g.factor_weights['value'] +
                growth_score * g.factor_weights['growth'] +
                momentum_score * g.factor_weights['momentum']
            )
            
            scores[stock] = {
                'total_score': total_score,
                'quality': quality_score,
                'value': value_score,
                'growth': growth_score,
                'momentum': momentum_score,
                'market_cap': row['market_cap']
            }
            
        except Exception as e:
            continue
    
    return scores

def calculate_quality_score(row):
    """计算质量因子评分"""
    # ROE评分
    roe_score = min(100, max(0, row['roe'] * 5))  # ROE*5，最高100分
    
    # ROA评分
    roa_score = min(100, max(0, row['roa'] * 10))  # ROA*10，最高100分
    
    # 负债率评分
    debt_ratio = row['total_liability'] / row['total_assets'] if row['total_assets'] > 0 else 1
    debt_score = max(0, 100 - debt_ratio * 100)  # 负债率越低评分越高
    
    # 毛利率评分
    margin_score = min(100, max(0, row['gross_profit_margin'] * 2))
    
    # 现金流质量评分
    if row['net_profit'] > 0:
        cash_quality = row['net_operate_cash_flow'] / row['net_profit']
        cash_score = min(100, max(0, cash_quality * 50))
    else:
        cash_score = 0
    
    return (roe_score + roa_score + debt_score + margin_score + cash_score) / 5

def calculate_value_score(row):
    """计算价值因子评分"""
    # PE评分（越低越好）
    pe_score = max(0, 100 - (row['pe_ratio'] - 10) * 3)
    
    # PB评分（越低越好）
    pb_score = max(0, 100 - (row['pb_ratio'] - 1) * 25)
    
    return (pe_score + pb_score) / 2

def calculate_growth_score(row):
    """计算成长因子评分"""
    # 营收增长评分
    revenue_score = min(100, max(0, row['inc_revenue_year_on_year'] * 2))
    
    # 净利润增长评分
    profit_score = min(100, max(0, row['inc_net_profit_year_on_year'] * 1.5))
    
    # PEG评分
    if row['inc_net_profit_year_on_year'] > 0:
        peg = row['pe_ratio'] / row['inc_net_profit_year_on_year']
        peg_score = max(0, 100 - (peg - 0.5) * 50)
    else:
        peg_score = 0
    
    return (revenue_score + profit_score + peg_score) / 3

def calculate_momentum_score(context, stock):
    """计算动量因子评分"""
    try:
        # 获取价格数据
        price_data = get_price(stock, count=60, end_date=context.previous_date,
                              fields=['close', 'volume'], frequency='daily')
        
        if len(price_data) < 30:
            return 50  # 默认中性评分
        
        prices = price_data['close']
        volumes = price_data['volume']
        
        # 计算收益率
        returns = prices.pct_change().dropna()
        
        # 相对强度评分（与大盘比较）
        index_data = get_price('000300.XSHG', count=60, end_date=context.previous_date,
                              fields=['close'], frequency='daily')
        index_returns = index_data['close'].pct_change().dropna()
        
        if len(returns) >= len(index_returns):
            relative_strength = (returns.iloc[-20:].mean() - index_returns.iloc[-20:].mean()) * 100
        else:
            relative_strength = 0
        
        rs_score = min(100, max(0, 50 + relative_strength * 10))
        
        # 价格趋势评分
        ma_short = prices.rolling(5).mean().iloc[-1]
        ma_long = prices.rolling(20).mean().iloc[-1]
        current_price = prices.iloc[-1]
        
        if current_price > ma_short > ma_long:
            trend_score = 80
        elif current_price > ma_long:
            trend_score = 60
        elif current_price < ma_short < ma_long:
            trend_score = 20
        else:
            trend_score = 40
        
        # 成交量评分
        avg_volume = volumes.rolling(20).mean().iloc[-1]
        recent_volume = volumes.iloc[-5:].mean()
        volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1
        volume_score = min(100, max(0, 50 + (volume_ratio - 1) * 25))
        
        return (rs_score + trend_score + volume_score) / 3
        
    except Exception as e:
        return 50  # 默认中性评分

def select_target_stocks(factor_scores):
    """选择目标股票"""
    if len(factor_scores) == 0:
        return []
    
    # 转换为DataFrame便于处理
    df = pd.DataFrame.from_dict(factor_scores, orient='index')
    
    # 根据市场状态调整选股策略
    if g.market_regime in ['bear_stable', 'bear_volatile']:
        # 熊市偏重质量和价值
        df['adjusted_score'] = (
            df['quality'] * 0.4 +
            df['value'] * 0.4 +
            df['growth'] * 0.1 +
            df['momentum'] * 0.1
        )
    elif g.market_regime in ['bull_stable', 'bull_volatile']:
        # 牛市偏重成长和动量
        df['adjusted_score'] = (
            df['quality'] * 0.2 +
            df['value'] * 0.2 +
            df['growth'] * 0.3 +
            df['momentum'] * 0.3
        )
    else:
        # 震荡市均衡配置
        df['adjusted_score'] = df['total_score']
    
    # 按评分排序
    df = df.sort_values('adjusted_score', ascending=False)
    
    # 选择前N只股票
    selected = df.head(g.stock_num).index.tolist()
    
    log.info(f"选中股票: {len(selected)}只")
    return selected

def execute_trades(context, target_stocks):
    """执行交易"""
    current_positions = set(context.portfolio.positions.keys())
    target_positions = set(target_stocks)
    
    # 卖出不在目标列表中的股票
    to_sell = current_positions - target_positions
    for stock in to_sell:
        order_target_percent(stock, 0)
        log.info(f"卖出: {stock}")
        # 清理记录
        if stock in g.position_cost:
            del g.position_cost[stock]
        if stock in g.hold_days:
            del g.hold_days[stock]
    
    # 买入新股票
    to_buy = target_positions - current_positions
    if len(to_buy) > 0:
        weight_per_stock = min(0.95 / len(target_stocks), g.max_single_weight)
        
        for stock in to_buy:
            order_target_percent(stock, weight_per_stock)
            g.position_cost[stock] = get_current_data()[stock].last_price
            g.hold_days[stock] = 0
            log.info(f"买入: {stock}, 权重: {weight_per_stock:.2%}")

def risk_management(context):
    """风险管理"""
    current_data = get_current_data()
    
    for stock in list(context.portfolio.positions.keys()):
        position = context.portfolio.positions[stock]
        if position.total_amount == 0:
            continue
        
        cost = g.position_cost.get(stock, position.avg_cost)
        current_price = current_data[stock].last_price
        
        if current_price > 0 and cost > 0:
            return_rate = current_price / cost - 1
            
            # 止损
            if return_rate <= g.stop_loss:
                order_target_percent(stock, 0)
                log.info(f"止损: {stock}, 亏损: {return_rate:.1%}")
                cleanup_stock_records(stock)
            
            # 止盈
            elif return_rate >= g.profit_take:
                # 部分止盈
                current_weight = position.value / context.portfolio.total_value
                order_target_percent(stock, current_weight * 0.5)
                log.info(f"部分止盈: {stock}, 收益: {return_rate:.1%}")

def cleanup_stock_records(stock):
    """清理股票记录"""
    if stock in g.position_cost:
        del g.position_cost[stock]
    if stock in g.hold_days:
        del g.hold_days[stock]

def after_trading_end(context):
    """收盘后统计"""
    total_value = context.portfolio.total_value
    positions = len([p for p in context.portfolio.positions.values() if p.total_amount > 0])
    cash_ratio = context.portfolio.available_cash / total_value
    
    log.info(f"收盘 - 总资产: {total_value:.0f}, 持仓: {positions}只, 现金比例: {cash_ratio:.1%}, 市场状态: {g.market_regime}")
