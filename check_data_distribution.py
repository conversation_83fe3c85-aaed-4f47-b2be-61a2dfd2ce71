#!/usr/bin/env python3
"""
检查数据分布情况
分析为什么共同交易日只有19天
"""

import sys
import os
import pandas as pd

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def analyze_data_distribution():
    """分析数据分布"""
    print("🔍 分析数据分布情况")
    print("=" * 60)
    
    try:
        from database.models import db_config
        from sqlalchemy import text
        
        session = db_config.get_session()
        
        # 1. 检查日线数据的实际分布
        print("\n📈 日线数据分布分析:")
        
        # 按年份统计日线数据
        daily_by_year = session.execute(text("""
            SELECT 
                SUBSTR(trade_date, 1, 4) as year,
                COUNT(*) as count,
                MIN(trade_date) as min_date,
                MAX(trade_date) as max_date
            FROM daily 
            WHERE trade_date >= '20150101'
            GROUP BY SUBSTR(trade_date, 1, 4)
            ORDER BY year
        """)).fetchall()
        
        print("   年份分布:")
        total_daily = 0
        for year, count, min_date, max_date in daily_by_year:
            print(f"     {year}: {count:,} 条记录 ({min_date} - {max_date})")
            total_daily += count
        
        print(f"   总计: {total_daily:,} 条日线记录")
        
        # 2. 检查基本面数据的实际分布
        print("\n📊 基本面数据分布分析:")
        
        basic_by_year = session.execute(text("""
            SELECT 
                SUBSTR(trade_date, 1, 4) as year,
                COUNT(*) as count,
                MIN(trade_date) as min_date,
                MAX(trade_date) as max_date
            FROM daily_basic 
            WHERE trade_date >= '20150101'
            GROUP BY SUBSTR(trade_date, 1, 4)
            ORDER BY year
        """)).fetchall()
        
        print("   年份分布:")
        total_basic = 0
        for year, count, min_date, max_date in basic_by_year:
            print(f"     {year}: {count:,} 条记录 ({min_date} - {max_date})")
            total_basic += count
        
        print(f"   总计: {total_basic:,} 条基本面记录")
        
        # 3. 检查共同日期
        print("\n🔗 共同日期分析:")
        
        common_dates = session.execute(text("""
            SELECT 
                d.trade_date,
                COUNT(DISTINCT d.ts_code) as daily_stocks,
                COUNT(DISTINCT db.ts_code) as basic_stocks
            FROM daily d
            INNER JOIN daily_basic db ON d.trade_date = db.trade_date
            WHERE d.trade_date >= '20150101'
            GROUP BY d.trade_date
            ORDER BY d.trade_date
        """)).fetchall()
        
        print(f"   共同交易日数量: {len(common_dates)}")
        
        if len(common_dates) > 0:
            print("   最近10个共同交易日:")
            for date, daily_stocks, basic_stocks in common_dates[-10:]:
                print(f"     {date}: 日线{daily_stocks:,}只股票, 基本面{basic_stocks:,}只股票")
        
        # 4. 检查数据缺口
        print("\n🕳️  数据缺口分析:")
        
        # 检查2015-2024年是否有数据
        for year in range(2015, 2025):
            daily_count = session.execute(text(f"""
                SELECT COUNT(*) FROM daily 
                WHERE trade_date >= '{year}0101' AND trade_date <= '{year}1231'
            """)).scalar()
            
            basic_count = session.execute(text(f"""
                SELECT COUNT(*) FROM daily_basic 
                WHERE trade_date >= '{year}0101' AND trade_date <= '{year}1231'
            """)).scalar()
            
            status = "✅" if daily_count > 0 and basic_count > 0 else "❌"
            print(f"   {status} {year}年: 日线{daily_count:,}条, 基本面{basic_count:,}条")
        
        session.close()
        
        # 5. 分析问题原因
        print("\n🔍 问题分析:")
        if len(common_dates) < 100:
            print("   ❌ 主要问题: 基本面数据严重缺失")
            print("   📊 日线数据可能有历史数据，但基本面数据只有最近的")
            print("   🔧 解决方案: 需要同步完整的历史基本面数据")
        
        return len(common_dates)
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return 0

def check_sync_status():
    """检查同步状态"""
    print("\n📋 检查数据同步状态...")
    
    try:
        from database.models import db_config
        from sqlalchemy import text
        
        session = db_config.get_session()
        
        # 检查是否有同步状态表
        try:
            sync_status = session.execute(text("""
                SELECT table_name, last_sync_date, sync_status, record_count
                FROM sync_status
                ORDER BY last_sync_time DESC
            """)).fetchall()
            
            if sync_status:
                print("   最近同步记录:")
                for table, last_date, status, count in sync_status[:10]:
                    print(f"     {table}: {last_date} ({status}) - {count:,}条")
            else:
                print("   ⚠️  没有同步状态记录")
                
        except Exception:
            print("   ⚠️  同步状态表不存在或为空")
        
        session.close()
        
    except Exception as e:
        print(f"❌ 同步状态检查失败: {e}")

def suggest_solution():
    """建议解决方案"""
    print("\n💡 解决方案建议:")
    print("=" * 60)
    
    print("\n问题根源:")
    print("   📊 日线数据: 可能有部分历史数据")
    print("   📈 基本面数据: 只有最近1个月的数据")
    print("   🔗 共同数据: 只有19天完整数据")
    
    print("\n解决步骤:")
    print("   1️⃣  重新同步基本面历史数据 (daily_basic)")
    print("   2️⃣  确保日线数据完整性 (daily)")
    print("   3️⃣  验证数据匹配情况")
    
    print("\n具体操作:")
    print("   🔄 运行: python sync_missing_data.py")
    print("   📊 重点同步: 2015-2024年的基本面数据")
    print("   ⏱️  预计时间: 2-4小时")

def main():
    """主函数"""
    print("🔍 数据分布分析工具")
    print("=" * 60)
    
    # 分析数据分布
    common_days = analyze_data_distribution()
    
    # 检查同步状态
    check_sync_status()
    
    # 建议解决方案
    suggest_solution()
    
    print("\n" + "=" * 60)
    if common_days < 100:
        print("❌ 数据严重不足，需要重新同步历史数据")
        print("💡 主要问题是基本面数据缺失，不是代码逻辑问题")
    else:
        print("✅ 数据基本充足，可以进行回测")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户中断分析")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
