# 聚宽策略：大盘择时+价值选股（修正版）
# 修正了市净率计算和止盈参数

def initialize(context):
    """初始化函数"""
    # 设定沪深300作为基准
    set_benchmark('000300.XSHG')
    
    # 设置交易成本
    set_order_cost(OrderCost(
        close_tax=0.001,        # 印花税1‰
        open_commission=0.0003, # 买入佣金万3
        close_commission=0.0003,# 卖出佣金万3
        min_commission=5        # 最低佣金5元
    ), type='stock')
    
    # 策略参数
    g.market_pb_buy = 2.0      # 大盘PB买入阈值（PB>2.0时不买入）
    g.market_pb_full = 1.2     # 大盘PB满仓阈值（PB<1.2时满仓）
    g.max_stocks = 20          # 最大持股数量
    g.sell_half_return = 0.5   # 减半卖出收益率（涨50%减半）
    g.sell_all_return = 1.0    # 清仓收益率（涨100%清仓）
    
    # 持仓成本记录
    g.position_cost = {}
    
    # 运行函数
    run_daily(trade, time='09:30')
    run_daily(after_market_close, time='after_close')

def get_market_pb():
    """获取A股市场整体市净率 = 总市值 / 总净资产"""
    try:
        # 方法1：尝试获取全市场数据
        q = query(
            valuation.code,
            valuation.market_cap,
            balance.total_owner_equities
        ).filter(
            valuation.market_cap > 0,
            balance.total_owner_equities > 0
        )
        
        df = get_fundamentals(q)
        
        if len(df) > 1000:  # 如果数据足够多
            # 计算整个市场的PB = 总市值 / 总净资产
            total_market_cap = df['market_cap'].sum() * 100000000  # 亿元转元
            total_net_assets = df['total_owner_equities'].sum()    # 净资产（元）
            
            if total_net_assets > 0:
                market_pb = total_market_cap / total_net_assets
                return market_pb
    
    except:
        pass
    
    # 方法2：如果上面失败，使用沪深300作为代表
    try:
        stocks = get_index_stocks('000300.XSHG')
        
        q = query(
            valuation.code,
            valuation.market_cap,
            valuation.pb_ratio
        ).filter(
            valuation.code.in_(stocks),
            valuation.pb_ratio > 0,
            valuation.pb_ratio < 10
        )
        
        df = get_fundamentals(q)
        
        if len(df) > 0:
            # 市值加权平均PB
            total_cap = df['market_cap'].sum()
            weighted_pb = (df['market_cap'] * df['pb_ratio']).sum() / total_cap
            return weighted_pb
    
    except:
        pass
    
    # 默认值
    return 2.0

def get_target_position(market_pb):
    """根据大盘PB确定目标仓位"""
    if market_pb >= g.market_pb_buy:
        return 0.0  # PB过高，不买入
    elif market_pb <= g.market_pb_full:
        return 1.0  # PB很低，满仓
    else:
        # 线性插值
        ratio = (g.market_pb_buy - market_pb) / (g.market_pb_buy - g.market_pb_full)
        return max(0, min(1, ratio))

def get_stock_pool():
    """获取股票池"""
    # 获取全部A股
    stocks = list(get_all_securities(['stock']).index)
    
    # 过滤条件
    current_data = get_current_data()
    
    valid_stocks = []
    for stock in stocks:
        data = current_data[stock]
        if (not data.paused and 
            not data.is_st and 
            'ST' not in data.name and
            '*' not in data.name):
            valid_stocks.append(stock)
    
    return valid_stocks

def calculate_scores(stocks):
    """计算股票评分"""
    if len(stocks) == 0:
        return []
    
    # 获取基本面数据
    q = query(
        valuation.code,
        valuation.pe_ratio,
        valuation.pb_ratio,
        valuation.market_cap
    ).filter(
        valuation.code.in_(stocks),
        valuation.pe_ratio > 0,
        valuation.pe_ratio < 100,
        valuation.pb_ratio > 0,
        valuation.pb_ratio < 10,
        valuation.market_cap > 50
    )
    
    df = get_fundamentals(q)
    
    if len(df) == 0:
        return []
    
    scores = []
    for _, row in df.iterrows():
        stock = row['code']
        
        try:
            # 获取历史价格数据
            hist_data = attribute_history(stock, 250, '1d', ['close'], skip_paused=True)
            
            if len(hist_data) < 50:
                continue
                
            current_price = hist_data['close'][-1]
            min_price = hist_data['close'].min()
            return_from_low = (current_price / min_price - 1) if min_price > 0 else 0
            
            # PE评分 (越低越好，10分最高，50分最低)
            pe_score = max(0, 100 - (row['pe_ratio'] - 10) * 100 / (50 - 10))
            
            # PB评分 (越低越好，0.5分最高，3分最低)  
            pb_score = max(0, 100 - (row['pb_ratio'] - 0.5) * 100 / (3 - 0.5))
            
            # 涨幅评分 (涨幅越小越好，0%最高，100%最低)
            return_score = max(0, 100 - return_from_low * 100)
            
            # 总分
            total_score = pe_score + pb_score + return_score
            
            scores.append({
                'stock': stock,
                'score': total_score,
                'pe': row['pe_ratio'],
                'pb': row['pb_ratio'],
                'return': return_from_low
            })
            
        except Exception as e:
            continue
    
    # 按评分排序
    scores.sort(key=lambda x: x['score'], reverse=True)
    return scores

def check_sell_signals(context):
    """检查个股止盈信号"""
    sells = []
    current_data = get_current_data()
    
    for stock in list(context.portfolio.positions.keys()):
        position = context.portfolio.positions[stock]
        if position.total_amount == 0:
            continue
            
        # 获取成本价
        cost = g.position_cost.get(stock, position.avg_cost)
        current_price = current_data[stock].last_price
        
        if current_price > 0 and cost > 0:
            return_rate = current_price / cost - 1
            
            if return_rate >= g.sell_all_return:
                # 涨100%清仓
                sells.append({'stock': stock, 'action': 'all', 'return': return_rate})
            elif return_rate >= g.sell_half_return:
                # 涨50%减半
                sells.append({'stock': stock, 'action': 'half', 'return': return_rate})
    
    return sells

def trade(context):
    """主交易函数"""
    # 1. 获取大盘PB和目标仓位
    market_pb = get_market_pb()
    target_pos = get_target_position(market_pb)
    
    log.info(f"大盘PB: {market_pb:.2f}, 目标仓位: {target_pos:.1%}")
    
    # 2. 检查个股止盈信号
    sells = check_sell_signals(context)
    
    # 执行卖出
    for sell in sells:
        stock = sell['stock']
        if sell['action'] == 'all':
            order_target(stock, 0)
            if stock in g.position_cost:
                del g.position_cost[stock]
            log.info(f"清仓 {stock}, 收益: {sell['return']:.1%}")
        elif sell['action'] == 'half':
            current_amount = context.portfolio.positions[stock].total_amount
            order_target(stock, current_amount // 2)
            log.info(f"减半 {stock}, 收益: {sell['return']:.1%}")
    
    # 3. 根据大盘PB控制整体仓位
    if target_pos <= 0:
        # 大盘PB过高，清仓
        for stock in context.portfolio.positions:
            order_target(stock, 0)
        g.position_cost.clear()
        log.info("大盘PB过高，清仓")
        return
    
    # 4. 选股和买入
    stock_pool = get_stock_pool()
    scores = calculate_scores(stock_pool[:300])  # 限制计算范围提高速度
    
    if len(scores) == 0:
        log.info("没有符合条件的股票")
        return
    
    # 选择前N只股票
    selected = scores[:g.max_stocks]
    
    # 计算每只股票的目标金额
    total_value = context.portfolio.total_value
    target_value_per_stock = total_value * target_pos / len(selected)
    
    # 执行买入
    buy_count = 0
    for stock_info in selected:
        stock = stock_info['stock']
        
        # 如果已经持有，跳过
        if context.portfolio.positions[stock].total_amount > 0:
            continue
        
        # 买入
        order_target_value(stock, target_value_per_stock)
        
        # 记录成本
        current_price = get_current_data()[stock].last_price
        g.position_cost[stock] = current_price
        
        buy_count += 1
        
        if buy_count >= 5:  # 每次最多买入5只
            break
    
    log.info(f"买入 {buy_count} 只股票")

def after_market_close(context):
    """收盘后运行"""
    total_value = context.portfolio.total_value
    positions = len([p for p in context.portfolio.positions.values() if p.total_amount > 0])
    
    log.info(f"收盘 - 总资产: {total_value:.0f}, 持仓: {positions}只")

# 策略说明：
# 1. 大盘择时：根据整个A股市场PB控制仓位
#    - PB > 2.0：不买入（熊市）
#    - PB < 1.2：满仓（牛市底部）
#    - 1.2 < PB < 2.0：线性仓位
#
# 2. 个股选择：PE、PB、涨幅综合评分
#    - PE越低越好（价值股）
#    - PB越低越好（净资产折价）
#    - 涨幅越小越好（避免追高）
#
# 3. 止盈策略：
#    - 涨50%减半仓位
#    - 涨100%清仓
#
# 4. 风险控制：
#    - 最多持有20只股票
#    - 每次最多买入5只（避免冲击）
