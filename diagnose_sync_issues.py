#!/usr/bin/env python3
"""
数据同步问题诊断和修复工具
确保与Tushare数据库完全一致
"""

import sys
import os
import pandas as pd
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_tushare_connection():
    """检查Tushare连接"""
    print("🔍 检查Tushare API连接...")
    
    try:
        import tushare as ts
        from config import get_config
        
        config = get_config()
        if not config.TUSHARE_TOKEN:
            print("❌ TUSHARE_TOKEN未配置")
            return False
        
        ts.set_token(config.TUSHARE_TOKEN)
        pro = ts.pro_api()
        
        # 测试基础API调用
        df = pro.stock_basic(limit=1)
        if df.empty:
            print("❌ Tushare API返回空数据")
            return False
        
        print(f"✅ Tushare API连接正常，测试返回 {len(df)} 条记录")
        print(f"📊 股票基础信息字段: {list(df.columns)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Tushare API连接失败: {e}")
        return False

def check_database_structure():
    """检查数据库结构"""
    print("\n🔍 检查数据库结构...")
    
    try:
        from database.models import db_config
        from sqlalchemy import text
        
        session = db_config.get_session()
        
        try:
            # 检查所有表
            result = session.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
            tables = [row[0] for row in result.fetchall()]
            
            print(f"✅ 数据库连接正常，发现 {len(tables)} 个表")
            
            # 检查主要表的记录数
            from config import TUSHARE_TABLES
            
            for table_name in TUSHARE_TABLES.keys():
                if table_name in tables:
                    try:
                        result = session.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                        count = result.scalar()
                        print(f"📊 {table_name}: {count:,} 条记录")
                    except Exception as e:
                        print(f"❌ {table_name}: 查询失败 - {e}")
                else:
                    print(f"⚠️  {table_name}: 表不存在")
            
            return True
            
        finally:
            session.close()
            
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

def compare_with_tushare():
    """与Tushare数据对比"""
    print("\n🔍 与Tushare数据对比...")
    
    try:
        import tushare as ts
        from config import get_config, TUSHARE_TABLES
        from database.models import db_config
        from sqlalchemy import text
        
        config = get_config()
        ts.set_token(config.TUSHARE_TOKEN)
        pro = ts.pro_api()
        
        session = db_config.get_session()
        
        try:
            # 检查股票基础信息
            print("📊 检查股票基础信息...")
            try:
                tushare_df = pro.stock_basic()
                tushare_count = len(tushare_df)
                
                result = session.execute(text("SELECT COUNT(*) FROM stock_basic"))
                local_count = result.scalar()
                
                print(f"  Tushare: {tushare_count:,} 条")
                print(f"  本地: {local_count:,} 条")
                print(f"  差异: {tushare_count - local_count:,} 条")
                
                if tushare_count != local_count:
                    print("  ⚠️  数据量不一致，需要重新同步")
                else:
                    print("  ✅ 数据量一致")
                    
            except Exception as e:
                print(f"  ❌ 股票基础信息对比失败: {e}")
            
            # 检查交易日历
            print("\n📊 检查交易日历...")
            try:
                # 检查最近一年的交易日历
                start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
                end_date = datetime.now().strftime('%Y%m%d')
                
                tushare_df = pro.trade_cal(start_date=start_date, end_date=end_date)
                tushare_count = len(tushare_df)
                
                result = session.execute(text(f"""
                    SELECT COUNT(*) FROM trade_cal 
                    WHERE cal_date >= '{start_date}' AND cal_date <= '{end_date}'
                """))
                local_count = result.scalar()
                
                print(f"  Tushare ({start_date}-{end_date}): {tushare_count:,} 条")
                print(f"  本地 ({start_date}-{end_date}): {local_count:,} 条")
                print(f"  差异: {tushare_count - local_count:,} 条")
                
                if tushare_count != local_count:
                    print("  ⚠️  交易日历数据不一致")
                else:
                    print("  ✅ 交易日历数据一致")
                    
            except Exception as e:
                print(f"  ❌ 交易日历对比失败: {e}")
            
            # 检查日线行情（最近一个交易日）
            print("\n📊 检查日线行情...")
            try:
                # 获取最近的交易日
                trade_cal_df = pro.trade_cal(start_date='20241201', end_date='20241231')
                recent_trade_dates = trade_cal_df[trade_cal_df['is_open'] == 1]['cal_date'].tolist()
                
                if recent_trade_dates:
                    latest_date = recent_trade_dates[-1]
                    
                    tushare_df = pro.daily(trade_date=latest_date)
                    tushare_count = len(tushare_df)
                    
                    result = session.execute(text(f"""
                        SELECT COUNT(*) FROM daily WHERE trade_date = '{latest_date}'
                    """))
                    local_count = result.scalar()
                    
                    print(f"  Tushare ({latest_date}): {tushare_count:,} 条")
                    print(f"  本地 ({latest_date}): {local_count:,} 条")
                    print(f"  差异: {tushare_count - local_count:,} 条")
                    
                    if tushare_count != local_count:
                        print("  ⚠️  日线行情数据不一致")
                    else:
                        print("  ✅ 日线行情数据一致")
                else:
                    print("  ⚠️  无法获取最近交易日")
                    
            except Exception as e:
                print(f"  ❌ 日线行情对比失败: {e}")
            
        finally:
            session.close()
            
    except Exception as e:
        print(f"❌ 数据对比失败: {e}")

def check_sync_status():
    """检查同步状态"""
    print("\n🔍 检查同步状态...")
    
    try:
        from database.models import db_config, SyncStatus
        
        session = db_config.get_session()
        
        try:
            sync_records = session.query(SyncStatus).all()
            
            if not sync_records:
                print("⚠️  没有同步状态记录")
                return
            
            print("📊 同步状态记录:")
            for record in sync_records:
                status_icon = {
                    'success': '✅',
                    'error': '❌',
                    'syncing': '🔄'
                }.get(record.sync_status, '❓')
                
                last_sync = record.last_sync_time.strftime('%Y-%m-%d %H:%M') if record.last_sync_time else '从未同步'
                
                print(f"  {status_icon} {record.table_name}: {record.sync_status} | "
                      f"记录数: {record.record_count or 0} | 最后同步: {last_sync}")
                
                if record.error_message:
                    print(f"    错误: {record.error_message}")
            
        finally:
            session.close()
            
    except Exception as e:
        print(f"❌ 同步状态检查失败: {e}")

def fix_sync_issues():
    """修复同步问题"""
    print("\n🔧 开始修复同步问题...")
    
    try:
        from sync.enhanced_data_sync import EnhancedTushareDataSync
        from config import TUSHARE_TABLES
        
        def progress_callback(data):
            progress = data.get('progress', 0)
            message = data.get('message', '')
            table_name = data.get('table_name', '')
            print(f"\r[{progress:3d}%] {table_name}: {message}", end='', flush=True)
            if progress >= 100:
                print()
        
        syncer = EnhancedTushareDataSync(progress_callback=progress_callback)
        
        # 优先同步基础数据
        priority_tables = ['stock_basic', 'trade_cal', 'index_basic']
        
        print("🔄 同步基础数据表...")
        for table_name in priority_tables:
            if table_name in TUSHARE_TABLES:
                print(f"\n📊 同步 {table_name} ({TUSHARE_TABLES[table_name]['name']})...")
                
                try:
                    result = syncer.sync_table_enhanced(table_name, force_full=True)
                    
                    if result['status'] == 'success':
                        print(f"✅ {table_name} 同步成功: {result['record_count']:,} 条记录")
                    else:
                        print(f"❌ {table_name} 同步失败: {result.get('error_message', '未知错误')}")
                        
                except Exception as e:
                    print(f"❌ {table_name} 同步异常: {e}")
        
        # 同步最近的行情数据
        print("\n🔄 同步最近行情数据...")
        recent_tables = ['daily', 'daily_basic']
        
        # 只同步最近30天的数据
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
        
        for table_name in recent_tables:
            if table_name in TUSHARE_TABLES:
                print(f"\n📊 同步 {table_name} ({start_date}-{end_date})...")
                
                try:
                    result = syncer.sync_table_enhanced(
                        table_name, 
                        start_date=start_date, 
                        end_date=end_date
                    )
                    
                    if result['status'] == 'success':
                        print(f"✅ {table_name} 同步成功: {result['record_count']:,} 条记录")
                    else:
                        print(f"❌ {table_name} 同步失败: {result.get('error_message', '未知错误')}")
                        
                except Exception as e:
                    print(f"❌ {table_name} 同步异常: {e}")
        
        print("\n🎉 同步修复完成！")
        
    except Exception as e:
        print(f"❌ 同步修复失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔧 Tushare数据同步诊断和修复工具")
    print("=" * 50)
    
    # 1. 检查Tushare连接
    if not check_tushare_connection():
        print("\n❌ Tushare连接失败，请检查配置")
        return False
    
    # 2. 检查数据库结构
    if not check_database_structure():
        print("\n❌ 数据库检查失败")
        return False
    
    # 3. 与Tushare数据对比
    compare_with_tushare()
    
    # 4. 检查同步状态
    check_sync_status()
    
    # 5. 询问是否修复
    print("\n" + "=" * 50)
    response = input("🤔 是否需要修复同步问题？(y/N): ").strip().lower()
    
    if response in ['y', 'yes']:
        fix_sync_issues()
        
        # 重新检查
        print("\n" + "=" * 50)
        print("🔍 修复后重新检查...")
        compare_with_tushare()
    
    print("\n✅ 诊断完成！")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
