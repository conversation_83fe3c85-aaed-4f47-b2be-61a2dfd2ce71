#!/usr/bin/env python3
"""
测试修复后的回测系统
使用现有数据验证所有修复是否正常工作
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_short_period_backtest():
    """测试短期回测"""
    print("🧪 测试修复后的回测系统")
    print("=" * 60)
    
    try:
        from strategy_backtest import StrategyBacktest
        
        # 创建回测实例
        backtest = StrategyBacktest()
        
        # 设置较短的回测期间（使用有数据的时间段）
        backtest.start_date = '20250708'  # 基本面数据开始日期
        backtest.end_date = '20250801'    # 基本面数据结束日期
        
        print(f"📅 测试期间: {backtest.start_date} - {backtest.end_date}")
        print(f"💰 初始资金: {backtest.initial_capital:,} 元")
        
        # 显示优化参数
        print(f"\n🔧 优化参数:")
        print(f"   数据延迟: {backtest.data_delay_days} 天")
        print(f"   佣金费率: {backtest.commission_rate*100:.3f}%")
        print(f"   印花税率: {backtest.stamp_tax_rate*100:.3f}%")
        print(f"   滑点率: {backtest.slippage_rate*100:.3f}%")
        print(f"   最大成交量占比: {backtest.max_volume_ratio*100:.1f}%")
        
        # 运行回测
        print(f"\n🚀 开始回测...")
        start_time = datetime.now()
        
        if backtest.run_backtest():
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            print(f"\n✅ 回测完成，耗时 {duration:.1f} 秒")
            
            # 显示关键结果
            if backtest.daily_returns:
                final_return = backtest.daily_returns[-1]
                total_return = final_return['cumulative_return'] * 100
                
                print(f"\n📊 回测结果摘要:")
                print(f"   总收益率: {total_return:.2f}%")
                print(f"   最终资金: {final_return['portfolio_value']:,.0f} 元")
                print(f"   交易次数: {len(backtest.trade_records)}")
                print(f"   最终持仓: {final_return['position_count']} 只股票")
                
                # 检查动态股票池功能
                if 'tradable_stocks_count' in final_return:
                    print(f"   最终可交易股票: {final_return['tradable_stocks_count']} 只")
                
                # 检查退市处理
                delisting_trades = [t for t in backtest.trade_records if t.get('action') == 'delisting']
                if delisting_trades:
                    print(f"   退市处理: {len(delisting_trades)} 次")
                
                # 检查交易成本
                buy_trades = [t for t in backtest.trade_records if t.get('action') == 'buy']
                if buy_trades:
                    avg_cost = sum(t.get('trading_cost', 0) for t in buy_trades) / len(buy_trades)
                    print(f"   平均买入成本: {avg_cost:.2f} 元")
            
            # 生成报告
            backtest.generate_report()
            
            return True
        else:
            print("❌ 回测失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_key_fixes():
    """验证关键修复点"""
    print("\n🔍 验证关键修复点...")
    
    try:
        from strategy_backtest import StrategyBacktest
        
        backtest = StrategyBacktest()
        
        # 加载数据
        from database.models import db_config
        import pandas as pd
        
        session = db_config.get_session()
        backtest.stock_basic = pd.read_sql("SELECT * FROM stock_basic", session.bind)
        session.close()
        
        print(f"\n1. ✅ 动态股票池功能:")
        
        # 测试不同日期的股票池
        test_dates = ['20150101', '20200101', '20250801']
        for date in test_dates:
            tradable = backtest.get_tradable_stocks_on_date(date)
            print(f"   {date}: {len(tradable):,} 只股票可交易")
        
        print(f"\n2. ✅ 股票状态分布:")
        status_counts = backtest.stock_basic['list_status'].value_counts()
        for status, count in status_counts.items():
            status_name = {'L': '正常上市', 'D': '已退市'}.get(status, status)
            print(f"   {status} ({status_name}): {count:,} 只")
        
        print(f"\n3. ✅ 交易成本计算:")
        test_amount = 100000
        buy_cost, commission, stamp_tax, slippage = backtest.calculate_trading_cost(test_amount, False)
        sell_cost, _, _, _ = backtest.calculate_trading_cost(test_amount, True)
        print(f"   买入10万元成本: {buy_cost:.2f} 元 ({buy_cost/test_amount*100:.3f}%)")
        print(f"   卖出10万元成本: {sell_cost:.2f} 元 ({sell_cost/test_amount*100:.3f}%)")
        
        print(f"\n4. ✅ 数据延迟设置:")
        print(f"   基本面数据延迟: {backtest.data_delay_days} 天")
        
        print(f"\n5. ✅ 流动性约束:")
        print(f"   最大成交量占比: {backtest.max_volume_ratio*100:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 修复后回测系统测试")
    print("=" * 60)
    
    # 1. 验证关键修复点
    if not verify_key_fixes():
        print("❌ 关键修复点验证失败")
        return
    
    # 2. 运行短期回测测试
    print("\n" + "=" * 60)
    if test_short_period_backtest():
        print("\n🎉 所有测试通过！")
        print("\n📝 修复总结:")
        print("✅ 解决了股票池动态变化问题")
        print("✅ 修复了前瞻性偏差")
        print("✅ 添加了完整的交易成本")
        print("✅ 考虑了流动性约束")
        print("✅ 改进了数据质量处理")
        print("✅ 优化了仓位管理逻辑")
        print("✅ 处理了退市股票")
        
        print("\n💡 现在可以用更完整的数据运行长期回测了！")
        print("   建议继续同步更多历史数据以获得更准确的结果。")
    else:
        print("\n❌ 测试失败，需要进一步检查")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户中断测试")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
