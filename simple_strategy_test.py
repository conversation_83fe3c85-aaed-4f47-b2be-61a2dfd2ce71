#!/usr/bin/env python3
"""
简单策略测试
"""

import sys
import os
import pandas as pd
import numpy as np

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试基本功能...")
    
    try:
        from database.models import db_config
        from sqlalchemy import text
        
        session = db_config.get_session()
        
        # 获取少量数据进行测试
        print("  📊 获取测试数据...")
        
        # 获取最近的交易日
        trade_cal_sql = """
            SELECT cal_date FROM trade_cal 
            WHERE is_open = 1 AND cal_date >= '20240101'
            ORDER BY cal_date DESC LIMIT 5
        """
        trade_dates = pd.read_sql(trade_cal_sql, session.bind)['cal_date'].tolist()
        print(f"    测试交易日: {trade_dates}")
        
        if not trade_dates:
            print("    ❌ 没有找到交易日数据")
            return False
        
        test_date = trade_dates[0]
        print(f"    使用测试日期: {test_date}")
        
        # 获取该日期的股票数据
        daily_sql = f"""
            SELECT ts_code, trade_date, close, open, high, low, vol, amount
            FROM daily 
            WHERE trade_date = '{test_date}'
            LIMIT 100
        """
        daily_data = pd.read_sql(daily_sql, session.bind)
        print(f"    日线数据: {len(daily_data)} 条")
        
        # 获取基本面数据
        basic_sql = f"""
            SELECT ts_code, trade_date, pe_ttm, pb
            FROM daily_basic 
            WHERE trade_date = '{test_date}'
            LIMIT 100
        """
        basic_data = pd.read_sql(basic_sql, session.bind)
        print(f"    基本面数据: {len(basic_data)} 条")
        
        session.close()
        
        if len(daily_data) == 0 or len(basic_data) == 0:
            print("    ⚠️  数据不足，无法进行测试")
            return False
        
        # 测试数据合并
        print("  🔧 测试数据处理...")
        
        # 确保日期格式一致
        daily_data['trade_date'] = daily_data['trade_date'].astype(str)
        basic_data['trade_date'] = basic_data['trade_date'].astype(str)
        
        # 合并数据
        merged_data = pd.merge(
            daily_data, 
            basic_data, 
            on=['ts_code', 'trade_date'], 
            how='inner'
        )
        print(f"    合并后数据: {len(merged_data)} 条")
        
        if len(merged_data) == 0:
            print("    ❌ 数据合并失败")
            return False
        
        # 转换数据类型
        merged_data['pe_ttm'] = pd.to_numeric(merged_data['pe_ttm'], errors='coerce')
        merged_data['pb'] = pd.to_numeric(merged_data['pb'], errors='coerce')
        merged_data['close'] = pd.to_numeric(merged_data['close'], errors='coerce')
        
        # 过滤有效数据
        valid_data = merged_data[
            (merged_data['pe_ttm'] > 0) & 
            (merged_data['pb'] > 0) & 
            (merged_data['close'] > 0)
        ]
        print(f"    有效数据: {len(valid_data)} 条")
        
        if len(valid_data) == 0:
            print("    ❌ 没有有效数据")
            return False
        
        # 测试评分计算
        print("  📊 测试评分计算...")
        
        # PE评分：25为0分，10为100分
        valid_data['pe_score'] = np.clip(
            100 - (valid_data['pe_ttm'] - 10) * 100 / (25 - 10), 0, 100
        )
        
        # PB评分：2.5为0分，1为100分
        valid_data['pb_score'] = np.clip(
            100 - (valid_data['pb'] - 1) * 100 / (2.5 - 1), 0, 100
        )
        
        # 涨幅评分（简化：使用随机值）
        valid_data['return_score'] = np.random.uniform(0, 100, len(valid_data))
        
        # 总分
        valid_data['total_score'] = (
            valid_data['pe_score'] + 
            valid_data['pb_score'] + 
            valid_data['return_score']
        )
        
        # 排序
        scored_data = valid_data.sort_values('total_score', ascending=False)
        
        print(f"    评分完成，最高分: {scored_data.iloc[0]['total_score']:.1f}")
        print(f"    最高分股票: {scored_data.iloc[0]['ts_code']}")
        
        # 测试大盘PB计算
        market_pb = valid_data['pb'].median()
        print(f"    大盘PB中位数: {market_pb:.2f}")
        
        print("  ✅ 基本功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_strategy_logic():
    """测试策略逻辑"""
    print("\n🧪 测试策略逻辑...")
    
    try:
        # 模拟数据
        np.random.seed(42)
        n_stocks = 50
        
        # 创建模拟股票数据
        stock_data = pd.DataFrame({
            'ts_code': [f'{i:06d}.SZ' for i in range(n_stocks)],
            'pe_ttm': np.random.uniform(5, 50, n_stocks),
            'pb': np.random.uniform(0.5, 5, n_stocks),
            'return_from_low': np.random.uniform(0, 1, n_stocks),
            'close': np.random.uniform(10, 100, n_stocks),
            'industry': np.random.choice(['银行', '地产', '科技', '医药', '消费'], n_stocks)
        })
        
        print(f"  📊 模拟数据: {len(stock_data)} 只股票")
        
        # 计算评分
        print("  📊 计算评分...")
        
        # PE评分：25为0分，10为100分
        stock_data['pe_score'] = np.clip(
            100 - (stock_data['pe_ttm'] - 10) * 100 / (25 - 10), 0, 100
        )
        
        # PB评分：2.5为0分，1为100分
        stock_data['pb_score'] = np.clip(
            100 - (stock_data['pb'] - 1) * 100 / (2.5 - 1), 0, 100
        )
        
        # 涨幅评分：50%为0分，0%为100分
        stock_data['return_score'] = np.clip(
            100 - stock_data['return_from_low'] * 100 / 0.5, 0, 100
        )
        
        # 总分
        stock_data['total_score'] = (
            stock_data['pe_score'] + 
            stock_data['pb_score'] + 
            stock_data['return_score']
        )
        
        # 排序
        scored_stocks = stock_data.sort_values('total_score', ascending=False)
        
        print(f"    最高分: {scored_stocks.iloc[0]['total_score']:.1f}")
        print(f"    最低分: {scored_stocks.iloc[-1]['total_score']:.1f}")
        
        # 测试选股逻辑
        print("  🎯 测试选股逻辑...")
        
        target_weight = 0.5  # 50%仓位
        max_industry_weight = 0.03  # 3%行业限制
        
        selected_stocks = []
        industry_weights = {}
        total_weight = 0
        
        for _, stock in scored_stocks.iterrows():
            industry = stock['industry']
            
            # 检查行业权重限制
            current_industry_weight = industry_weights.get(industry, 0)
            if current_industry_weight >= max_industry_weight:
                continue
            
            # 计算可分配权重
            available_weight = min(
                target_weight - total_weight,
                max_industry_weight - current_industry_weight,
                0.02  # 单只股票最大权重2%
            )
            
            if available_weight > 0.005:  # 最小权重0.5%
                selected_stocks.append({
                    'ts_code': stock['ts_code'],
                    'weight': available_weight,
                    'score': stock['total_score'],
                    'industry': industry
                })
                
                industry_weights[industry] = current_industry_weight + available_weight
                total_weight += available_weight
                
                if total_weight >= target_weight * 0.95:  # 95%仓位即可
                    break
        
        print(f"    选中股票: {len(selected_stocks)} 只")
        print(f"    总权重: {total_weight:.1%}")
        print(f"    行业分布: {len(industry_weights)} 个行业")
        
        # 显示选中的股票
        for i, stock in enumerate(selected_stocks[:5]):
            print(f"      {i+1}. {stock['ts_code']} ({stock['industry']}) - 权重: {stock['weight']:.1%}, 分数: {stock['score']:.1f}")
        
        print("  ✅ 策略逻辑测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 策略逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 简单策略测试")
    print("=" * 40)
    
    # 测试1: 基本功能
    if not test_basic_functionality():
        print("❌ 基本功能测试失败")
        return
    
    # 测试2: 策略逻辑
    if not test_strategy_logic():
        print("❌ 策略逻辑测试失败")
        return
    
    print("\n🎉 所有测试通过！")
    print("✅ 策略系统基本功能正常")
    print("\n💡 现在可以尝试运行完整回测:")
    print("   python strategy_backtest.py")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
