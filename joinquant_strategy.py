# 聚宽量化策略：大盘择时+多因子选股
# 策略概述：
# 1. 大盘择时：市净率2倍买入，1.2倍满仓
# 2. 个股评分：PE、PB、涨幅三因子评分
# 3. 风险控制：行业分散，单行业不超过3%
# 4. 止盈策略：涨幅100%减半，200%清仓

import pandas as pd
import numpy as np
from jqdata import *

def initialize(context):
    """初始化函数"""
    # 策略参数
    g.market_pb_buy = 2.0      # 大盘市净率买入阈值
    g.market_pb_full = 1.2     # 大盘市净率满仓阈值
    g.max_industry_weight = 0.03  # 单行业最大权重3%
    g.sell_half_return = 1.0   # 减半卖出涨幅100%
    g.sell_all_return = 2.0    # 清仓涨幅200%
    
    # 交易成本设置
    set_order_cost(OrderCost(
        close_tax=0.001,      # 印花税1‰
        open_commission=0.0003,  # 买入佣金万3
        close_commission=0.0003, # 卖出佣金万3
        min_commission=5      # 最低佣金5元
    ), type='stock')
    
    # 基准设置
    g.benchmark = '000300.XSHG'  # 沪深300
    set_benchmark(g.benchmark)
    
    # 股票池：A股主板+中小板+创业板
    g.stock_pool = []
    
    # 持仓记录
    g.position_info = {}  # {stock: {'cost': 成本价, 'buy_date': 买入日期}}
    
    # 运行设置
    run_daily(trade, time='09:30')  # 每日开盘后运行
    run_daily(after_market_close, time='after_close')  # 收盘后运行
    
    log.info("策略初始化完成")

def before_trading_start(context):
    """开盘前运行"""
    # 更新股票池（排除ST、退市等）
    g.stock_pool = get_stock_pool()
    
    # 获取大盘市净率
    g.market_pb = get_market_pb()
    
    log.info(f"今日股票池: {len(g.stock_pool)}只, 大盘PB: {g.market_pb:.2f}")

def get_stock_pool():
    """获取股票池"""
    # 获取所有A股
    stocks = list(get_all_securities(['stock']).index)
    
    # 过滤条件
    current_data = get_current_data()
    
    valid_stocks = []
    for stock in stocks:
        data = current_data[stock]
        
        # 排除停牌、ST、退市等
        if (not data.paused and 
            not data.is_st and 
            'ST' not in data.name and 
            '*' not in data.name and
            '退' not in data.name):
            valid_stocks.append(stock)
    
    return valid_stocks

def get_market_pb():
    """计算大盘市净率"""
    try:
        # 使用沪深300作为大盘代表
        index_stocks = get_index_stocks('000300.XSHG')
        
        # 获取市值和PB数据
        q = query(
            valuation.code,
            valuation.market_cap,
            valuation.pb_ratio
        ).filter(
            valuation.code.in_(index_stocks),
            valuation.pb_ratio > 0
        )
        
        df = get_fundamentals(q)
        
        if len(df) == 0:
            return 2.0  # 默认值
        
        # 计算加权平均PB
        total_market_cap = df['market_cap'].sum()
        weighted_pb = (df['market_cap'] * df['pb_ratio']).sum() / total_market_cap
        
        return weighted_pb
        
    except Exception as e:
        log.error(f"计算大盘PB失败: {e}")
        return 2.0

def get_target_position_weight():
    """根据大盘市净率确定目标仓位"""
    market_pb = g.market_pb
    
    if market_pb >= g.market_pb_buy:
        return 0.0  # 不买入
    elif market_pb <= g.market_pb_full:
        return 1.0  # 满仓
    else:
        # 线性插值
        weight = (g.market_pb_buy - market_pb) / (g.market_pb_buy - g.market_pb_full)
        return min(max(weight, 0), 1)

def calculate_stock_scores():
    """计算股票评分"""
    if len(g.stock_pool) == 0:
        return pd.DataFrame()
    
    try:
        # 获取基本面数据
        q = query(
            valuation.code,
            valuation.pe_ratio,
            valuation.pb_ratio,
            income.total_operating_revenue
        ).filter(
            valuation.code.in_(g.stock_pool),
            valuation.pe_ratio > 0,
            valuation.pb_ratio > 0
        )
        
        df = get_fundamentals(q)
        
        if len(df) == 0:
            return pd.DataFrame()
        
        # 获取价格数据计算涨幅
        prices = get_price(df['code'].tolist(), count=250, end_date=context.current_dt, fields=['close'])
        
        stock_scores = []
        
        for _, row in df.iterrows():
            stock = row['code']
            
            try:
                # 计算一年内最低价和涨幅
                stock_prices = prices['close'][stock].dropna()
                if len(stock_prices) == 0:
                    continue
                    
                current_price = stock_prices.iloc[-1]
                min_price_1y = stock_prices.min()
                return_from_low = (current_price / min_price_1y - 1) if min_price_1y > 0 else 0
                
                # PE评分：25为0分，10为100分
                pe_score = max(0, min(100, 100 - (row['pe_ratio'] - 10) * 100 / (25 - 10)))
                
                # PB评分：2.5为0分，1为100分
                pb_score = max(0, min(100, 100 - (row['pb_ratio'] - 1) * 100 / (2.5 - 1)))
                
                # 涨幅评分：50%为0分，0%为100分
                return_score = max(0, min(100, 100 - return_from_low * 100 / 0.5))
                
                # 总分
                total_score = pe_score + pb_score + return_score
                
                stock_scores.append({
                    'stock': stock,
                    'pe_ratio': row['pe_ratio'],
                    'pb_ratio': row['pb_ratio'],
                    'return_from_low': return_from_low,
                    'pe_score': pe_score,
                    'pb_score': pb_score,
                    'return_score': return_score,
                    'total_score': total_score
                })
                
            except Exception as e:
                continue
        
        return pd.DataFrame(stock_scores).sort_values('total_score', ascending=False)
        
    except Exception as e:
        log.error(f"计算股票评分失败: {e}")
        return pd.DataFrame()

def select_stocks(scored_stocks, target_weight):
    """选股逻辑"""
    if target_weight <= 0 or len(scored_stocks) == 0:
        return []
    
    selected_stocks = []
    industry_weights = {}
    total_weight = 0
    
    # 获取行业信息
    stocks_list = scored_stocks['stock'].tolist()
    industries = get_industry(stocks_list)
    
    for _, stock_data in scored_stocks.iterrows():
        stock = stock_data['stock']
        
        # 获取行业
        industry = industries.get(stock, {}).get('sw_l1', {}).get('industry_name', '其他')
        
        # 检查行业权重限制
        current_industry_weight = industry_weights.get(industry, 0)
        if current_industry_weight >= g.max_industry_weight:
            continue
        
        # 计算可分配权重
        available_weight = min(
            target_weight - total_weight,
            g.max_industry_weight - current_industry_weight,
            0.02  # 单只股票最大权重2%
        )
        
        if available_weight > 0.005:  # 最小权重0.5%
            selected_stocks.append({
                'stock': stock,
                'weight': available_weight,
                'score': stock_data['total_score'],
                'industry': industry
            })
            
            industry_weights[industry] = current_industry_weight + available_weight
            total_weight += available_weight
            
            if total_weight >= target_weight * 0.95:  # 95%仓位即可
                break
    
    return selected_stocks

def check_sell_signals():
    """检查卖出信号"""
    sell_orders = []
    current_data = get_current_data()
    
    for stock in context.portfolio.positions:
        position = context.portfolio.positions[stock]
        if position.total_amount == 0:
            continue
        
        # 获取持仓信息
        position_info = g.position_info.get(stock, {})
        cost_price = position_info.get('cost', position.avg_cost)
        
        # 获取当前价格
        current_price = current_data[stock].last_price
        
        if current_price > 0 and cost_price > 0:
            return_rate = (current_price / cost_price - 1)
            
            if return_rate >= g.sell_all_return:
                # 清仓
                sell_orders.append({
                    'stock': stock,
                    'action': 'sell_all',
                    'amount': position.total_amount,
                    'return': return_rate
                })
            elif return_rate >= g.sell_half_return:
                # 减半
                sell_orders.append({
                    'stock': stock,
                    'action': 'sell_half',
                    'amount': position.total_amount // 2,
                    'return': return_rate
                })
    
    return sell_orders

def trade(context):
    """交易函数"""
    # 1. 检查卖出信号
    sell_orders = check_sell_signals()
    
    # 执行卖出
    for sell_order in sell_orders:
        stock = sell_order['stock']
        amount = sell_order['amount']
        
        if amount > 0:
            order_target_value(stock, 0 if sell_order['action'] == 'sell_all' 
                             else context.portfolio.positions[stock].value / 2)
            
            log.info(f"卖出 {stock}: {sell_order['action']}, 收益率: {sell_order['return']:.2%}")
    
    # 2. 计算目标仓位
    target_weight = get_target_position_weight()
    
    if target_weight <= 0:
        # 清仓
        for stock in context.portfolio.positions:
            order_target_percent(stock, 0)
        log.info(f"大盘PB过高({g.market_pb:.2f})，清仓")
        return
    
    # 3. 选股
    scored_stocks = calculate_stock_scores()
    selected_stocks = select_stocks(scored_stocks, target_weight)
    
    if len(selected_stocks) == 0:
        log.info("没有符合条件的股票")
        return
    
    # 4. 执行买入
    for stock_info in selected_stocks:
        stock = stock_info['stock']
        weight = stock_info['weight']
        
        # 下单
        order_target_percent(stock, weight)
        
        # 记录持仓信息
        current_price = get_current_data()[stock].last_price
        g.position_info[stock] = {
            'cost': current_price,
            'buy_date': context.current_dt.date()
        }
    
    log.info(f"目标仓位: {target_weight:.1%}, 选中股票: {len(selected_stocks)}只")

def after_market_close(context):
    """收盘后运行"""
    # 记录当日信息
    total_value = context.portfolio.total_value
    positions_count = len([p for p in context.portfolio.positions.values() if p.total_amount > 0])
    
    log.info(f"收盘 - 总资产: {total_value:.0f}, 持仓数: {positions_count}, 大盘PB: {g.market_pb:.2f}")

# 风险管理
def handle_data(context, data):
    """实时数据处理"""
    pass

def process_initialize(context):
    """进程初始化"""
    pass

def after_code_changed(context):
    """代码更新后"""
    pass
