#!/usr/bin/env python3
"""
Tushare数据镜像系统启动脚本
一键启动完整系统
"""

import sys
import os
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def print_welcome():
    """打印欢迎信息"""
    print("🎉 欢迎使用 Tushare数据镜像系统 v1.0.0")
    print("=" * 60)
    print("一个完整的Tushare数据镜像解决方案")
    print("=" * 60)

def check_system():
    """检查系统状态"""
    print("\n🔍 检查系统状态...")
    
    try:
        # 检查数据库
        from database.init_db import check_database
        tables_info = check_database()
        
        if tables_info:
            data_count = sum(count for _, count in tables_info if isinstance(count, int))
            print(f"✅ 数据库正常 - {len(tables_info)} 个表，{data_count:,} 条记录")
        else:
            print("⚠️  数据库未初始化")
            return False
        
        # 检查配置
        from config import get_config
        config = get_config()
        
        if config.TUSHARE_TOKEN and len(config.TUSHARE_TOKEN) > 10:
            print("✅ Tushare Token 已配置")
        else:
            print("⚠️  Tushare Token 未配置或无效")
        
        return True
        
    except Exception as e:
        print(f"❌ 系统检查失败: {e}")
        return False

def initialize_system():
    """初始化系统"""
    print("\n🔧 初始化系统...")
    
    try:
        from database.init_db import init_database
        
        if init_database():
            print("✅ 数据库初始化成功")
            return True
        else:
            print("❌ 数据库初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ 系统初始化失败: {e}")
        return False

def start_web_server():
    """启动Web服务器"""
    print("\n🌐 启动Web服务器...")
    
    try:
        from app import app, config
        
        print(f"🚀 服务器启动在端口 {config.WEB_CONFIG['port']}")
        print("\n📱 访问地址:")
        print(f"  • 主页: http://localhost:{config.WEB_CONFIG['port']}")
        print(f"  • 用户管理: http://localhost:{config.WEB_CONFIG['port']}/user")
        print(f"  • API文档: http://localhost:{config.WEB_CONFIG['port']}/docs")
        
        print("\n💡 使用提示:")
        print("  1. 首先访问用户管理页面注册账户")
        print("  2. 获取API Token用于数据查询")
        print("  3. 在主页进行数据同步")
        print("  4. 通过API查询数据")
        
        print("\n⌨️  按 Ctrl+C 停止服务器")
        print("=" * 60)
        
        app.run(
            debug=config.WEB_CONFIG['debug'],
            host=config.WEB_CONFIG['host'],
            port=config.WEB_CONFIG['port']
        )
        
    except KeyboardInterrupt:
        print("\n👋 服务器已停止，感谢使用！")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")

def show_quick_commands():
    """显示快速命令"""
    print("\n📚 常用命令:")
    print("  python sync_cli.py status           # 查看系统状态")
    print("  python sync_cli.py sync --table stock_basic  # 同步股票基础信息")
    print("  python sync_cli.py sync-daily       # 每日增量同步")
    print("  python sync_cli.py list-tables      # 查看支持的数据表")

def main():
    """主函数"""
    print_welcome()
    
    # 检查系统状态
    if not check_system():
        print("\n🔧 系统需要初始化...")
        
        choice = input("是否现在初始化系统？(y/N): ").lower().strip()
        if choice == 'y':
            if not initialize_system():
                print("❌ 初始化失败，请检查配置")
                return False
        else:
            print("💡 请先运行: python sync_cli.py init")
            return False
    
    # 显示快速命令
    show_quick_commands()
    
    # 询问是否启动Web服务器
    print("\n🚀 准备启动Web服务器...")
    choice = input("现在启动吗？(Y/n): ").lower().strip()
    
    if choice != 'n':
        start_web_server()
    else:
        print("\n💡 随时运行以下命令启动:")
        print("  python app.py")
        print("  或")
        print("  python start.py")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 已取消启动")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        sys.exit(1)
