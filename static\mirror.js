// Tushare数据镜像系统前端脚本

// WebSocket连接
const socket = io();

// 全局变量
let isSyncing = false;
let tables = [];
let databaseStatus = {};

// DOM元素
const startSyncBtn = document.getElementById('startSync');
const stopSyncBtn = document.getElementById('stopSync');
const refreshStatusBtn = document.getElementById('refreshStatus');
const tableSelect = document.getElementById('tableSelect');
const startDateInput = document.getElementById('startDate');
const endDateInput = document.getElementById('endDate');
const forceFullCheckbox = document.getElementById('forceFull');
const progressBar = document.getElementById('progressBar');
const currentStatus = document.getElementById('currentStatus');
const currentTable = document.getElementById('currentTable');
const completedTables = document.getElementById('completedTables');
const totalTablesSync = document.getElementById('totalTablesSync');
const totalRecordsSync = document.getElementById('totalRecordsSync');
const logContainer = document.getElementById('logContainer');

// 状态概览元素
const totalTablesEl = document.getElementById('totalTables');
const syncedTablesEl = document.getElementById('syncedTables');
const totalRecordsEl = document.getElementById('totalRecords');
const syncStatusEl = document.getElementById('syncStatus');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    setupEventListeners();
    loadTables();
    loadDatabaseStatus();
});

// 初始化页面
function initializePage() {
    // 设置默认日期
    const today = new Date();
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
    
    startDateInput.value = formatDate(lastMonth);
    endDateInput.value = formatDate(today);
}

// 设置事件监听器
function setupEventListeners() {
    startSyncBtn.addEventListener('click', startSync);
    stopSyncBtn.addEventListener('click', stopSync);
    refreshStatusBtn.addEventListener('click', refreshStatus);
    
    // WebSocket事件
    socket.on('sync_status', handleSyncStatus);
    socket.on('sync_log', handleSyncLog);
    socket.on('connect', function() {
        console.log('WebSocket连接成功');
        addLog('WebSocket连接成功', 'info');
    });
    socket.on('disconnect', function() {
        console.log('WebSocket连接断开');
        addLog('WebSocket连接断开', 'warning');
    });
}

// 加载数据表列表
async function loadTables() {
    try {
        const response = await fetch('/api/tables');
        const data = await response.json();
        
        tables = data;
        updateTableSelect();
        totalTablesEl.textContent = data.length;
        
    } catch (error) {
        console.error('加载数据表失败:', error);
        addLog('加载数据表失败: ' + error.message, 'error');
    }
}

// 更新表选择下拉框
function updateTableSelect() {
    tableSelect.innerHTML = '<option value="all">所有表</option>';
    
    tables.forEach(table => {
        const option = document.createElement('option');
        option.value = table.name;
        option.textContent = `${table.name} (${table.chinese_name})`;
        tableSelect.appendChild(option);
    });
}

// 加载数据库状态
async function loadDatabaseStatus() {
    try {
        const response = await fetch('/api/database_status');
        const data = await response.json();
        
        databaseStatus = data;
        updateStatusOverview();
        
    } catch (error) {
        console.error('加载数据库状态失败:', error);
        addLog('加载数据库状态失败: ' + error.message, 'error');
    }
}

// 更新状态概览
function updateStatusOverview() {
    if (!databaseStatus.tables) return;
    
    const totalRecords = databaseStatus.tables.reduce((sum, table) => sum + (table.record_count || 0), 0);
    const syncedCount = Object.keys(databaseStatus.sync_info || {}).length;
    
    totalRecordsEl.textContent = formatNumber(totalRecords);
    syncedTablesEl.textContent = syncedCount;
    
    // 更新同步状态
    const hasErrors = Object.values(databaseStatus.sync_info || {}).some(info => info.sync_status === 'error');
    const hasSuccess = Object.values(databaseStatus.sync_info || {}).some(info => info.sync_status === 'success');
    
    if (hasErrors) {
        syncStatusEl.textContent = '有错误';
        syncStatusEl.className = 'card-text fs-4 text-danger';
    } else if (hasSuccess) {
        syncStatusEl.textContent = '正常';
        syncStatusEl.className = 'card-text fs-4 text-success';
    } else {
        syncStatusEl.textContent = '待同步';
        syncStatusEl.className = 'card-text fs-4 text-warning';
    }
}

// 开始同步
async function startSync() {
    if (isSyncing) return;
    
    const tableName = tableSelect.value;
    const startDate = startDateInput.value;
    const endDate = endDateInput.value;
    const forceFull = forceFullCheckbox.checked;
    
    try {
        const response = await fetch('/api/start_sync', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                table_name: tableName,
                start_date: startDate,
                end_date: endDate,
                force_full: forceFull
            })
        });
        
        const result = await response.json();
        
        if (response.ok) {
            addLog(result.message, 'success');
            updateSyncButtons(true);
        } else {
            addLog('启动同步失败: ' + result.error, 'error');
        }
        
    } catch (error) {
        console.error('启动同步失败:', error);
        addLog('启动同步失败: ' + error.message, 'error');
    }
}

// 停止同步
async function stopSync() {
    try {
        const response = await fetch('/api/stop_sync', {
            method: 'POST'
        });
        
        const result = await response.json();
        addLog(result.message, 'warning');
        
    } catch (error) {
        console.error('停止同步失败:', error);
        addLog('停止同步失败: ' + error.message, 'error');
    }
}

// 刷新状态
function refreshStatus() {
    loadDatabaseStatus();
    addLog('状态已刷新', 'info');
}

// 处理同步状态更新
function handleSyncStatus(status) {
    isSyncing = status.is_running;
    
    // 更新进度条
    progressBar.style.width = status.progress + '%';
    progressBar.textContent = Math.round(status.progress) + '%';
    
    // 更新状态信息
    currentStatus.textContent = getStatusText(status.status);
    currentTable.textContent = status.current_table || '-';
    completedTables.textContent = status.tables_completed || 0;
    totalTablesSync.textContent = status.total_tables || 0;
    totalRecordsSync.textContent = formatNumber(status.total_records || 0);
    
    // 更新按钮状态
    updateSyncButtons(status.is_running);
    
    // 更新进度条样式
    updateProgressBarStyle(status.status);
}

// 处理同步日志
function handleSyncLog(log) {
    addLog(log.message, log.type);
}

// 更新同步按钮状态
function updateSyncButtons(isRunning) {
    if (isRunning) {
        startSyncBtn.classList.add('d-none');
        stopSyncBtn.classList.remove('d-none');
        progressBar.classList.add('progress-bar-animated');
    } else {
        startSyncBtn.classList.remove('d-none');
        stopSyncBtn.classList.add('d-none');
        progressBar.classList.remove('progress-bar-animated');
    }
}

// 更新进度条样式
function updateProgressBarStyle(status) {
    progressBar.className = 'progress-bar progress-bar-striped';
    
    switch (status) {
        case 'completed':
            progressBar.classList.add('bg-success');
            break;
        case 'error':
            progressBar.classList.add('bg-danger');
            break;
        case 'syncing':
            progressBar.classList.add('bg-primary', 'progress-bar-animated');
            break;
        default:
            progressBar.classList.add('bg-info');
    }
}

// 获取状态文本
function getStatusText(status) {
    const statusMap = {
        'waiting': '等待开始',
        'starting': '正在启动',
        'syncing': '同步中',
        'completed': '同步完成',
        'stopped': '已停止',
        'error': '同步错误'
    };
    
    return statusMap[status] || status;
}

// 添加日志
function addLog(message, type = 'info') {
    if (!logContainer) return;
    
    const logEntry = document.createElement('div');
    logEntry.className = `alert alert-${getBootstrapClass(type)} alert-dismissible fade show`;
    logEntry.innerHTML = `
        <small class="text-muted">${new Date().toLocaleTimeString()}</small>
        <span class="ms-2">${message}</span>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    logContainer.insertBefore(logEntry, logContainer.firstChild);
    
    // 限制日志数量
    while (logContainer.children.length > 50) {
        logContainer.removeChild(logContainer.lastChild);
    }
}

// 获取Bootstrap样式类
function getBootstrapClass(type) {
    const classMap = {
        'info': 'info',
        'success': 'success',
        'warning': 'warning',
        'error': 'danger'
    };
    
    return classMap[type] || 'info';
}

// 格式化日期
function formatDate(date) {
    return date.toISOString().split('T')[0];
}

// 格式化数字
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

// 显示数据库状态模态框
function showDatabaseStatus() {
    // 这里可以实现一个详细的数据库状态模态框
    alert('数据库状态功能待实现');
}
