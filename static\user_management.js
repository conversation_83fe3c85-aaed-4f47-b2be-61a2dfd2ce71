// 用户管理页面JavaScript

// 全局变量
let currentUser = null;
let userToken = localStorage.getItem('tushare_token');

// DOM元素
const authForms = document.getElementById('authForms');
const userStatusCards = document.getElementById('userStatusCards');
const userPanel = document.getElementById('userPanel');
const apiExamples = document.getElementById('apiExamples');
const messageContainer = document.getElementById('messageContainer');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
    checkUserStatus();
});

// 设置事件监听器
function setupEventListeners() {
    // 登录表单
    document.getElementById('loginForm').addEventListener('submit', handleLogin);
    
    // 注册表单
    document.getElementById('registerForm').addEventListener('submit', handleRegister);
    
    // 修改密码表单
    document.getElementById('changePasswordForm').addEventListener('submit', handleChangePassword);
}

// 检查用户状态
async function checkUserStatus() {
    if (userToken) {
        try {
            const response = await fetch('/auth/profile', {
                headers: {
                    'Authorization': userToken
                }
            });
            
            const result = await response.json();
            
            if (result.code === 0) {
                currentUser = result.data;
                showUserPanel();
            } else {
                // Token无效，清除本地存储
                localStorage.removeItem('tushare_token');
                userToken = null;
                showAuthForms();
            }
        } catch (error) {
            console.error('检查用户状态失败:', error);
            showAuthForms();
        }
    } else {
        showAuthForms();
    }
}

// 显示认证表单
function showAuthForms() {
    authForms.style.display = 'block';
    userStatusCards.style.display = 'none';
    userPanel.style.display = 'none';
    apiExamples.style.display = 'none';
}

// 显示用户面板
function showUserPanel() {
    authForms.style.display = 'none';
    userStatusCards.style.display = 'flex';
    userPanel.style.display = 'block';
    apiExamples.style.display = 'block';
    
    updateUserInfo();
    updateAPIExamples();
}

// 更新用户信息显示
function updateUserInfo() {
    if (!currentUser) return;
    
    // 更新状态卡片
    document.getElementById('currentUsername').textContent = currentUser.username;
    document.getElementById('currentToken').textContent = currentUser.api_token.substring(0, 20) + '...';
    document.getElementById('todayCalls').textContent = currentUser.api_calls_today;
    document.getElementById('accountType').textContent = currentUser.is_premium ? '高级用户' : '免费用户';
    
    // 更新使用进度
    const usagePercent = (currentUser.api_calls_today / currentUser.api_limit_daily) * 100;
    const progressBar = document.getElementById('usageProgress');
    progressBar.style.width = usagePercent + '%';
    progressBar.textContent = Math.round(usagePercent) + '%';
    
    // 更新使用统计
    document.getElementById('usedCalls').textContent = currentUser.api_calls_today;
    document.getElementById('totalCalls').textContent = currentUser.api_limit_daily;
    
    // 更新账户信息
    document.getElementById('registerTime').textContent = formatDateTime(currentUser.created_at);
    document.getElementById('lastLogin').textContent = formatDateTime(currentUser.last_login);
    
    // 更新Token显示
    document.getElementById('displayToken').value = currentUser.api_token;
}

// 更新API示例
function updateAPIExamples() {
    if (!currentUser) return;
    
    const token = currentUser.api_token;
    
    // 更新Python示例
    const pythonExample = document.getElementById('pythonExample');
    pythonExample.textContent = pythonExample.textContent.replace('your_token_here', token);
    
    // 更新JavaScript示例
    const jsExample = document.getElementById('jsExample');
    jsExample.textContent = jsExample.textContent.replace('your_token_here', token);
}

// 处理登录
async function handleLogin(event) {
    event.preventDefault();
    
    const username = document.getElementById('loginUsername').value;
    const password = document.getElementById('loginPassword').value;
    
    try {
        const response = await fetch('/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: username,
                password: password
            })
        });
        
        const result = await response.json();
        
        if (result.code === 0) {
            currentUser = result.data;
            userToken = result.data.api_token;
            localStorage.setItem('tushare_token', userToken);
            
            showMessage('登录成功！', 'success');
            showUserPanel();
            
            // 清空表单
            document.getElementById('loginForm').reset();
        } else {
            showMessage('登录失败: ' + result.msg, 'danger');
        }
    } catch (error) {
        showMessage('登录请求失败: ' + error.message, 'danger');
    }
}

// 处理注册
async function handleRegister(event) {
    event.preventDefault();
    
    const username = document.getElementById('registerUsername').value;
    const email = document.getElementById('registerEmail').value;
    const password = document.getElementById('registerPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    
    // 验证密码确认
    if (password !== confirmPassword) {
        showMessage('两次输入的密码不一致', 'danger');
        return;
    }
    
    try {
        const response = await fetch('/auth/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: username,
                email: email,
                password: password
            })
        });
        
        const result = await response.json();
        
        if (result.code === 0) {
            currentUser = result.data;
            userToken = result.data.api_token;
            localStorage.setItem('tushare_token', userToken);
            
            showMessage('注册成功！已自动登录', 'success');
            showUserPanel();
            
            // 清空表单
            document.getElementById('registerForm').reset();
        } else {
            showMessage('注册失败: ' + result.msg, 'danger');
        }
    } catch (error) {
        showMessage('注册请求失败: ' + error.message, 'danger');
    }
}

// 处理修改密码
async function handleChangePassword(event) {
    event.preventDefault();
    
    const oldPassword = document.getElementById('oldPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    
    try {
        const response = await fetch('/auth/change_password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                old_password: oldPassword,
                new_password: newPassword
            })
        });
        
        const result = await response.json();
        
        if (result.code === 0) {
            showMessage('密码修改成功！', 'success');
            document.getElementById('changePasswordForm').reset();
        } else {
            showMessage('密码修改失败: ' + result.msg, 'danger');
        }
    } catch (error) {
        showMessage('密码修改请求失败: ' + error.message, 'danger');
    }
}

// 重置Token
async function resetToken() {
    if (!confirm('确定要重置API Token吗？重置后需要更新所有使用该Token的应用。')) {
        return;
    }
    
    try {
        const response = await fetch('/auth/reset_token', {
            method: 'POST'
        });
        
        const result = await response.json();
        
        if (result.code === 0) {
            currentUser.api_token = result.data.api_token;
            userToken = result.data.api_token;
            localStorage.setItem('tushare_token', userToken);
            
            updateUserInfo();
            updateAPIExamples();
            showMessage('API Token重置成功！', 'success');
        } else {
            showMessage('Token重置失败: ' + result.msg, 'danger');
        }
    } catch (error) {
        showMessage('Token重置请求失败: ' + error.message, 'danger');
    }
}

// 复制Token
function copyToken() {
    const tokenInput = document.getElementById('displayToken');
    tokenInput.select();
    tokenInput.setSelectionRange(0, 99999); // 移动端兼容
    
    try {
        document.execCommand('copy');
        showMessage('API Token已复制到剪贴板', 'info');
    } catch (error) {
        showMessage('复制失败，请手动复制', 'warning');
    }
}

// 退出登录
async function logout() {
    try {
        await fetch('/auth/logout', {
            method: 'POST'
        });
    } catch (error) {
        console.error('登出请求失败:', error);
    }
    
    // 清除本地数据
    currentUser = null;
    userToken = null;
    localStorage.removeItem('tushare_token');
    
    showMessage('已退出登录', 'info');
    showAuthForms();
}

// 显示API文档
function showAPIDoc() {
    window.open('/api/v1/info', '_blank');
}

// 显示消息
function showMessage(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    messageContainer.appendChild(alertDiv);
    
    // 自动移除消息
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

// 格式化日期时间
function formatDateTime(dateString) {
    if (!dateString) return '-';
    
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}
