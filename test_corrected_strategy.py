#!/usr/bin/env python3
"""
测试修正后的策略（正确的大盘市净率计算）
"""

import sys
import os
import pandas as pd

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_corrected_strategy():
    """测试修正后的策略"""
    print("🧪 测试修正后的策略（正确大盘PB计算）...")
    
    try:
        from strategy_backtest import StrategyBacktest
        
        # 创建回测实例
        backtest = StrategyBacktest()
        backtest.start_date = '20250801'
        backtest.end_date = '20250801'
        
        print(f"📅 测试期间: {backtest.start_date} - {backtest.end_date}")
        
        # 测试数据加载
        print("\n📊 测试数据加载...")
        if backtest.load_data():
            print("✅ 数据加载成功")
            
            if len(backtest.stock_data) > 0 and len(backtest.trade_dates) > 0:
                test_date = backtest.trade_dates[0]
                print(f"\n🧪 测试修正后的大盘PB计算 ({test_date})...")
                
                try:
                    # 测试修正后的大盘PB计算
                    market_pb = backtest.calculate_market_pb(test_date)
                    print(f"  ✅ 修正后大盘PB: {market_pb:.3f}")
                    
                    # 手动验证计算
                    date_str = str(test_date)
                    date_data = backtest.stock_data[
                        (backtest.stock_data['trade_date'].astype(str) == date_str) &
                        (backtest.stock_data['pb'] > 0) &
                        (backtest.stock_data['pb'].notna()) &
                        (backtest.stock_data['total_mv'] > 0) &
                        (backtest.stock_data['total_mv'].notna())
                    ].copy()
                    
                    if len(date_data) > 0:
                        # 手动计算验证
                        date_data['net_assets'] = date_data['total_mv'] / date_data['pb']
                        total_mv = date_data['total_mv'].sum()
                        total_na = date_data['net_assets'].sum()
                        manual_pb = total_mv / total_na if total_na > 0 else 0
                        
                        print(f"  📊 手动验证PB: {manual_pb:.3f}")
                        print(f"  📊 计算一致性: {'✅ 一致' if abs(market_pb - manual_pb) < 0.001 else '❌ 不一致'}")
                        
                        # 显示详细信息
                        print(f"  📊 全市场总市值: {total_mv:,.0f} 万元")
                        print(f"  📊 全市场净资产: {total_na:,.0f} 万元")
                        print(f"  📊 有效股票数量: {len(date_data)} 只")
                        
                        # 对比错误方法
                        simple_median = date_data['pb'].median()
                        weighted_avg = (date_data['pb'] * date_data['total_mv']).sum() / date_data['total_mv'].sum()
                        
                        print(f"\n📊 方法对比:")
                        print(f"  正确方法 (总市值÷净资产): {market_pb:.3f}")
                        print(f"  错误方法1 (简单中位数): {simple_median:.3f}")
                        print(f"  错误方法2 (市值加权平均): {weighted_avg:.3f}")
                        
                        # 择时信号分析
                        print(f"\n🎯 择时信号分析:")
                        
                        def get_signal(pb):
                            if pb < 1.2:
                                return "🟢 满仓信号"
                            elif pb < 2.0:
                                return "🟡 买入信号"
                            else:
                                return "🔴 观望信号"
                        
                        print(f"  正确PB({market_pb:.3f}): {get_signal(market_pb)}")
                        print(f"  错误PB1({simple_median:.3f}): {get_signal(simple_median)}")
                        print(f"  错误PB2({weighted_avg:.3f}): {get_signal(weighted_avg)}")
                        
                        # 测试仓位计算
                        position_weight = backtest.get_position_weight(test_date)
                        print(f"\n📊 策略仓位:")
                        print(f"  目标仓位: {position_weight:.1%}")
                        
                        # 测试股票评分
                        scored_stocks = backtest.calculate_stock_scores(test_date)
                        print(f"  评分股票数: {len(scored_stocks)}")
                        
                        if len(scored_stocks) > 0:
                            top_stock = scored_stocks.iloc[0]
                            print(f"  最高分股票: {top_stock['ts_code']} (分数: {top_stock['total_score']:.1f})")
                        
                        print("🎉 修正后计算测试通过！")
                        
                        # 分析市场结构
                        print(f"\n🔍 市场结构分析:")
                        
                        # 按市值分组
                        date_data['mv_group'] = pd.qcut(date_data['total_mv'], 
                                                       q=5, 
                                                       labels=['小盘', '中小盘', '中盘', '中大盘', '大盘'])
                        
                        group_stats = date_data.groupby('mv_group').agg({
                            'total_mv': ['sum', 'count'],
                            'pb': 'mean',
                            'net_assets': 'sum'
                        }).round(3)
                        
                        print("  按市值分组统计:")
                        for group in ['小盘', '中小盘', '中盘', '中大盘', '大盘']:
                            group_data = date_data[date_data['mv_group'] == group]
                            if len(group_data) > 0:
                                group_mv = group_data['total_mv'].sum()
                                group_na = group_data['net_assets'].sum()
                                group_pb = group_mv / group_na if group_na > 0 else 0
                                weight = group_mv / total_mv
                                avg_pb = group_data['pb'].mean()
                                print(f"    {group}: 真实PB={group_pb:.3f}, 平均PB={avg_pb:.3f}, 权重={weight:.1%}")
                        
                        return True
                        
                    else:
                        print("❌ 没有有效数据")
                        return False
                        
                except Exception as e:
                    print(f"❌ 计算失败: {e}")
                    import traceback
                    traceback.print_exc()
                    return False
            else:
                print("❌ 没有数据或交易日")
                return False
        else:
            print("❌ 数据加载失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 修正后策略测试")
    print("=" * 50)
    print("修正内容: 大盘市净率 = 全市场总市值 ÷ 全市场净资产总额")
    print("=" * 50)
    
    if test_corrected_strategy():
        print("\n🎉 测试成功！")
        print("✅ 大盘市净率计算已修正为正确方法")
        print("✅ 择时信号更加准确")
        print("\n💡 现在可以运行完整回测:")
        print("   python run_strategy_backtest.py")
        print("\n⚠️  注意: 修正后的择时信号可能与之前有很大差异！")
    else:
        print("\n❌ 测试失败")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
