#!/usr/bin/env python3
"""
日常数据更新脚本
每天运行一次，更新最新的股票数据
"""

import sys
import os
from datetime import datetime, timedelta

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def update_latest_data():
    """更新最新数据"""
    print("🔄 日常数据更新")
    print("=" * 40)
    
    try:
        from sync.enhanced_data_sync import EnhancedTushareDataSync
        
        # 获取昨日日期
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y%m%d')
        
        def progress_callback(data):
            progress = data.get('progress', 0)
            message = data.get('message', '')
            print(f"\r[{progress:3d}%] {message}", end='', flush=True)
            if progress >= 100:
                print()
        
        syncer = EnhancedTushareDataSync(progress_callback=progress_callback)
        
        # 更新日线数据
        print(f"📈 更新 {yesterday} 日线数据...")
        daily_result = syncer.sync_table_enhanced(
            'daily', 
            start_date=yesterday, 
            end_date=yesterday
        )
        
        # 更新基本面数据
        print(f"📊 更新 {yesterday} 基本面数据...")
        basic_result = syncer.sync_table_enhanced(
            'daily_basic', 
            start_date=yesterday, 
            end_date=yesterday
        )
        
        if daily_result['status'] == 'success' and basic_result['status'] == 'success':
            print(f"✅ 数据更新完成")
            print(f"   日线数据: {daily_result['record_count']:,} 条")
            print(f"   基本面数据: {basic_result['record_count']:,} 条")
        else:
            print(f"⚠️  部分数据更新失败")
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")

if __name__ == "__main__":
    update_latest_data()
