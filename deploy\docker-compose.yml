version: '3.8'

services:
  # Tushare镜像系统主应用
  tushare-mirror:
    build:
      context: ..
      dockerfile: deploy/Dockerfile
    container_name: tushare-mirror-app
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=sqlite:///data/tushare_mirror.db
      - TUSHARE_TOKEN=${TUSHARE_TOKEN}
      - SECRET_KEY=${SECRET_KEY}
    volumes:
      - ../data:/app/data
      - ../logs:/app/logs
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - tushare-network

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: tushare-mirror-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - tushare-network
    command: redis-server --appendonly yes

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: tushare-mirror-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - ../static:/var/www/static
    depends_on:
      - tushare-mirror
    restart: unless-stopped
    networks:
      - tushare-network

  # 定时任务调度器
  scheduler:
    build:
      context: ..
      dockerfile: deploy/Dockerfile.scheduler
    container_name: tushare-mirror-scheduler
    environment:
      - DATABASE_URL=sqlite:///data/tushare_mirror.db
      - TUSHARE_TOKEN=${TUSHARE_TOKEN}
    volumes:
      - ../data:/app/data
      - ../logs:/app/logs
    depends_on:
      - tushare-mirror
    restart: unless-stopped
    networks:
      - tushare-network

  # 监控服务 (Prometheus)
  prometheus:
    image: prom/prometheus:latest
    container_name: tushare-mirror-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    restart: unless-stopped
    networks:
      - tushare-network

  # 监控面板 (Grafana)
  grafana:
    image: grafana/grafana:latest
    container_name: tushare-mirror-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - tushare-network

volumes:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  tushare-network:
    driver: bridge
