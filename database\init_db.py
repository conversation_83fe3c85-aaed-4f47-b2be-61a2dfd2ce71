"""
数据库初始化脚本
创建所有必要的表和索引
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.models import db_config, Base
from sqlalchemy import text
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def init_database():
    """初始化数据库"""
    try:
        logger.info("开始初始化数据库...")
        
        # 创建所有表
        db_config.create_tables()
        logger.info("数据库表创建完成")
        
        # 创建一些基础数据
        session = db_config.get_session()
        
        try:
            # 检查是否已有数据
            result = session.execute(text("SELECT COUNT(*) FROM sync_status"))
            count = result.scalar()
            
            if count == 0:
                # 插入初始同步状态记录
                initial_tables = [
                    'stock_basic', 'trade_cal', 'daily', 'daily_basic',
                    'income', 'balancesheet', 'index_basic', 'index_daily'
                ]
                
                for table_name in initial_tables:
                    session.execute(text("""
                        INSERT INTO sync_status (table_name, sync_status, record_count)
                        VALUES (:table_name, 'pending', 0)
                    """), {'table_name': table_name})
                
                session.commit()
                logger.info("初始同步状态记录创建完成")
            else:
                logger.info("数据库已有数据，跳过初始化")
                
        except Exception as e:
            session.rollback()
            logger.error(f"插入初始数据失败: {e}")
            raise
        finally:
            session.close()
        
        logger.info("数据库初始化完成")
        return True
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        return False

def check_database():
    """检查数据库状态"""
    try:
        session = db_config.get_session()
        
        # 检查各表的记录数
        tables_info = []
        
        table_names = [
            'stock_basic', 'trade_cal', 'daily', 'daily_basic',
            'income', 'balancesheet', 'index_basic', 'index_daily',
            'sync_status', 'users'
        ]
        
        for table_name in table_names:
            try:
                result = session.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                count = result.scalar()
                tables_info.append((table_name, count))
            except Exception as e:
                tables_info.append((table_name, f"错误: {e}"))
        
        session.close()
        
        print("\n数据库状态检查:")
        print("=" * 50)
        for table_name, count in tables_info:
            print(f"{table_name:20} : {count}")
        print("=" * 50)
        
        return tables_info
        
    except Exception as e:
        logger.error(f"检查数据库状态失败: {e}")
        return None

def reset_database():
    """重置数据库（删除所有表并重新创建）"""
    try:
        logger.warning("开始重置数据库...")
        
        # 删除所有表
        Base.metadata.drop_all(bind=db_config.engine)
        logger.info("所有表已删除")
        
        # 重新创建表
        init_database()
        logger.info("数据库重置完成")
        
        return True
        
    except Exception as e:
        logger.error(f"数据库重置失败: {e}")
        return False

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='数据库管理工具')
    parser.add_argument('--init', action='store_true', help='初始化数据库')
    parser.add_argument('--check', action='store_true', help='检查数据库状态')
    parser.add_argument('--reset', action='store_true', help='重置数据库')
    
    args = parser.parse_args()
    
    if args.init:
        if init_database():
            print("✓ 数据库初始化成功")
        else:
            print("✗ 数据库初始化失败")
    
    elif args.check:
        check_database()
    
    elif args.reset:
        confirm = input("确认要重置数据库吗？这将删除所有数据！(y/N): ")
        if confirm.lower() == 'y':
            if reset_database():
                print("✓ 数据库重置成功")
            else:
                print("✗ 数据库重置失败")
        else:
            print("操作已取消")
    
    else:
        print("请指定操作参数: --init, --check, 或 --reset")
        print("使用 --help 查看详细帮助")
