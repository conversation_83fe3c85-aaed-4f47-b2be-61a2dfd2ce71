#!/usr/bin/env python3
"""
完整系统测试脚本
测试整个Tushare数据镜像系统的所有功能
"""

import sys
import os
import time
import threading
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_database_system():
    """测试数据库系统"""
    print("=" * 60)
    print("1. 数据库系统测试")
    print("=" * 60)
    
    try:
        from database.init_db import check_database
        from database.models import db_config
        
        print("✓ 数据库模块导入成功")
        
        # 检查数据库状态
        tables_info = check_database()
        if tables_info:
            print("✓ 数据库连接正常")
            
            # 统计数据
            total_records = 0
            for table_name, count in tables_info:
                if isinstance(count, int):
                    total_records += count
            
            print(f"✓ 数据库包含 {len(tables_info)} 个表")
            print(f"✓ 总记录数: {total_records:,}")
            return True
        else:
            print("✗ 数据库连接失败")
            return False
            
    except Exception as e:
        print(f"✗ 数据库系统测试失败: {e}")
        return False

def test_sync_system():
    """测试数据同步系统"""
    print("\n" + "=" * 60)
    print("2. 数据同步系统测试")
    print("=" * 60)
    
    try:
        from sync.data_sync import TushareDataSync
        from config import TUSHARE_TABLES
        
        print("✓ 同步模块导入成功")
        
        # 创建同步器
        syncer = TushareDataSync()
        print("✓ 同步器创建成功")
        
        print(f"✓ 支持 {len(TUSHARE_TABLES)} 个数据表同步")
        
        # 测试Tushare连接
        import tushare as ts
        from config import get_config
        
        config = get_config()
        ts.set_token(config.TUSHARE_TOKEN)
        pro = ts.pro_api()
        
        # 测试获取少量数据
        df = pro.stock_basic(limit=1)
        if not df.empty:
            print("✓ Tushare API连接正常")
            return True
        else:
            print("✗ Tushare API连接失败")
            return False
            
    except Exception as e:
        print(f"✗ 数据同步系统测试失败: {e}")
        return False

def test_api_system():
    """测试API系统"""
    print("\n" + "=" * 60)
    print("3. API系统测试")
    print("=" * 60)
    
    try:
        from api.tushare_api import api_bp, TushareAPIHandler
        from api.auth import auth_bp, generate_api_token
        
        print("✓ API模块导入成功")
        
        # 测试token生成
        token = generate_api_token()
        print(f"✓ API token生成成功: {token[:10]}...")
        
        # 测试API处理器
        handler = TushareAPIHandler()
        print("✓ API处理器创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ API系统测试失败: {e}")
        return False

def test_web_system():
    """测试Web系统"""
    print("\n" + "=" * 60)
    print("4. Web系统测试")
    print("=" * 60)
    
    try:
        from app import app
        
        print("✓ Flask应用导入成功")
        
        # 检查路由
        routes = []
        for rule in app.url_map.iter_rules():
            routes.append(rule.rule)
        
        expected_routes = ['/', '/user', '/docs', '/api/v1/info', '/auth/register']
        found_routes = 0
        
        for route in expected_routes:
            if any(route in r for r in routes):
                found_routes += 1
        
        print(f"✓ 路由检查: {found_routes}/{len(expected_routes)} 个主要路由正常")
        
        # 检查模板文件
        template_files = ['index.html', 'user_management.html', 'api_docs.html']
        existing_templates = 0
        
        for template in template_files:
            if os.path.exists(f'templates/{template}'):
                existing_templates += 1
        
        print(f"✓ 模板检查: {existing_templates}/{len(template_files)} 个模板文件存在")
        
        return True
        
    except Exception as e:
        print(f"✗ Web系统测试失败: {e}")
        return False

def start_web_server():
    """启动Web服务器"""
    try:
        from app import app, config
        print(f"启动Web服务器在端口 {config.WEB_CONFIG['port']}")
        app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)
    except Exception as e:
        print(f"Web服务器启动失败: {e}")

def test_web_endpoints():
    """测试Web端点"""
    print("\n" + "=" * 60)
    print("5. Web端点测试")
    print("=" * 60)
    
    try:
        import requests
        base_url = "http://localhost:5000"
        
        # 测试主要页面
        pages = [
            ('/', '首页'),
            ('/user', '用户管理'),
            ('/docs', 'API文档'),
            ('/api/v1/info', 'API信息')
        ]
        
        successful_pages = 0
        
        for path, name in pages:
            try:
                response = requests.get(base_url + path, timeout=5)
                if response.status_code == 200:
                    print(f"✓ {name} ({path}) - 状态码: {response.status_code}")
                    successful_pages += 1
                else:
                    print(f"✗ {name} ({path}) - 状态码: {response.status_code}")
            except Exception as e:
                print(f"✗ {name} ({path}) - 错误: {e}")
        
        print(f"✓ 页面测试: {successful_pages}/{len(pages)} 个页面正常")
        
        return successful_pages == len(pages)
        
    except ImportError:
        print("⚠ 需要安装requests库进行Web端点测试")
        print("  运行: pip install requests")
        return True  # 不影响整体测试结果
    except Exception as e:
        print(f"✗ Web端点测试失败: {e}")
        return False

def generate_system_report():
    """生成系统报告"""
    print("\n" + "=" * 60)
    print("系统报告生成")
    print("=" * 60)
    
    try:
        from database.init_db import check_database
        from config import TUSHARE_TABLES, get_config
        
        config = get_config()
        
        report = {
            'generated_at': datetime.now().isoformat(),
            'system_info': {
                'version': '1.0.0',
                'config_type': type(config).__name__,
                'supported_tables': len(TUSHARE_TABLES),
                'api_endpoints': len(TUSHARE_TABLES) + 1  # +1 for query endpoint
            },
            'database_info': {},
            'features': [
                '完整的数据库架构',
                '智能数据同步系统',
                'Tushare兼容API',
                '用户认证和管理',
                'Web管理界面',
                'API文档系统',
                '实时进度监控'
            ]
        }
        
        # 获取数据库信息
        tables_info = check_database()
        if tables_info:
            total_records = sum(count for _, count in tables_info if isinstance(count, int))
            report['database_info'] = {
                'total_tables': len(tables_info),
                'total_records': total_records,
                'tables': dict(tables_info)
            }
        
        # 保存报告
        import json
        with open('system_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print("✓ 系统报告已生成: system_report.json")
        
        # 显示摘要
        print(f"\n系统摘要:")
        print(f"  支持数据表: {report['system_info']['supported_tables']} 个")
        print(f"  API端点: {report['system_info']['api_endpoints']} 个")
        if 'total_records' in report['database_info']:
            print(f"  数据记录: {report['database_info']['total_records']:,} 条")
        print(f"  功能特性: {len(report['features'])} 项")
        
        return True
        
    except Exception as e:
        print(f"✗ 系统报告生成失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Tushare数据镜像系统 - 完整测试")
    print("测试时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # 运行各项测试
    tests = [
        ("数据库系统", test_database_system),
        ("数据同步系统", test_sync_system),
        ("API系统", test_api_system),
        ("Web系统", test_web_system),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        if test_func():
            passed += 1
        else:
            print(f"⚠ {test_name}测试失败，但继续其他测试")
    
    # 启动Web服务器进行端点测试
    print(f"\n启动Web服务器进行端点测试...")
    app_thread = threading.Thread(target=start_web_server, daemon=True)
    app_thread.start()
    
    # 等待服务器启动
    time.sleep(3)
    
    # 测试Web端点
    if test_web_endpoints():
        passed += 1
    total += 1
    
    # 生成系统报告
    if generate_system_report():
        passed += 1
    total += 1
    
    # 显示最终结果
    print("\n" + "=" * 60)
    print("🎯 测试结果汇总")
    print("=" * 60)
    print(f"总测试项: {total}")
    print(f"通过测试: {passed}")
    print(f"成功率: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 恭喜！所有系统测试通过！")
        print("\n✨ 您的Tushare数据镜像系统已完全就绪！")
        print("\n🌟 系统功能:")
        print("  • 完整的数据同步和存储")
        print("  • 与Tushare兼容的API接口")
        print("  • 用户认证和权限管理")
        print("  • 现代化的Web管理界面")
        print("  • 详细的API文档")
        print("  • 实时监控和日志")
        
        print("\n🚀 快速开始:")
        print("  1. 访问 http://localhost:5000 - 主控制台")
        print("  2. 访问 http://localhost:5000/user - 用户管理")
        print("  3. 访问 http://localhost:5000/docs - API文档")
        print("  4. 使用命令行: python sync_cli.py --help")
        
        print("\n📊 系统报告已保存到: system_report.json")
        
        # 保持服务器运行
        print("\n🌐 Web服务器正在运行...")
        print("按 Ctrl+C 停止服务器")
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n👋 服务器已停止，感谢使用！")
        
        return True
    else:
        print(f"\n❌ 部分测试失败 ({total-passed}/{total})")
        print("请检查错误信息并修复问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
