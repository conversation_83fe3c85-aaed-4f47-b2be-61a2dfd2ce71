#!/usr/bin/env python3
"""
同步历史数据脚本
专门用于同步回测所需的完整历史数据
"""

import sys
import os
import time
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_current_data_status():
    """检查当前数据状态"""
    print("🔍 检查当前数据状态...")
    
    try:
        from database.models import db_config
        from sqlalchemy import text
        
        session = db_config.get_session()
        
        # 检查各表的数据范围
        tables_to_check = {
            'stock_basic': ['ts_code', 'list_date', 'delist_date'],
            'daily': ['trade_date'],
            'daily_basic': ['trade_date'],
            'trade_cal': ['cal_date']
        }
        
        for table, date_columns in tables_to_check.items():
            try:
                # 检查记录数
                count_result = session.execute(text(f"SELECT COUNT(*) FROM {table}"))
                count = count_result.scalar()
                
                print(f"\n📊 {table} 表:")
                print(f"   记录数: {count:,}")
                
                if count > 0:
                    # 检查日期范围
                    for col in date_columns:
                        try:
                            min_result = session.execute(text(f"SELECT MIN({col}) FROM {table} WHERE {col} IS NOT NULL"))
                            max_result = session.execute(text(f"SELECT MAX({col}) FROM {table} WHERE {col} IS NOT NULL"))
                            min_date = min_result.scalar()
                            max_date = max_result.scalar()
                            
                            if min_date and max_date:
                                print(f"   {col}: {min_date} - {max_date}")
                            else:
                                print(f"   {col}: 无有效数据")
                        except Exception as e:
                            print(f"   {col}: 查询失败 - {e}")
                
                # 特别检查stock_basic的list_status字段
                if table == 'stock_basic':
                    try:
                        status_result = session.execute(text(f"SELECT list_status, COUNT(*) FROM {table} GROUP BY list_status"))
                        status_data = status_result.fetchall()
                        print(f"   上市状态分布:")
                        for status, count in status_data:
                            print(f"     {status}: {count}")
                    except Exception as e:
                        print(f"   上市状态查询失败: {e}")
                        
            except Exception as e:
                print(f"❌ {table} 表查询失败: {e}")
        
        session.close()
        
    except Exception as e:
        print(f"❌ 数据状态检查失败: {e}")

def sync_stock_basic():
    """同步股票基础信息"""
    print("\n📋 同步股票基础信息...")
    
    try:
        from sync.enhanced_data_sync import EnhancedTushareDataSync
        
        def progress_callback(data):
            progress = data.get('progress', 0)
            message = data.get('message', '')
            print(f"\r[{progress:3d}%] {message}", end='', flush=True)
            if progress >= 100:
                print()
        
        syncer = EnhancedTushareDataSync(progress_callback=progress_callback)
        
        print("  🔄 开始同步股票基础信息...")
        result = syncer.sync_table_enhanced('stock_basic', force_full=True)
        
        if result['status'] == 'success':
            print(f"✅ 股票基础信息同步成功: {result['record_count']:,} 条记录")
        else:
            print(f"❌ 股票基础信息同步失败: {result.get('error_message', '未知错误')}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 股票基础信息同步异常: {e}")
        return False

def sync_trade_calendar():
    """同步交易日历"""
    print("\n📅 同步交易日历...")
    
    try:
        from sync.enhanced_data_sync import EnhancedTushareDataSync
        
        def progress_callback(data):
            progress = data.get('progress', 0)
            message = data.get('message', '')
            print(f"\r[{progress:3d}%] {message}", end='', flush=True)
            if progress >= 100:
                print()
        
        syncer = EnhancedTushareDataSync(progress_callback=progress_callback)
        
        print("  🔄 开始同步交易日历...")
        result = syncer.sync_table_enhanced('trade_cal', force_full=True)
        
        if result['status'] == 'success':
            print(f"✅ 交易日历同步成功: {result['record_count']:,} 条记录")
        else:
            print(f"❌ 交易日历同步失败: {result.get('error_message', '未知错误')}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 交易日历同步异常: {e}")
        return False

def sync_historical_daily_data():
    """同步历史日线数据"""
    print("\n📈 同步历史日线数据...")
    
    try:
        from sync.enhanced_data_sync import EnhancedTushareDataSync
        
        def progress_callback(data):
            progress = data.get('progress', 0)
            message = data.get('message', '')
            table_name = data.get('table_name', '')
            print(f"\r[{progress:3d}%] {table_name}: {message}", end='', flush=True)
            if progress >= 100:
                print()
        
        syncer = EnhancedTushareDataSync(progress_callback=progress_callback)
        
        # 分年度同步，避免一次性同步太多数据
        years = [2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022, 2023, 2024, 2025]
        
        for year in years:
            start_date = f"{year}0101"
            end_date = f"{year}1231"
            
            print(f"\n  📊 同步 {year} 年日线数据...")
            
            result = syncer.sync_table_enhanced(
                'daily', 
                start_date=start_date, 
                end_date=end_date,
                force_full=False
            )
            
            if result['status'] == 'success':
                print(f"✅ {year} 年日线数据同步成功: {result['record_count']:,} 条记录")
            else:
                print(f"❌ {year} 年日线数据同步失败: {result.get('error_message', '未知错误')}")
                # 继续同步其他年份
                continue
            
            # 短暂休息，避免API限制
            time.sleep(2)
        
        return True
        
    except Exception as e:
        print(f"❌ 历史日线数据同步异常: {e}")
        return False

def sync_historical_basic_data():
    """同步历史基本面数据"""
    print("\n📊 同步历史基本面数据...")
    
    try:
        from sync.enhanced_data_sync import EnhancedTushareDataSync
        
        def progress_callback(data):
            progress = data.get('progress', 0)
            message = data.get('message', '')
            table_name = data.get('table_name', '')
            print(f"\r[{progress:3d}%] {table_name}: {message}", end='', flush=True)
            if progress >= 100:
                print()
        
        syncer = EnhancedTushareDataSync(progress_callback=progress_callback)
        
        # 分年度同步基本面数据
        years = [2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022, 2023, 2024, 2025]
        
        for year in years:
            start_date = f"{year}0101"
            end_date = f"{year}1231"
            
            print(f"\n  📊 同步 {year} 年基本面数据...")
            
            result = syncer.sync_table_enhanced(
                'daily_basic', 
                start_date=start_date, 
                end_date=end_date,
                force_full=False
            )
            
            if result['status'] == 'success':
                print(f"✅ {year} 年基本面数据同步成功: {result['record_count']:,} 条记录")
            else:
                print(f"❌ {year} 年基本面数据同步失败: {result.get('error_message', '未知错误')}")
                # 继续同步其他年份
                continue
            
            # 短暂休息，避免API限制
            time.sleep(2)
        
        return True
        
    except Exception as e:
        print(f"❌ 历史基本面数据同步异常: {e}")
        return False

def verify_data_completeness():
    """验证数据完整性"""
    print("\n🔍 验证数据完整性...")
    
    try:
        from database.models import db_config
        from sqlalchemy import text
        
        session = db_config.get_session()
        
        # 检查回测所需的数据完整性
        print("  📊 检查回测数据完整性:")
        
        # 1. 检查股票基础信息
        stock_count = session.execute(text("SELECT COUNT(*) FROM stock_basic")).scalar()
        print(f"     股票数量: {stock_count:,}")
        
        # 2. 检查交易日历
        trade_days = session.execute(text(
            "SELECT COUNT(*) FROM trade_cal WHERE cal_date >= '20150101' AND cal_date <= '20251231' AND is_open = 1"
        )).scalar()
        print(f"     交易日数量: {trade_days:,}")
        
        # 3. 检查日线数据
        daily_count = session.execute(text(
            "SELECT COUNT(*) FROM daily WHERE trade_date >= '20150101' AND trade_date <= '20251231'"
        )).scalar()
        print(f"     日线记录数: {daily_count:,}")
        
        # 4. 检查基本面数据
        basic_count = session.execute(text(
            "SELECT COUNT(*) FROM daily_basic WHERE trade_date >= '20150101' AND trade_date <= '20251231'"
        )).scalar()
        print(f"     基本面记录数: {basic_count:,}")
        
        # 5. 检查数据匹配度
        if daily_count > 0 and basic_count > 0:
            # 检查共同日期
            common_dates = session.execute(text("""
                SELECT COUNT(DISTINCT d.trade_date) 
                FROM daily d 
                INNER JOIN daily_basic db ON d.trade_date = db.trade_date 
                WHERE d.trade_date >= '20150101' AND d.trade_date <= '20251231'
            """)).scalar()
            print(f"     共同交易日: {common_dates:,}")
            
            if common_dates >= 2000:  # 约8年的交易日
                print("  ✅ 数据完整性检查通过，可以进行回测")
                return True
            else:
                print("  ⚠️  数据不够完整，建议继续同步")
                return False
        else:
            print("  ❌ 数据严重不足")
            return False
        
        session.close()
        
    except Exception as e:
        print(f"❌ 数据完整性验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🔄 历史数据同步脚本")
    print("=" * 60)
    
    # 1. 检查当前数据状态
    check_current_data_status()
    
    # 2. 询问用户是否继续
    print("\n" + "=" * 60)
    response = input("🤔 是否开始同步历史数据？这可能需要较长时间 (y/N): ").strip().lower()
    
    if response != 'y':
        print("👋 用户取消同步")
        return
    
    start_time = time.time()
    
    # 3. 同步基础数据
    print("\n📋 第1步: 同步基础数据...")
    if not sync_stock_basic():
        print("❌ 股票基础信息同步失败，停止后续同步")
        return
    
    if not sync_trade_calendar():
        print("❌ 交易日历同步失败，停止后续同步")
        return
    
    # 4. 同步历史行情数据
    print("\n📈 第2步: 同步历史行情数据...")
    if not sync_historical_daily_data():
        print("⚠️  历史日线数据同步有问题，但继续进行")
    
    # 5. 同步历史基本面数据
    print("\n📊 第3步: 同步历史基本面数据...")
    if not sync_historical_basic_data():
        print("⚠️  历史基本面数据同步有问题，但继续进行")
    
    # 6. 验证数据完整性
    print("\n🔍 第4步: 验证数据完整性...")
    data_ready = verify_data_completeness()
    
    end_time = time.time()
    duration = end_time - start_time
    
    print("\n" + "=" * 60)
    print(f"⏱️  总耗时: {duration/60:.1f} 分钟")
    
    if data_ready:
        print("🎉 历史数据同步完成！现在可以运行回测了。")
        print("\n💡 下一步:")
        print("   python run_strategy_backtest.py")
    else:
        print("⚠️  数据同步不完整，可能需要重试或检查网络连接。")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户中断同步")
    except Exception as e:
        print(f"\n❌ 同步过程异常: {e}")
        import traceback
        traceback.print_exc()
