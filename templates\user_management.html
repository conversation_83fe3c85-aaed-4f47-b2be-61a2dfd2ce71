<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - Tushare数据镜像系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='style.css') }}" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="bi bi-database"></i>
                Tushare数据镜像系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('index') }}">
                    <i class="bi bi-house"></i>
                    首页
                </a>
                <a class="nav-link active" href="#">
                    <i class="bi bi-person-gear"></i>
                    用户管理
                </a>
                <a class="nav-link" href="#" onclick="showAPIDoc()">
                    <i class="bi bi-book"></i>
                    API文档
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 用户状态卡片 -->
        <div class="row mb-4" id="userStatusCards" style="display: none;">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="bi bi-person-check fs-1 text-success"></i>
                        <h5 class="card-title">已登录</h5>
                        <p class="card-text" id="currentUsername">-</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="bi bi-key fs-1 text-info"></i>
                        <h5 class="card-title">API Token</h5>
                        <p class="card-text small" id="currentToken">-</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="bi bi-speedometer2 fs-1 text-warning"></i>
                        <h5 class="card-title">今日调用</h5>
                        <p class="card-text" id="todayCalls">-</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="bi bi-shield-check fs-1 text-primary"></i>
                        <h5 class="card-title">账户类型</h5>
                        <p class="card-text" id="accountType">-</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 登录/注册表单 -->
        <div class="row" id="authForms">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-box-arrow-in-right"></i>
                            用户登录
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="loginForm">
                            <div class="mb-3">
                                <label for="loginUsername" class="form-label">用户名或邮箱</label>
                                <input type="text" class="form-control" id="loginUsername" required>
                            </div>
                            <div class="mb-3">
                                <label for="loginPassword" class="form-label">密码</label>
                                <input type="password" class="form-control" id="loginPassword" required>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-box-arrow-in-right"></i>
                                登录
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-person-plus"></i>
                            用户注册
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="registerForm">
                            <div class="mb-3">
                                <label for="registerUsername" class="form-label">用户名</label>
                                <input type="text" class="form-control" id="registerUsername" required>
                                <div class="form-text">3-20位字母、数字或下划线</div>
                            </div>
                            <div class="mb-3">
                                <label for="registerEmail" class="form-label">邮箱</label>
                                <input type="email" class="form-control" id="registerEmail" required>
                            </div>
                            <div class="mb-3">
                                <label for="registerPassword" class="form-label">密码</label>
                                <input type="password" class="form-control" id="registerPassword" required>
                                <div class="form-text">至少6位，包含字母和数字</div>
                            </div>
                            <div class="mb-3">
                                <label for="confirmPassword" class="form-label">确认密码</label>
                                <input type="password" class="form-control" id="confirmPassword" required>
                            </div>
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-person-plus"></i>
                                注册
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- 用户管理面板 -->
        <div class="row mt-4" id="userPanel" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-gear"></i>
                            账户管理
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>API Token 管理</h6>
                                <div class="input-group mb-3">
                                    <input type="text" class="form-control" id="displayToken" readonly>
                                    <button class="btn btn-outline-secondary" type="button" onclick="copyToken()">
                                        <i class="bi bi-clipboard"></i>
                                        复制
                                    </button>
                                    <button class="btn btn-outline-warning" type="button" onclick="resetToken()">
                                        <i class="bi bi-arrow-clockwise"></i>
                                        重置
                                    </button>
                                </div>
                                
                                <h6>修改密码</h6>
                                <form id="changePasswordForm">
                                    <div class="mb-3">
                                        <input type="password" class="form-control" id="oldPassword" placeholder="当前密码" required>
                                    </div>
                                    <div class="mb-3">
                                        <input type="password" class="form-control" id="newPassword" placeholder="新密码" required>
                                    </div>
                                    <button type="submit" class="btn btn-warning">
                                        <i class="bi bi-key"></i>
                                        修改密码
                                    </button>
                                </form>
                            </div>
                            
                            <div class="col-md-6">
                                <h6>使用统计</h6>
                                <div class="progress mb-3">
                                    <div class="progress-bar" role="progressbar" id="usageProgress" style="width: 0%">0%</div>
                                </div>
                                <p class="text-muted">
                                    今日已使用 <span id="usedCalls">0</span> / <span id="totalCalls">0</span> 次
                                </p>
                                
                                <h6>账户信息</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td>注册时间:</td>
                                        <td id="registerTime">-</td>
                                    </tr>
                                    <tr>
                                        <td>最后登录:</td>
                                        <td id="lastLogin">-</td>
                                    </tr>
                                    <tr>
                                        <td>账户状态:</td>
                                        <td><span class="badge bg-success" id="accountStatus">正常</span></td>
                                    </tr>
                                </table>
                                
                                <button class="btn btn-danger" onclick="logout()">
                                    <i class="bi bi-box-arrow-right"></i>
                                    退出登录
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- API使用示例 -->
        <div class="row mt-4" id="apiExamples" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-code-slash"></i>
                            API使用示例
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Python示例</h6>
                                <pre><code id="pythonExample">import requests

# 获取股票基础信息
url = "http://localhost:5000/api/v1/stock_basic"
params = {
    "token": "your_token_here",
    "limit": 10
}

response = requests.get(url, params=params)
data = response.json()

if data['code'] == 0:
    stocks = data['data']['items']
    for stock in stocks:
        print(f"{stock['ts_code']} - {stock['name']}")
</code></pre>
                            </div>
                            
                            <div class="col-md-6">
                                <h6>JavaScript示例</h6>
                                <pre><code id="jsExample">// 获取每日指标
const url = "http://localhost:5000/api/v1/daily_basic";
const data = {
    ts_code: "000001.SZ",
    limit: 5
};

fetch(url, {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'your_token_here'
    },
    body: JSON.stringify(data)
})
.then(response => response.json())
.then(data => {
    if (data.code === 0) {
        console.log(data.data.items);
    }
});
</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 消息提示区域 -->
        <div class="row mt-4">
            <div class="col-12">
                <div id="messageContainer"></div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 用户管理JS -->
    <script src="{{ url_for('static', filename='user_management.js') }}"></script>
</body>
</html>
