"""
Tushare数据库模型定义
支持所有主要的Tushare数据表结构
"""

from sqlalchemy import create_engine, Column, String, Float, Integer, Date, DateTime, Text, Boolean, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import os

Base = declarative_base()

# 基础数据表
class StockBasic(Base):
    """股票基础信息"""
    __tablename__ = 'stock_basic'
    
    ts_code = Column(String(20), primary_key=True, comment='股票代码')
    symbol = Column(String(10), comment='股票代码（不含后缀）')
    name = Column(String(20), comment='股票名称')
    area = Column(String(20), comment='地域')
    industry = Column(String(50), comment='所属行业')
    fullname = Column(String(100), comment='股票全称')
    enname = Column(String(200), comment='英文全称')
    cnspell = Column(String(50), comment='拼音缩写')
    market = Column(String(10), comment='市场类型')
    exchange = Column(String(10), comment='交易所代码')
    curr_type = Column(String(10), comment='交易货币')
    list_status = Column(String(1), comment='上市状态')
    list_date = Column(String(8), comment='上市日期')
    delist_date = Column(String(8), comment='退市日期')
    is_hs = Column(String(1), comment='是否沪深港通标的')
    act_name = Column(String(100), comment='实控人名称')
    act_ent_type = Column(String(100), comment='实控人企业性质')
    
    __table_args__ = (
        Index('idx_stock_basic_symbol', 'symbol'),
        Index('idx_stock_basic_name', 'name'),
        Index('idx_stock_basic_industry', 'industry'),
    )

class TradeCal(Base):
    """交易日历"""
    __tablename__ = 'trade_cal'
    
    exchange = Column(String(10), primary_key=True, comment='交易所')
    cal_date = Column(String(8), primary_key=True, comment='日历日期')
    is_open = Column(Integer, comment='是否交易 0休市 1交易')
    pretrade_date = Column(String(8), comment='上一交易日')
    
    __table_args__ = (
        Index('idx_trade_cal_date', 'cal_date'),
        Index('idx_trade_cal_open', 'is_open'),
    )

# 行情数据表
class Daily(Base):
    """日线行情"""
    __tablename__ = 'daily'
    
    ts_code = Column(String(20), primary_key=True, comment='股票代码')
    trade_date = Column(String(8), primary_key=True, comment='交易日期')
    open = Column(Float, comment='开盘价')
    high = Column(Float, comment='最高价')
    low = Column(Float, comment='最低价')
    close = Column(Float, comment='收盘价')
    pre_close = Column(Float, comment='昨收价')
    change = Column(Float, comment='涨跌额')
    pct_chg = Column(Float, comment='涨跌幅')
    vol = Column(Float, comment='成交量（手）')
    amount = Column(Float, comment='成交额（千元）')
    
    __table_args__ = (
        Index('idx_daily_date', 'trade_date'),
        Index('idx_daily_code_date', 'ts_code', 'trade_date'),
    )

class DailyBasic(Base):
    """每日指标"""
    __tablename__ = 'daily_basic'
    
    ts_code = Column(String(20), primary_key=True, comment='股票代码')
    trade_date = Column(String(8), primary_key=True, comment='交易日期')
    close = Column(Float, comment='当日收盘价')
    turnover_rate = Column(Float, comment='换手率（%）')
    turnover_rate_f = Column(Float, comment='换手率（自由流通股）')
    volume_ratio = Column(Float, comment='量比')
    pe = Column(Float, comment='市盈率（总市值/净利润）')
    pe_ttm = Column(Float, comment='市盈率（TTM）')
    pb = Column(Float, comment='市净率（总市值/净资产）')
    ps = Column(Float, comment='市销率')
    ps_ttm = Column(Float, comment='市销率（TTM）')
    dv_ratio = Column(Float, comment='股息率（%）')
    dv_ttm = Column(Float, comment='股息率（TTM）（%）')
    total_share = Column(Float, comment='总股本（万股）')
    float_share = Column(Float, comment='流通股本（万股）')
    free_share = Column(Float, comment='自由流通股本（万股）')
    total_mv = Column(Float, comment='总市值（万元）')
    circ_mv = Column(Float, comment='流通市值（万元）')
    
    __table_args__ = (
        Index('idx_daily_basic_date', 'trade_date'),
        Index('idx_daily_basic_code_date', 'ts_code', 'trade_date'),
    )

# 财务数据表
class Income(Base):
    """利润表"""
    __tablename__ = 'income'
    
    ts_code = Column(String(20), primary_key=True, comment='股票代码')
    ann_date = Column(String(8), primary_key=True, comment='公告日期')
    f_ann_date = Column(String(8), primary_key=True, comment='实际公告日期')
    end_date = Column(String(8), comment='报告期')
    report_type = Column(String(20), comment='报告类型')
    comp_type = Column(String(20), comment='公司类型')
    basic_eps = Column(Float, comment='基本每股收益')
    diluted_eps = Column(Float, comment='稀释每股收益')
    total_revenue = Column(Float, comment='营业总收入')
    revenue = Column(Float, comment='营业收入')
    int_income = Column(Float, comment='利息收入')
    prem_earned = Column(Float, comment='已赚保费')
    comm_income = Column(Float, comment='手续费及佣金收入')
    n_commis_income = Column(Float, comment='手续费及佣金净收入')
    n_oth_income = Column(Float, comment='其他经营净收益')
    n_oth_b_income = Column(Float, comment='加:其他业务净收益')
    prem_income = Column(Float, comment='保险业务收入')
    out_prem = Column(Float, comment='减:分出保费')
    une_prem_reser = Column(Float, comment='提取未到期责任准备金')
    reins_income = Column(Float, comment='其中:分保费收入')
    n_sec_tb_income = Column(Float, comment='代理买卖证券业务净收入')
    n_sec_uw_income = Column(Float, comment='证券承销业务净收入')
    n_asset_mg_income = Column(Float, comment='受托客户资产管理业务净收入')
    oth_b_income = Column(Float, comment='其他业务收入')
    fv_value_chg_gain = Column(Float, comment='加:公允价值变动净收益')
    invest_income = Column(Float, comment='加:投资净收益')
    ass_invest_income = Column(Float, comment='其中:对联营企业和合营企业的投资收益')
    forex_gain = Column(Float, comment='加:汇兑净收益')
    total_cogs = Column(Float, comment='营业总成本')
    oper_cost = Column(Float, comment='减:营业成本')
    
    __table_args__ = (
        Index('idx_income_code', 'ts_code'),
        Index('idx_income_end_date', 'end_date'),
        Index('idx_income_ann_date', 'ann_date'),
    )

class BalanceSheet(Base):
    """资产负债表"""
    __tablename__ = 'balancesheet'
    
    ts_code = Column(String(20), primary_key=True, comment='股票代码')
    ann_date = Column(String(8), primary_key=True, comment='公告日期')
    f_ann_date = Column(String(8), primary_key=True, comment='实际公告日期')
    end_date = Column(String(8), comment='报告期')
    report_type = Column(String(20), comment='报告类型')
    comp_type = Column(String(20), comment='公司类型')
    total_share = Column(Float, comment='期末总股本')
    cap_rese = Column(Float, comment='资本公积金')
    undistr_porfit = Column(Float, comment='未分配利润')
    surplus_rese = Column(Float, comment='盈余公积金')
    special_rese = Column(Float, comment='专项储备')
    money_cap = Column(Float, comment='货币资金')
    trad_asset = Column(Float, comment='交易性金融资产')
    notes_receiv = Column(Float, comment='应收票据')
    accounts_receiv = Column(Float, comment='应收账款')
    oth_receiv = Column(Float, comment='其他应收款')
    prepayment = Column(Float, comment='预付款项')
    div_receiv = Column(Float, comment='应收股利')
    int_receiv = Column(Float, comment='应收利息')
    inventories = Column(Float, comment='存货')
    amor_exp = Column(Float, comment='长期待摊费用')
    nca_within_1y = Column(Float, comment='一年内到期的非流动资产')
    sett_rsrv = Column(Float, comment='结算备付金')
    loanto_oth_bank_fi = Column(Float, comment='拆出资金')
    premium_receiv = Column(Float, comment='应收保费')
    reinsur_receiv = Column(Float, comment='应收分保账款')
    reinsur_res_receiv = Column(Float, comment='应收分保合同准备金')
    pur_resale_fa = Column(Float, comment='买入返售金融资产')
    oth_cur_assets = Column(Float, comment='其他流动资产')
    total_cur_assets = Column(Float, comment='流动资产合计')
    
    __table_args__ = (
        Index('idx_balance_code', 'ts_code'),
        Index('idx_balance_end_date', 'end_date'),
        Index('idx_balance_ann_date', 'ann_date'),
    )

# 指数数据表
class IndexBasic(Base):
    """指数基础信息"""
    __tablename__ = 'index_basic'
    
    ts_code = Column(String(20), primary_key=True, comment='指数代码')
    name = Column(String(100), comment='指数名称')
    fullname = Column(String(200), comment='指数全称')
    market = Column(String(20), comment='市场')
    publisher = Column(String(100), comment='发布方')
    index_type = Column(String(20), comment='指数风格')
    category = Column(String(20), comment='指数类别')
    base_date = Column(String(8), comment='基期')
    base_point = Column(Float, comment='基点')
    list_date = Column(String(8), comment='发布日期')
    weight_rule = Column(String(100), comment='加权方式')
    desc = Column(Text, comment='描述')
    exp_date = Column(String(8), comment='终止日期')
    
    __table_args__ = (
        Index('idx_index_basic_name', 'name'),
        Index('idx_index_basic_market', 'market'),
    )

class IndexDaily(Base):
    """指数日线行情"""
    __tablename__ = 'index_daily'
    
    ts_code = Column(String(20), primary_key=True, comment='指数代码')
    trade_date = Column(String(8), primary_key=True, comment='交易日期')
    close = Column(Float, comment='收盘点位')
    open = Column(Float, comment='开盘点位')
    high = Column(Float, comment='最高点位')
    low = Column(Float, comment='最低点位')
    pre_close = Column(Float, comment='昨日收盘点')
    change = Column(Float, comment='涨跌点')
    pct_chg = Column(Float, comment='涨跌幅')
    vol = Column(Float, comment='成交量（手）')
    amount = Column(Float, comment='成交额（千元）')
    
    __table_args__ = (
        Index('idx_index_daily_date', 'trade_date'),
        Index('idx_index_daily_code_date', 'ts_code', 'trade_date'),
    )

# 数据同步状态表
class SyncStatus(Base):
    """数据同步状态记录"""
    __tablename__ = 'sync_status'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    table_name = Column(String(50), comment='表名')
    last_sync_date = Column(String(8), comment='最后同步日期')
    last_sync_time = Column(DateTime, default=datetime.now, comment='最后同步时间')
    sync_status = Column(String(20), default='pending', comment='同步状态')
    error_message = Column(Text, comment='错误信息')
    record_count = Column(Integer, default=0, comment='同步记录数')
    
    __table_args__ = (
        Index('idx_sync_status_table', 'table_name'),
        Index('idx_sync_status_date', 'last_sync_date'),
    )

# 用户管理表
class User(Base):
    """用户信息"""
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String(50), unique=True, nullable=False, comment='用户名')
    email = Column(String(100), unique=True, nullable=False, comment='邮箱')
    password_hash = Column(String(255), nullable=False, comment='密码哈希')
    api_token = Column(String(100), unique=True, comment='API Token')
    is_active = Column(Boolean, default=True, comment='是否激活')
    is_premium = Column(Boolean, default=False, comment='是否高级用户')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    last_login = Column(DateTime, comment='最后登录时间')
    api_calls_today = Column(Integer, default=0, comment='今日API调用次数')
    api_limit_daily = Column(Integer, default=1000, comment='每日API调用限制')
    
    __table_args__ = (
        Index('idx_user_username', 'username'),
        Index('idx_user_email', 'email'),
        Index('idx_user_token', 'api_token'),
    )

# 数据库配置
class DatabaseConfig:
    """数据库配置类"""
    
    def __init__(self, db_url=None):
        if db_url is None:
            # 默认使用SQLite数据库
            db_path = os.path.join(os.path.dirname(__file__), '..', 'data', 'tushare_mirror.db')
            os.makedirs(os.path.dirname(db_path), exist_ok=True)
            db_url = f'sqlite:///{db_path}'
        
        self.engine = create_engine(db_url, echo=False)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
    
    def create_tables(self):
        """创建所有表"""
        Base.metadata.create_all(bind=self.engine)
    
    def get_session(self):
        """获取数据库会话"""
        return self.SessionLocal()

# 全局数据库实例
db_config = DatabaseConfig()
