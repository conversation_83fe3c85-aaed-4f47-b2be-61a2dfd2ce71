#!/usr/bin/env python3
"""
测试Web应用启动
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_web_app():
    """测试Web应用"""
    print("🌐 测试Web应用启动...")
    
    try:
        from app import app, config
        print("✅ app.py 导入成功")
        
        # 测试基本路由
        with app.test_client() as client:
            # 测试主页
            response = client.get('/')
            print(f"✅ 主页访问: 状态码 {response.status_code}")
            
            # 测试API端点
            response = client.get('/api/tables')
            print(f"✅ API端点访问: 状态码 {response.status_code}")
            
            # 测试数据库状态
            response = client.get('/api/database_status')
            print(f"✅ 数据库状态: 状态码 {response.status_code}")
        
        print("✅ Web应用测试通过")
        return True
        
    except Exception as e:
        print(f"❌ Web应用测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def start_test_server():
    """启动测试服务器"""
    print("🚀 启动测试Web服务器...")
    
    try:
        from app import app, config
        
        print("🌐 启动Web服务器...")
        print(f"📍 访问地址: http://localhost:5001")
        print("按 Ctrl+C 停止服务器")
        
        app.run(
            debug=False, 
            host='127.0.0.1', 
            port=5001, 
            use_reloader=False
        )
        
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "start":
        start_test_server()
    else:
        success = test_web_app()
        if success:
            print("\n💡 运行 'python test_web_app.py start' 启动测试服务器")
        sys.exit(0 if success else 1)
