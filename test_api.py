#!/usr/bin/env python3
"""
API接口测试脚本
"""

import sys
import os
import requests
import json
import time
import threading
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def start_app():
    """启动Web应用"""
    from app import app, config
    print(f"启动API服务器在端口 {config.WEB_CONFIG['port']}")
    app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)

class APITester:
    """API测试器"""
    
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.token = None
        self.session = requests.Session()
    
    def test_user_registration(self):
        """测试用户注册"""
        print("\n=== 测试用户注册 ===")
        
        # 生成唯一用户名
        timestamp = int(time.time())
        test_user = {
            "username": f"testuser_{timestamp}",
            "email": f"test_{timestamp}@example.com",
            "password": "test123456"
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/auth/register",
                json=test_user,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data['code'] == 0:
                    print(f"✓ 用户注册成功")
                    print(f"  用户名: {data['data']['username']}")
                    print(f"  邮箱: {data['data']['email']}")
                    print(f"  API Token: {data['data']['api_token'][:20]}...")
                    print(f"  每日限制: {data['data']['api_limit_daily']}")
                    
                    # 保存token用于后续测试
                    self.token = data['data']['api_token']
                    self.test_user = test_user
                    return True
                else:
                    print(f"✗ 注册失败: {data['msg']}")
                    return False
            else:
                print(f"✗ 注册请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"✗ 注册测试异常: {e}")
            return False
    
    def test_user_login(self):
        """测试用户登录"""
        print("\n=== 测试用户登录 ===")
        
        if not hasattr(self, 'test_user'):
            print("✗ 需要先注册用户")
            return False
        
        try:
            response = self.session.post(
                f"{self.base_url}/auth/login",
                json={
                    "username": self.test_user['username'],
                    "password": self.test_user['password']
                },
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data['code'] == 0:
                    print(f"✓ 用户登录成功")
                    print(f"  用户名: {data['data']['username']}")
                    print(f"  今日调用: {data['data']['api_calls_today']}")
                    return True
                else:
                    print(f"✗ 登录失败: {data['msg']}")
                    return False
            else:
                print(f"✗ 登录请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"✗ 登录测试异常: {e}")
            return False
    
    def test_api_info(self):
        """测试API信息接口"""
        print("\n=== 测试API信息接口 ===")
        
        try:
            response = self.session.get(
                f"{self.base_url}/api/v1/info",
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data['code'] == 0:
                    print(f"✓ API信息获取成功")
                    print(f"  版本: {data['data']['version']}")
                    print(f"  名称: {data['data']['name']}")
                    print(f"  支持的端点: {len(data['data']['endpoints'])} 个")
                    print(f"  端点列表: {', '.join(data['data']['endpoints'])}")
                    return True
                else:
                    print(f"✗ API信息获取失败: {data['msg']}")
                    return False
            else:
                print(f"✗ API信息请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"✗ API信息测试异常: {e}")
            return False
    
    def test_stock_basic_api(self):
        """测试股票基础信息API"""
        print("\n=== 测试股票基础信息API ===")
        
        if not self.token:
            print("✗ 需要先获取API token")
            return False
        
        try:
            # 测试GET方式
            response = self.session.get(
                f"{self.base_url}/api/v1/stock_basic",
                params={
                    "token": self.token,
                    "limit": 10
                },
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data['code'] == 0:
                    items = data['data']['items']
                    print(f"✓ 股票基础信息API测试成功")
                    print(f"  返回记录数: {len(items)}")
                    print(f"  字段数: {len(data['data']['fields'])}")
                    if items:
                        print(f"  示例股票: {items[0].get('ts_code')} - {items[0].get('name')}")
                    return True
                else:
                    print(f"✗ 股票基础信息API失败: {data['msg']}")
                    return False
            else:
                print(f"✗ 股票基础信息API请求失败: {response.status_code}")
                if response.status_code == 401:
                    print("  可能是token验证失败")
                return False
                
        except Exception as e:
            print(f"✗ 股票基础信息API测试异常: {e}")
            return False
    
    def test_daily_basic_api(self):
        """测试每日指标API"""
        print("\n=== 测试每日指标API ===")
        
        if not self.token:
            print("✗ 需要先获取API token")
            return False
        
        try:
            # 测试POST方式
            response = self.session.post(
                f"{self.base_url}/api/v1/daily_basic",
                json={
                    "ts_code": "000001.SZ",
                    "limit": 5
                },
                headers={
                    "Authorization": self.token
                },
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data['code'] == 0:
                    items = data['data']['items']
                    print(f"✓ 每日指标API测试成功")
                    print(f"  返回记录数: {len(items)}")
                    if items:
                        print(f"  最新数据: {items[0].get('trade_date')} - PE: {items[0].get('pe')}")
                    return True
                else:
                    print(f"✗ 每日指标API失败: {data['msg']}")
                    return False
            else:
                print(f"✗ 每日指标API请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"✗ 每日指标API测试异常: {e}")
            return False
    
    def test_query_api(self):
        """测试通用查询API"""
        print("\n=== 测试通用查询API ===")
        
        if not self.token:
            print("✗ 需要先获取API token")
            return False
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/v1/query",
                json={
                    "api_name": "stock_basic",
                    "params": {
                        "limit": 5,
                        "is_hs": "S"
                    }
                },
                headers={
                    "Authorization": self.token
                },
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data['code'] == 0:
                    items = data['data']['items']
                    print(f"✓ 通用查询API测试成功")
                    print(f"  返回记录数: {len(items)}")
                    if items:
                        print(f"  示例: {items[0].get('ts_code')} - {items[0].get('name')}")
                    return True
                else:
                    print(f"✗ 通用查询API失败: {data['msg']}")
                    return False
            else:
                print(f"✗ 通用查询API请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"✗ 通用查询API测试异常: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("=" * 60)
        print("Tushare兼容API接口测试")
        print("=" * 60)
        
        tests = [
            ("用户注册", self.test_user_registration),
            ("用户登录", self.test_user_login),
            ("API信息", self.test_api_info),
            ("股票基础信息API", self.test_stock_basic_api),
            ("每日指标API", self.test_daily_basic_api),
            ("通用查询API", self.test_query_api),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
                else:
                    print(f"测试 '{test_name}' 失败，继续下一个测试")
            except Exception as e:
                print(f"测试 '{test_name}' 异常: {e}")
        
        print("\n" + "=" * 60)
        print(f"测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("✓ 所有API测试通过！")
            return True
        else:
            print("✗ 部分API测试失败")
            return False

def main():
    """主函数"""
    print("启动API测试...")
    
    # 在后台线程启动Web应用
    app_thread = threading.Thread(target=start_app, daemon=True)
    app_thread.start()
    
    # 等待服务器启动
    print("等待服务器启动...")
    time.sleep(5)
    
    # 运行API测试
    tester = APITester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 API系统测试完成！")
        print("API服务器正在运行在: http://localhost:5000")
        print("API文档: http://localhost:5000/api/v1/info")
        print("\n按 Ctrl+C 停止服务器")
        
        try:
            # 保持主线程运行
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n服务器已停止")
    else:
        print("\n❌ API测试失败，请检查配置")

if __name__ == "__main__":
    main()
