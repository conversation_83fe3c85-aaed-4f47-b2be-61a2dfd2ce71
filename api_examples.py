#!/usr/bin/env python3
"""
Tushare兼容API使用示例
演示如何使用我们的API接口获取数据
"""

import requests
import json
import pandas as pd
from datetime import datetime, timedelta

class TushareMirrorAPI:
    """Tushare镜像API客户端"""
    
    def __init__(self, base_url="http://localhost:5000", token=None):
        self.base_url = base_url
        self.token = token
        self.session = requests.Session()
    
    def set_token(self, token):
        """设置API token"""
        self.token = token
    
    def _request(self, method, endpoint, data=None, params=None):
        """发送API请求"""
        url = f"{self.base_url}{endpoint}"
        
        headers = {}
        if self.token:
            headers['Authorization'] = self.token
        
        if method.upper() == 'GET':
            if params and self.token:
                params['token'] = self.token
            response = self.session.get(url, params=params, headers=headers)
        else:
            response = self.session.post(url, json=data, headers=headers)
        
        return response.json()
    
    def register(self, username, email, password):
        """用户注册"""
        data = {
            "username": username,
            "email": email,
            "password": password
        }
        
        result = self._request('POST', '/auth/register', data=data)
        
        if result['code'] == 0:
            self.token = result['data']['api_token']
            print(f"注册成功！API Token: {self.token}")
            return result['data']
        else:
            print(f"注册失败: {result['msg']}")
            return None
    
    def login(self, username, password):
        """用户登录"""
        data = {
            "username": username,
            "password": password
        }
        
        result = self._request('POST', '/auth/login', data=data)
        
        if result['code'] == 0:
            self.token = result['data']['api_token']
            print(f"登录成功！API Token: {self.token}")
            return result['data']
        else:
            print(f"登录失败: {result['msg']}")
            return None
    
    def stock_basic(self, **kwargs):
        """获取股票基础信息"""
        result = self._request('GET', '/api/v1/stock_basic', params=kwargs)
        
        if result['code'] == 0:
            return pd.DataFrame(result['data']['items'])
        else:
            print(f"获取股票基础信息失败: {result['msg']}")
            return pd.DataFrame()
    
    def daily(self, **kwargs):
        """获取日线行情"""
        result = self._request('POST', '/api/v1/daily', data=kwargs)
        
        if result['code'] == 0:
            return pd.DataFrame(result['data']['items'])
        else:
            print(f"获取日线行情失败: {result['msg']}")
            return pd.DataFrame()
    
    def daily_basic(self, **kwargs):
        """获取每日指标"""
        result = self._request('POST', '/api/v1/daily_basic', data=kwargs)
        
        if result['code'] == 0:
            return pd.DataFrame(result['data']['items'])
        else:
            print(f"获取每日指标失败: {result['msg']}")
            return pd.DataFrame()
    
    def trade_cal(self, **kwargs):
        """获取交易日历"""
        result = self._request('GET', '/api/v1/trade_cal', params=kwargs)
        
        if result['code'] == 0:
            return pd.DataFrame(result['data']['items'])
        else:
            print(f"获取交易日历失败: {result['msg']}")
            return pd.DataFrame()
    
    def query(self, api_name, **params):
        """通用查询接口"""
        data = {
            "api_name": api_name,
            "params": params
        }
        
        result = self._request('POST', '/api/v1/query', data=data)
        
        if result['code'] == 0:
            return pd.DataFrame(result['data']['items'])
        else:
            print(f"查询失败: {result['msg']}")
            return pd.DataFrame()

def example_basic_usage():
    """基础使用示例"""
    print("=" * 60)
    print("基础使用示例")
    print("=" * 60)
    
    # 创建API客户端
    api = TushareMirrorAPI()
    
    # 注册用户（如果还没有账户）
    timestamp = int(datetime.now().timestamp())
    username = f"demo_user_{timestamp}"
    email = f"demo_{timestamp}@example.com"
    password = "demo123456"
    
    print(f"1. 注册用户: {username}")
    user_info = api.register(username, email, password)
    
    if not user_info:
        print("注册失败，尝试使用现有token")
        return
    
    print(f"   用户ID: {user_info['user_id']}")
    print(f"   每日限制: {user_info['api_limit_daily']} 次")
    
    # 获取股票基础信息
    print("\n2. 获取股票基础信息（前10条）")
    stocks = api.stock_basic(limit=10)
    
    if not stocks.empty:
        print(f"   获取到 {len(stocks)} 条记录")
        print("   示例数据:")
        for _, stock in stocks.head(3).iterrows():
            print(f"     {stock['ts_code']} - {stock['name']} ({stock['industry']})")
    
    # 获取特定股票的每日指标
    print("\n3. 获取平安银行最近5天的每日指标")
    daily_basic = api.daily_basic(ts_code="000001.SZ", limit=5)
    
    if not daily_basic.empty:
        print(f"   获取到 {len(daily_basic)} 条记录")
        print("   最新数据:")
        latest = daily_basic.iloc[0]
        print(f"     日期: {latest['trade_date']}")
        print(f"     收盘价: {latest['close']}")
        print(f"     市盈率: {latest['pe']}")
        print(f"     市净率: {latest['pb']}")

def example_advanced_usage():
    """高级使用示例"""
    print("\n" + "=" * 60)
    print("高级使用示例")
    print("=" * 60)
    
    # 使用现有token（实际使用中应该保存token）
    api = TushareMirrorAPI()
    
    # 这里需要使用实际的token
    print("注意: 需要先运行基础示例获取token，或手动设置token")
    print("api.set_token('your_token_here')")
    
    # 示例查询代码（需要有效token才能运行）
    print("\n示例查询代码:")
    
    print("""
# 1. 获取沪深300成分股
hs300_stocks = api.stock_basic(is_hs='S', limit=50)
print(f"沪深300成分股数量: {len(hs300_stocks)}")

# 2. 获取特定日期的交易日历
trade_cal = api.trade_cal(
    exchange='SSE',
    start_date='20240101',
    end_date='20240131'
)
print(f"2024年1月交易日: {len(trade_cal[trade_cal['is_open']==1])} 天")

# 3. 使用通用查询接口
result = api.query('daily_basic', 
                  ts_code='000001.SZ',
                  start_date='20240101',
                  end_date='20240131')
print(f"平安银行1月每日指标: {len(result)} 条")

# 4. 批量获取多只股票数据
stock_codes = ['000001.SZ', '000002.SZ', '600000.SH']
for code in stock_codes:
    data = api.daily_basic(ts_code=code, limit=1)
    if not data.empty:
        latest = data.iloc[0]
        print(f"{code}: PE={latest['pe']}, PB={latest['pb']}")
""")

def example_data_analysis():
    """数据分析示例"""
    print("\n" + "=" * 60)
    print("数据分析示例")
    print("=" * 60)
    
    print("""
# 使用pandas进行数据分析
import pandas as pd
import matplotlib.pyplot as plt

# 获取数据
api = TushareMirrorAPI()
api.set_token('your_token_here')

# 1. 分析市盈率分布
stocks = api.stock_basic(limit=1000)
daily_data = []

for _, stock in stocks.iterrows():
    data = api.daily_basic(ts_code=stock['ts_code'], limit=1)
    if not data.empty:
        daily_data.append(data.iloc[0])

df = pd.DataFrame(daily_data)

# 过滤异常值
df_clean = df[(df['pe'] > 0) & (df['pe'] < 100)]

# 绘制市盈率分布图
plt.figure(figsize=(10, 6))
plt.hist(df_clean['pe'], bins=50, alpha=0.7)
plt.xlabel('市盈率 (PE)')
plt.ylabel('股票数量')
plt.title('A股市盈率分布')
plt.show()

# 2. 行业分析
industry_stats = stocks.groupby('industry').size().sort_values(ascending=False)
print("行业股票数量排名:")
print(industry_stats.head(10))

# 3. 市值分析
df_mv = df[df['total_mv'] > 0]
print(f"平均总市值: {df_mv['total_mv'].mean():.2f} 万元")
print(f"中位数总市值: {df_mv['total_mv'].median():.2f} 万元")
""")

def main():
    """主函数"""
    print("Tushare兼容API使用示例")
    print("确保API服务器正在运行在 http://localhost:5000")
    
    try:
        # 运行基础示例
        example_basic_usage()
        
        # 显示高级示例
        example_advanced_usage()
        
        # 显示数据分析示例
        example_data_analysis()
        
        print("\n" + "=" * 60)
        print("示例完成！")
        print("更多API文档请访问: http://localhost:5000/api/v1/info")
        print("=" * 60)
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务器")
        print("请确保运行: python app.py 或 python test_api.py")
    except Exception as e:
        print(f"❌ 示例运行失败: {e}")

if __name__ == "__main__":
    main()
