#!/usr/bin/env python3
"""
增强功能系统测试
测试新增的高级功能模块
"""

import sys
import os
import time
import threading
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_admin_module():
    """测试管理员模块"""
    print("=" * 60)
    print("1. 管理员功能模块测试")
    print("=" * 60)
    
    try:
        from api.admin import admin_bp, require_admin
        print("✓ 管理员模块导入成功")
        
        # 测试蓝图注册
        print(f"✓ 管理员蓝图URL前缀: {admin_bp.url_prefix}")
        
        # 测试装饰器
        print("✓ 管理员权限装饰器可用")
        
        return True
    except Exception as e:
        print(f"✗ 管理员模块测试失败: {e}")
        return False

def test_scheduler_module():
    """测试定时任务模块"""
    print("\n" + "=" * 60)
    print("2. 定时任务系统测试")
    print("=" * 60)
    
    try:
        from scheduler.tasks import TaskScheduler, start_scheduler, stop_scheduler
        print("✓ 定时任务模块导入成功")
        
        # 创建调度器实例
        scheduler = TaskScheduler()
        print("✓ 任务调度器创建成功")
        
        # 测试调度器状态
        status = scheduler.get_job_status()
        print(f"✓ 调度器状态: {status['status']}")
        
        return True
    except Exception as e:
        print(f"✗ 定时任务模块测试失败: {e}")
        return False

def test_export_module():
    """测试数据导出模块"""
    print("\n" + "=" * 60)
    print("3. 数据导出功能测试")
    print("=" * 60)
    
    try:
        from api.export import export_bp
        print("✓ 数据导出模块导入成功")
        
        print(f"✓ 导出蓝图URL前缀: {export_bp.url_prefix}")
        
        # 测试pandas和openpyxl
        import pandas as pd
        print("✓ pandas库可用")
        
        try:
            import openpyxl
            print("✓ openpyxl库可用，支持Excel导出")
        except ImportError:
            print("⚠ openpyxl库不可用，Excel导出功能受限")
        
        return True
    except Exception as e:
        print(f"✗ 数据导出模块测试失败: {e}")
        return False

def test_monitor_module():
    """测试系统监控模块"""
    print("\n" + "=" * 60)
    print("4. 系统监控功能测试")
    print("=" * 60)
    
    try:
        from api.monitor import monitor_bp
        print("✓ 系统监控模块导入成功")
        
        print(f"✓ 监控蓝图URL前缀: {monitor_bp.url_prefix}")
        
        # 测试psutil
        import psutil
        print("✓ psutil库可用")
        
        # 获取系统信息
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('.')
        
        print(f"✓ 系统监控数据获取成功:")
        print(f"  • CPU使用率: {cpu_percent:.1f}%")
        print(f"  • 内存使用率: {memory.percent:.1f}%")
        print(f"  • 磁盘使用率: {(disk.used/disk.total)*100:.1f}%")
        
        return True
    except Exception as e:
        print(f"✗ 系统监控模块测试失败: {e}")
        return False

def test_enhanced_web_app():
    """测试增强的Web应用"""
    print("\n" + "=" * 60)
    print("5. 增强Web应用测试")
    print("=" * 60)
    
    try:
        from app import app
        print("✓ 增强Web应用导入成功")
        
        # 检查新增的蓝图
        blueprint_names = [bp.name for bp in app.blueprints.values()]
        expected_blueprints = ['tushare_api', 'auth', 'admin', 'export', 'monitor']
        
        registered_count = 0
        for bp_name in expected_blueprints:
            if bp_name in blueprint_names:
                print(f"✓ {bp_name} 蓝图已注册")
                registered_count += 1
            else:
                print(f"✗ {bp_name} 蓝图未注册")
        
        print(f"✓ 蓝图注册状态: {registered_count}/{len(expected_blueprints)}")
        
        # 检查路由数量
        routes = list(app.url_map.iter_rules())
        print(f"✓ 总路由数: {len(routes)}")
        
        return registered_count == len(expected_blueprints)
    except Exception as e:
        print(f"✗ 增强Web应用测试失败: {e}")
        return False

def test_template_files():
    """测试模板文件"""
    print("\n" + "=" * 60)
    print("6. 模板文件测试")
    print("=" * 60)
    
    try:
        template_files = [
            'templates/index.html',
            'templates/user_management.html', 
            'templates/api_docs.html',
            'templates/admin_panel.html'
        ]
        
        existing_templates = 0
        for template in template_files:
            if os.path.exists(template):
                print(f"✓ {template} 存在")
                existing_templates += 1
            else:
                print(f"✗ {template} 不存在")
        
        print(f"✓ 模板文件状态: {existing_templates}/{len(template_files)}")
        
        # 检查静态文件
        static_files = [
            'static/mirror.js',
            'static/user_management.js',
            'static/admin_panel.js'
        ]
        
        existing_static = 0
        for static_file in static_files:
            if os.path.exists(static_file):
                print(f"✓ {static_file} 存在")
                existing_static += 1
            else:
                print(f"✗ {static_file} 不存在")
        
        print(f"✓ 静态文件状态: {existing_static}/{len(static_files)}")
        
        return existing_templates == len(template_files) and existing_static == len(static_files)
    except Exception as e:
        print(f"✗ 模板文件测试失败: {e}")
        return False

def test_database_enhancements():
    """测试数据库增强功能"""
    print("\n" + "=" * 60)
    print("7. 数据库增强功能测试")
    print("=" * 60)
    
    try:
        from database.models import User, SyncStatus, db_config
        print("✓ 增强数据模型导入成功")
        
        # 测试数据库连接
        session = db_config.get_session()
        
        try:
            # 测试用户表
            user_count = session.query(User).count()
            print(f"✓ 用户表查询成功: {user_count} 个用户")
            
            # 测试同步状态表
            sync_count = session.query(SyncStatus).count()
            print(f"✓ 同步状态表查询成功: {sync_count} 条记录")
            
            return True
        finally:
            session.close()
            
    except Exception as e:
        print(f"✗ 数据库增强功能测试失败: {e}")
        return False

def start_enhanced_web_server():
    """启动增强的Web服务器"""
    try:
        from app import app, config
        print(f"启动增强Web服务器在端口 {config.WEB_CONFIG['port']}")
        app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)
    except Exception as e:
        print(f"增强Web服务器启动失败: {e}")

def test_api_endpoints():
    """测试新增API端点"""
    print("\n" + "=" * 60)
    print("8. 新增API端点测试")
    print("=" * 60)
    
    try:
        import requests
        base_url = "http://localhost:5000"
        
        # 测试监控端点
        endpoints = [
            ('/api/v1/monitor/system', '系统监控'),
            ('/api/v1/monitor/health', '健康检查'),
            ('/api/v1/monitor/alerts', '系统告警'),
            ('/api/v1/export/tables', '导出表列表'),
        ]
        
        successful_endpoints = 0
        
        for path, name in endpoints:
            try:
                response = requests.get(base_url + path, timeout=5)
                if response.status_code in [200, 401]:  # 401表示需要认证，但端点存在
                    print(f"✓ {name} ({path}) - 端点可访问")
                    successful_endpoints += 1
                else:
                    print(f"✗ {name} ({path}) - 状态码: {response.status_code}")
            except Exception as e:
                print(f"✗ {name} ({path}) - 错误: {e}")
        
        print(f"✓ API端点测试: {successful_endpoints}/{len(endpoints)} 个端点可访问")
        
        return successful_endpoints >= len(endpoints) // 2  # 至少一半端点可访问
        
    except ImportError:
        print("⚠ 需要安装requests库进行API端点测试")
        return True  # 不影响整体测试结果
    except Exception as e:
        print(f"✗ API端点测试失败: {e}")
        return False

def generate_enhanced_report():
    """生成增强功能报告"""
    print("\n" + "=" * 60)
    print("增强功能报告生成")
    print("=" * 60)
    
    try:
        from config import get_config, TUSHARE_TABLES
        
        config = get_config()
        
        report = {
            'generated_at': datetime.now().isoformat(),
            'system_version': '1.1.0',
            'enhanced_features': {
                'admin_panel': '管理员面板 - 用户管理、系统监控',
                'scheduler': '定时任务系统 - 自动化数据同步',
                'export': '数据导出 - CSV/Excel/JSON格式',
                'monitor': '系统监控 - 性能指标、健康检查、告警',
                'enhanced_auth': '增强认证 - 权限管理、Token管理'
            },
            'new_api_endpoints': [
                '/admin/users - 用户管理',
                '/admin/stats - 系统统计',
                '/api/v1/export/data - 数据导出',
                '/api/v1/monitor/system - 系统监控',
                '/api/v1/monitor/health - 健康检查'
            ],
            'dependencies': {
                'apscheduler': '定时任务调度',
                'psutil': '系统监控',
                'openpyxl': 'Excel导出',
                'werkzeug': '安全功能'
            }
        }
        
        # 保存报告
        import json
        with open('enhanced_features_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print("✓ 增强功能报告已生成: enhanced_features_report.json")
        
        # 显示摘要
        print(f"\n增强功能摘要:")
        print(f"  新增功能模块: {len(report['enhanced_features'])} 个")
        print(f"  新增API端点: {len(report['new_api_endpoints'])} 个")
        print(f"  新增依赖包: {len(report['dependencies'])} 个")
        
        return True
        
    except Exception as e:
        print(f"✗ 增强功能报告生成失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Tushare数据镜像系统 - 增强功能测试")
    print("测试时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # 运行各项测试
    tests = [
        ("管理员功能模块", test_admin_module),
        ("定时任务系统", test_scheduler_module),
        ("数据导出功能", test_export_module),
        ("系统监控功能", test_monitor_module),
        ("增强Web应用", test_enhanced_web_app),
        ("模板文件", test_template_files),
        ("数据库增强功能", test_database_enhancements),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        if test_func():
            passed += 1
        else:
            print(f"⚠ {test_name}测试失败，但继续其他测试")
    
    # 启动Web服务器进行API测试
    print(f"\n启动增强Web服务器进行API测试...")
    app_thread = threading.Thread(target=start_enhanced_web_server, daemon=True)
    app_thread.start()
    
    # 等待服务器启动
    time.sleep(3)
    
    # 测试API端点
    if test_api_endpoints():
        passed += 1
    total += 1
    
    # 生成增强功能报告
    if generate_enhanced_report():
        passed += 1
    total += 1
    
    # 显示最终结果
    print("\n" + "=" * 60)
    print("🎯 增强功能测试结果")
    print("=" * 60)
    print(f"总测试项: {total}")
    print(f"通过测试: {passed}")
    print(f"成功率: {(passed/total)*100:.1f}%")
    
    if passed >= total * 0.8:  # 80%通过率
        print("\n🎉 恭喜！增强功能测试基本通过！")
        print("\n✨ 新增功能特性:")
        print("  • 🔧 管理员面板 - 用户管理、系统监控")
        print("  • ⏰ 定时任务系统 - 自动化数据同步")
        print("  • 📊 数据导出功能 - 多格式导出")
        print("  • 📈 系统监控 - 实时性能监控")
        print("  • 🔐 增强认证 - 权限管理")
        
        print("\n🌟 访问地址:")
        print("  • 主控制台: http://localhost:5000")
        print("  • 用户管理: http://localhost:5000/user")
        print("  • 管理员面板: http://localhost:5000/admin")
        print("  • API文档: http://localhost:5000/docs")
        
        print("\n📊 增强功能报告: enhanced_features_report.json")
        
        # 保持服务器运行
        print("\n🌐 增强Web服务器正在运行...")
        print("按 Ctrl+C 停止服务器")
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n👋 服务器已停止，感谢测试增强功能！")
        
        return True
    else:
        print(f"\n❌ 部分增强功能测试失败 ({total-passed}/{total})")
        print("请检查错误信息并修复问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
