#!/usr/bin/env python3
"""
简单的数据同步测试脚本
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入"""
    try:
        print("测试导入...")
        
        from config import TUSHARE_TABLES, get_config
        print("✓ config 导入成功")
        
        from database.models import db_config
        print("✓ database.models 导入成功")
        
        from database.init_db import check_database
        print("✓ database.init_db 导入成功")
        
        from sync.data_sync import TushareDataSync
        print("✓ sync.data_sync 导入成功")
        
        return True
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_database():
    """测试数据库连接"""
    try:
        print("\n测试数据库连接...")
        
        from database.init_db import check_database
        tables_info = check_database()
        
        if tables_info:
            print("✓ 数据库连接成功")
            return True
        else:
            print("✗ 数据库连接失败")
            return False
    except Exception as e:
        print(f"✗ 数据库测试失败: {e}")
        return False

def test_tushare_connection():
    """测试Tushare连接"""
    try:
        print("\n测试Tushare连接...")
        
        import tushare as ts
        from config import get_config
        
        config = get_config()
        ts.set_token(config.TUSHARE_TOKEN)
        pro = ts.pro_api()
        
        # 测试获取少量数据
        df = pro.stock_basic(limit=5)
        
        if not df.empty:
            print(f"✓ Tushare连接成功，获取到 {len(df)} 条股票基础信息")
            print(f"  示例数据: {df.iloc[0]['ts_code']} - {df.iloc[0]['name']}")
            return True
        else:
            print("✗ Tushare连接失败，未获取到数据")
            return False
    except Exception as e:
        print(f"✗ Tushare连接测试失败: {e}")
        return False

def test_simple_sync():
    """测试简单的数据同步"""
    try:
        print("\n测试数据同步...")
        
        from sync.data_sync import TushareDataSync
        
        syncer = TushareDataSync()
        
        # 测试同步股票基础信息（少量数据）
        print("开始同步股票基础信息...")
        result = syncer.sync_table('stock_basic')
        
        if result['status'] == 'success':
            print(f"✓ 同步成功: {result['record_count']} 条记录")
            return True
        else:
            print(f"✗ 同步失败: {result.get('error_message', '未知错误')}")
            return False
    except Exception as e:
        print(f"✗ 数据同步测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("Tushare数据镜像系统测试")
    print("=" * 50)
    
    tests = [
        ("导入测试", test_imports),
        ("数据库测试", test_database),
        ("Tushare连接测试", test_tushare_connection),
        ("数据同步测试", test_simple_sync),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n[{passed+1}/{total}] {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
        else:
            print(f"测试失败，停止后续测试")
            break
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！系统运行正常")
        return True
    else:
        print("✗ 部分测试失败，请检查配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
