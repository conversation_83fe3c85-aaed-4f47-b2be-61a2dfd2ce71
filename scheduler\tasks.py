"""
定时任务系统
自动化数据同步和系统维护任务
"""

from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from datetime import datetime, timedelta
import logging
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sync.data_sync import sync_daily_tables, sync_all_tables
from database.models import db_config, User, SyncStatus
from config import get_config, TUSHARE_TABLES

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TaskScheduler:
    """任务调度器"""
    
    def __init__(self):
        self.scheduler = BackgroundScheduler()
        self.config = get_config()
        self.is_running = False
    
    def start(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("调度器已在运行")
            return
        
        try:
            # 添加定时任务
            self._add_scheduled_tasks()
            
            # 启动调度器
            self.scheduler.start()
            self.is_running = True
            
            logger.info("任务调度器启动成功")
            
        except Exception as e:
            logger.error(f"任务调度器启动失败: {e}")
            raise
    
    def stop(self):
        """停止调度器"""
        if not self.is_running:
            return
        
        try:
            self.scheduler.shutdown()
            self.is_running = False
            logger.info("任务调度器已停止")
        except Exception as e:
            logger.error(f"停止调度器失败: {e}")
    
    def _add_scheduled_tasks(self):
        """添加定时任务"""
        
        # 每日数据同步任务 - 每天早上6点
        self.scheduler.add_job(
            func=self.daily_sync_task,
            trigger=CronTrigger(hour=6, minute=0),
            id='daily_sync',
            name='每日数据同步',
            replace_existing=True
        )
        
        # 每周全量同步基础数据 - 每周日凌晨2点
        self.scheduler.add_job(
            func=self.weekly_full_sync_task,
            trigger=CronTrigger(day_of_week=6, hour=2, minute=0),
            id='weekly_full_sync',
            name='每周全量同步',
            replace_existing=True
        )
        
        # 每日API调用计数重置 - 每天凌晨0点
        self.scheduler.add_job(
            func=self.reset_daily_api_calls,
            trigger=CronTrigger(hour=0, minute=0),
            id='reset_api_calls',
            name='重置API调用计数',
            replace_existing=True
        )
        
        # 系统健康检查 - 每小时
        self.scheduler.add_job(
            func=self.health_check_task,
            trigger=IntervalTrigger(hours=1),
            id='health_check',
            name='系统健康检查',
            replace_existing=True
        )
        
        # 清理旧日志 - 每天凌晨3点
        self.scheduler.add_job(
            func=self.cleanup_old_logs,
            trigger=CronTrigger(hour=3, minute=0),
            id='cleanup_logs',
            name='清理旧日志',
            replace_existing=True
        )
        
        # 数据质量检查 - 每天晚上11点
        self.scheduler.add_job(
            func=self.data_quality_check,
            trigger=CronTrigger(hour=23, minute=0),
            id='data_quality_check',
            name='数据质量检查',
            replace_existing=True
        )
        
        logger.info("已添加6个定时任务")
    
    def daily_sync_task(self):
        """每日数据同步任务"""
        logger.info("开始执行每日数据同步任务")
        
        try:
            # 同步每日更新的表
            results = sync_daily_tables()
            
            success_count = sum(1 for result in results.values() if result['status'] == 'success')
            total_records = sum(result['record_count'] for result in results.values())
            
            logger.info(f"每日同步完成: {success_count}/{len(results)} 个表成功, 总计 {total_records:,} 条记录")
            
            # 记录任务执行结果
            self._log_task_result('daily_sync', True, f"同步了 {total_records:,} 条记录")
            
        except Exception as e:
            logger.error(f"每日数据同步失败: {e}")
            self._log_task_result('daily_sync', False, str(e))
    
    def weekly_full_sync_task(self):
        """每周全量同步任务"""
        logger.info("开始执行每周全量同步任务")
        
        try:
            # 只同步基础数据表（全量同步）
            basic_tables = [
                name for name, config in TUSHARE_TABLES.items() 
                if config['sync_type'] == 'full'
            ]
            
            from sync.data_sync import TushareDataSync
            syncer = TushareDataSync()
            
            total_records = 0
            success_count = 0
            
            for table_name in basic_tables:
                try:
                    result = syncer.sync_table(table_name, force_full=True)
                    if result['status'] == 'success':
                        success_count += 1
                        total_records += result['record_count']
                        logger.info(f"表 {table_name} 全量同步成功: {result['record_count']:,} 条记录")
                    else:
                        logger.error(f"表 {table_name} 全量同步失败: {result.get('error_message')}")
                except Exception as e:
                    logger.error(f"表 {table_name} 全量同步异常: {e}")
            
            logger.info(f"每周全量同步完成: {success_count}/{len(basic_tables)} 个表成功, 总计 {total_records:,} 条记录")
            self._log_task_result('weekly_full_sync', True, f"同步了 {total_records:,} 条记录")
            
        except Exception as e:
            logger.error(f"每周全量同步失败: {e}")
            self._log_task_result('weekly_full_sync', False, str(e))
    
    def reset_daily_api_calls(self):
        """重置每日API调用计数"""
        logger.info("开始重置每日API调用计数")
        
        try:
            session = db_config.get_session()
            
            try:
                # 重置所有用户的每日调用计数
                updated = session.query(User).update({'api_calls_today': 0})
                session.commit()
                
                logger.info(f"已重置 {updated} 个用户的API调用计数")
                self._log_task_result('reset_api_calls', True, f"重置了 {updated} 个用户")
                
            except Exception as e:
                session.rollback()
                raise e
            finally:
                session.close()
                
        except Exception as e:
            logger.error(f"重置API调用计数失败: {e}")
            self._log_task_result('reset_api_calls', False, str(e))
    
    def health_check_task(self):
        """系统健康检查"""
        logger.info("开始系统健康检查")
        
        try:
            issues = []
            
            # 检查数据库连接
            try:
                session = db_config.get_session()
                session.execute("SELECT 1")
                session.close()
            except Exception as e:
                issues.append(f"数据库连接异常: {e}")
            
            # 检查Tushare API连接
            try:
                import tushare as ts
                ts.set_token(self.config.TUSHARE_TOKEN)
                pro = ts.pro_api()
                df = pro.stock_basic(limit=1)
                if df.empty:
                    issues.append("Tushare API返回空数据")
            except Exception as e:
                issues.append(f"Tushare API连接异常: {e}")
            
            # 检查磁盘空间
            try:
                import shutil
                total, used, free = shutil.disk_usage('.')
                free_gb = free // (1024**3)
                if free_gb < 1:  # 少于1GB
                    issues.append(f"磁盘空间不足: 剩余 {free_gb}GB")
            except Exception as e:
                issues.append(f"磁盘空间检查失败: {e}")
            
            if issues:
                logger.warning(f"健康检查发现 {len(issues)} 个问题: {'; '.join(issues)}")
                self._log_task_result('health_check', False, '; '.join(issues))
            else:
                logger.info("系统健康检查通过")
                self._log_task_result('health_check', True, "系统运行正常")
                
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            self._log_task_result('health_check', False, str(e))
    
    def cleanup_old_logs(self):
        """清理旧日志"""
        logger.info("开始清理旧日志")
        
        try:
            session = db_config.get_session()
            
            try:
                # 清理30天前的错误同步记录
                cutoff_date = datetime.now() - timedelta(days=30)
                deleted = session.query(SyncStatus).filter(
                    SyncStatus.last_sync_time < cutoff_date,
                    SyncStatus.sync_status == 'error'
                ).delete()
                
                session.commit()
                
                logger.info(f"已清理 {deleted} 条旧的错误日志记录")
                self._log_task_result('cleanup_logs', True, f"清理了 {deleted} 条记录")
                
            except Exception as e:
                session.rollback()
                raise e
            finally:
                session.close()
                
        except Exception as e:
            logger.error(f"清理旧日志失败: {e}")
            self._log_task_result('cleanup_logs', False, str(e))
    
    def data_quality_check(self):
        """数据质量检查"""
        logger.info("开始数据质量检查")
        
        try:
            session = db_config.get_session()
            issues = []
            
            try:
                # 检查各表的数据更新情况
                for table_name, config in TUSHARE_TABLES.items():
                    if config['sync_frequency'] == 'daily':
                        # 检查每日更新的表是否有最新数据
                        sync_status = session.query(SyncStatus).filter_by(table_name=table_name).first()
                        
                        if sync_status:
                            if sync_status.last_sync_date:
                                last_sync = datetime.strptime(sync_status.last_sync_date, '%Y%m%d').date()
                                days_ago = (datetime.now().date() - last_sync).days
                                
                                if days_ago > 3:  # 超过3天未更新
                                    issues.append(f"表 {table_name} 已 {days_ago} 天未更新")
                            else:
                                issues.append(f"表 {table_name} 从未同步过")
                        else:
                            issues.append(f"表 {table_name} 无同步记录")
                
                if issues:
                    logger.warning(f"数据质量检查发现 {len(issues)} 个问题: {'; '.join(issues)}")
                    self._log_task_result('data_quality_check', False, '; '.join(issues))
                else:
                    logger.info("数据质量检查通过")
                    self._log_task_result('data_quality_check', True, "数据质量正常")
                
            finally:
                session.close()
                
        except Exception as e:
            logger.error(f"数据质量检查失败: {e}")
            self._log_task_result('data_quality_check', False, str(e))
    
    def _log_task_result(self, task_name: str, success: bool, message: str):
        """记录任务执行结果"""
        try:
            # 这里可以扩展为写入专门的任务日志表
            status = "SUCCESS" if success else "FAILED"
            logger.info(f"任务 {task_name} {status}: {message}")
        except Exception as e:
            logger.error(f"记录任务结果失败: {e}")
    
    def get_job_status(self):
        """获取任务状态"""
        if not self.is_running:
            return {"status": "stopped", "jobs": []}
        
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                "id": job.id,
                "name": job.name,
                "next_run": job.next_run_time.isoformat() if job.next_run_time else None,
                "trigger": str(job.trigger)
            })
        
        return {
            "status": "running",
            "jobs": jobs,
            "job_count": len(jobs)
        }

# 全局调度器实例
scheduler = TaskScheduler()

def start_scheduler():
    """启动调度器"""
    scheduler.start()

def stop_scheduler():
    """停止调度器"""
    scheduler.stop()

def get_scheduler_status():
    """获取调度器状态"""
    return scheduler.get_job_status()

if __name__ == "__main__":
    # 直接运行时启动调度器
    print("启动任务调度器...")
    start_scheduler()
    
    try:
        import time
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("停止任务调度器...")
        stop_scheduler()
