#!/usr/bin/env python3
"""
简单的API模块测试
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_api_imports():
    """测试API模块导入"""
    try:
        print("测试API模块导入...")
        
        from api.tushare_api import api_bp, TushareAPIHandler
        print("✓ tushare_api 导入成功")
        
        from api.auth import auth_bp, generate_api_token
        print("✓ auth 导入成功")
        
        # 测试token生成
        token = generate_api_token()
        print(f"✓ API token生成成功: {token[:10]}...")
        
        return True
    except Exception as e:
        print(f"✗ API模块导入失败: {e}")
        return False

def test_api_handler():
    """测试API处理器"""
    try:
        print("\n测试API处理器...")
        
        from api.tushare_api import TushareAPIHandler
        from config import API_ENDPOINTS
        
        # 创建处理器
        handler = TushareAPIHandler()
        print("✓ API处理器创建成功")
        
        # 测试查询（不需要实际数据）
        print(f"✓ 支持的API端点: {len(API_ENDPOINTS)} 个")
        print(f"  端点列表: {', '.join(API_ENDPOINTS.keys())}")
        
        return True
    except Exception as e:
        print(f"✗ API处理器测试失败: {e}")
        return False

def test_flask_app_with_api():
    """测试Flask应用集成API"""
    try:
        print("\n测试Flask应用集成...")
        
        from app import app
        print("✓ Flask应用导入成功")
        
        # 检查蓝图注册
        blueprint_names = [bp.name for bp in app.blueprints.values()]
        print(f"✓ 注册的蓝图: {blueprint_names}")
        
        if 'tushare_api' in blueprint_names:
            print("✓ Tushare API蓝图已注册")
        else:
            print("✗ Tushare API蓝图未注册")
            return False
        
        if 'auth' in blueprint_names:
            print("✓ 认证蓝图已注册")
        else:
            print("✗ 认证蓝图未注册")
            return False
        
        return True
    except Exception as e:
        print(f"✗ Flask应用集成测试失败: {e}")
        return False

def test_database_user_model():
    """测试用户数据模型"""
    try:
        print("\n测试用户数据模型...")
        
        from database.models import User, db_config
        print("✓ 用户模型导入成功")
        
        # 测试创建用户实例（不保存到数据库）
        test_user = User(
            username="test_user",
            email="<EMAIL>",
            password_hash="test_hash",
            api_token="test_token"
        )
        
        print(f"✓ 用户实例创建成功: {test_user.username}")
        
        return True
    except Exception as e:
        print(f"✗ 用户数据模型测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("Tushare兼容API模块测试")
    print("=" * 50)
    
    tests = [
        ("API模块导入", test_api_imports),
        ("API处理器", test_api_handler),
        ("Flask应用集成", test_flask_app_with_api),
        ("用户数据模型", test_database_user_model),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n[{passed+1}/{total}] {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
        else:
            print(f"测试失败，继续下一个测试")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有API模块测试通过！")
        print("\n🎉 API系统已准备就绪！")
        print("\n功能特性:")
        print("  • 用户注册和认证系统")
        print("  • API token管理")
        print("  • 与Tushare兼容的数据接口")
        print("  • 请求限流和权限控制")
        print("  • 支持GET和POST请求")
        print("\n使用方法:")
        print("  1. 启动服务器: python app.py")
        print("  2. 注册用户: POST /auth/register")
        print("  3. 获取数据: GET /api/v1/stock_basic?token=your_token")
        print("  4. 查看API信息: GET /api/v1/info")
        
        return True
    else:
        print("✗ 部分API模块测试失败，请检查配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
