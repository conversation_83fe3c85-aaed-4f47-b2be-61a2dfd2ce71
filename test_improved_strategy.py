#!/usr/bin/env python3
"""
测试改进后的策略
"""

import sys
import os
import pandas as pd

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_improved_strategy():
    """测试改进后的策略"""
    print("🧪 测试改进后的策略...")
    
    try:
        from strategy_backtest import StrategyBacktest
        
        # 创建回测实例，使用较短的时间范围
        backtest = StrategyBacktest()
        backtest.start_date = '20250801'  # 使用有数据的日期
        backtest.end_date = '20250801'
        
        print(f"📅 测试期间: {backtest.start_date} - {backtest.end_date}")
        
        # 测试数据加载
        print("\n📊 测试数据加载...")
        if backtest.load_data():
            print("✅ 数据加载成功")
            
            print(f"  股票基础信息: {len(backtest.stock_basic)} 只")
            print(f"  日线数据: {len(backtest.daily_data)} 条")
            print(f"  基本面数据: {len(backtest.daily_basic)} 条")
            print(f"  合并后数据: {len(backtest.stock_data)} 条")
            print(f"  交易日数: {len(backtest.trade_dates)}")
            
            # 检查是否包含total_mv字段
            if 'total_mv' in backtest.stock_data.columns:
                print("  ✅ 包含total_mv字段，可以进行市值加权计算")
                mv_valid = backtest.stock_data['total_mv'].notna().sum()
                print(f"  有效市值数据: {mv_valid}/{len(backtest.stock_data)}")
            else:
                print("  ⚠️  缺少total_mv字段，将使用简单平均")
            
            if len(backtest.stock_data) > 0:
                print("\n📊 数据样本:")
                sample = backtest.stock_data.head(3)
                print(f"  PE数据类型: {sample['pe_ttm'].dtype}")
                print(f"  PB数据类型: {sample['pb'].dtype}")
                if 'total_mv' in sample.columns:
                    print(f"  总市值数据类型: {sample['total_mv'].dtype}")
                
                # 测试单日计算
                if len(backtest.trade_dates) > 0:
                    test_date = backtest.trade_dates[0]
                    print(f"\n🧪 测试改进后的计算 ({test_date})...")
                    
                    try:
                        # 测试改进后的大盘PB计算
                        market_pb = backtest.calculate_market_pb(test_date)
                        print(f"  ✅ 改进后大盘PB: {market_pb:.3f}")
                        
                        # 对比原始方法
                        date_str = str(test_date)
                        date_data = backtest.stock_data[
                            (backtest.stock_data['trade_date'].astype(str) == date_str) &
                            (backtest.stock_data['pb'] > 0) &
                            (backtest.stock_data['pb'].notna())
                        ]
                        
                        if len(date_data) > 0:
                            simple_median = date_data['pb'].median()
                            print(f"  📊 对比-简单中位数: {simple_median:.3f}")
                            print(f"  📊 改进幅度: {((market_pb/simple_median-1)*100):+.1f}%")
                            
                            # 显示PB分布
                            print(f"  📈 PB分布: 最小{date_data['pb'].min():.2f}, "
                                  f"中位{date_data['pb'].median():.2f}, "
                                  f"最大{date_data['pb'].max():.2f}")
                        
                        # 测试股票评分
                        scored_stocks = backtest.calculate_stock_scores(test_date)
                        print(f"  ✅ 评分股票数: {len(scored_stocks)}")
                        
                        if len(scored_stocks) > 0:
                            top_stock = scored_stocks.iloc[0]
                            print(f"  ✅ 最高分股票: {top_stock['ts_code']} (分数: {top_stock['total_score']:.1f})")
                        
                        # 测试仓位计算
                        position_weight = backtest.get_position_weight(test_date)
                        print(f"  ✅ 目标仓位: {position_weight:.1%}")
                        
                        # 测试择时信号
                        if market_pb > 2.0:
                            signal = "买入信号" if market_pb < 2.5 else "观望"
                        else:
                            signal = "满仓信号"
                        print(f"  🎯 择时信号: {signal} (PB={market_pb:.2f})")
                        
                        print("🎉 改进后计算测试通过！")
                        return True
                        
                    except Exception as e:
                        print(f"❌ 改进后计算失败: {e}")
                        import traceback
                        traceback.print_exc()
                        return False
                else:
                    print("⚠️  没有交易日数据")
                    return False
            else:
                print("❌ 没有合并后的股票数据")
                return False
        else:
            print("❌ 数据加载失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_st_filtering():
    """测试ST股票过滤"""
    print("\n🔍 测试ST股票过滤...")
    
    try:
        from database.models import db_config
        
        session = db_config.get_session()
        
        # 获取所有股票
        all_stocks_sql = "SELECT ts_code, name FROM stock_basic"
        all_stocks = pd.read_sql(all_stocks_sql, session.bind)
        print(f"全部股票: {len(all_stocks)} 只")
        
        # 应用改进的过滤规则
        risk_patterns = [
            r'\*ST',      # *ST股票
            r'ST(?!A)',   # ST股票（但不包括STA等正常名称）
            r'退市',      # 退市股票
            r'暂停',      # 暂停上市
            r'终止',      # 终止上市
            r'PT',        # PT股票
        ]
        
        risk_pattern = '|'.join(risk_patterns)
        
        valid_stocks = all_stocks[
            ~all_stocks['name'].str.contains(risk_pattern, na=False, regex=True)
        ]
        
        filtered_out = all_stocks[
            all_stocks['name'].str.contains(risk_pattern, na=False, regex=True)
        ]
        
        print(f"过滤后股票: {len(valid_stocks)} 只")
        print(f"被过滤股票: {len(filtered_out)} 只")
        
        if len(filtered_out) > 0:
            print("\n被过滤的股票样本:")
            print(filtered_out.head(10)[['ts_code', 'name']].to_string())
        
        session.close()
        
        return True
        
    except Exception as e:
        print(f"❌ ST股票过滤测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 改进后策略测试")
    print("=" * 40)
    
    # 测试1: 改进后的策略计算
    if not test_improved_strategy():
        print("❌ 改进后策略测试失败")
        return
    
    # 测试2: ST股票过滤
    if not test_st_filtering():
        print("❌ ST股票过滤测试失败")
        return
    
    print("\n🎉 所有测试通过！")
    print("✅ 策略改进成功，主要改进:")
    print("  1. 大盘PB使用市值加权平均（去极值）")
    print("  2. ST股票过滤更加全面")
    print("  3. 数据处理更加稳健")
    print("\n💡 现在可以运行完整回测:")
    print("   python run_strategy_backtest.py")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
