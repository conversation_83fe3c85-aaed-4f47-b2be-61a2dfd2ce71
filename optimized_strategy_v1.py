# 优化版量化策略 - 融合投资大师理念
# 结合巴菲特价值投资、彼得林奇成长策略、格雷厄姆安全边际理念
# 适配A股市场特点：政策敏感、资金流向、题材轮动

import pandas as pd
import numpy as np
import jqdata
import datetime

def initialize(context):
    """初始化策略"""
    set_benchmark('000300.XSHG')
    log.set_level('order', 'error')
    set_option('use_real_price', True)
    set_option('avoid_future_data', True)
    set_slippage(FixedSlippage(0.02))
    
    # === 策略参数 ===
    # 核心-卫星配置
    g.core_stocks = 8      # 核心持仓数量（价值股）
    g.satellite_stocks = 7  # 卫星持仓数量（成长股）
    g.total_stocks = g.core_stocks + g.satellite_stocks
    
    # 仓位管理参数
    g.max_position = 0.95   # 最大仓位
    g.min_position = 0.30   # 最小仓位
    g.core_weight = 0.70    # 核心持仓权重
    g.satellite_weight = 0.30  # 卫星持仓权重
    
    # 风险控制参数
    g.max_single_weight = 0.10  # 单股最大权重
    g.max_industry_weight = 0.30  # 单行业最大权重
    g.stop_loss = -0.20     # 个股止损线
    g.portfolio_stop_loss = -0.15  # 组合止损线
    
    # 市场环境判断参数
    g.market_pe_high = 18   # 市场高估值线
    g.market_pe_low = 12    # 市场低估值线
    g.vix_high = 25         # 恐慌指数高位
    
    # 持仓记录
    g.position_cost = {}
    g.hold_days = {}
    g.last_rebalance = None
    
    # 调度任务
    run_daily(before_trading_start, time='before_open')
    run_daily(market_timing, time='09:15')  # 市场择时
    run_weekly(core_rebalance, weekday=1, time='10:00')  # 核心持仓月度调仓
    run_weekly(satellite_rebalance, weekday=3, time='10:00')  # 卫星持仓双周调仓
    run_daily(risk_management, time='14:00')  # 风险管理
    run_daily(after_trading_end, time='after_close')

def before_trading_start(context):
    """开盘前准备"""
    # 更新持仓天数
    for stock in context.portfolio.positions:
        if stock not in g.hold_days:
            g.hold_days[stock] = 0
        g.hold_days[stock] += 1
    
    # 获取市场环境信息
    g.market_env = get_market_environment(context)
    log.info(f"市场环境: {g.market_env}")

def get_market_environment(context):
    """判断市场环境"""
    # 获取沪深300 PE
    index_pe = get_index_pe('000300.XSHG')
    
    # 获取北向资金流向（简化版）
    north_money_flow = get_north_money_flow()
    
    # 综合判断市场环境
    if index_pe > g.market_pe_high:
        return "高估值"
    elif index_pe < g.market_pe_low:
        return "低估值" 
    elif north_money_flow > 0:
        return "资金流入"
    else:
        return "震荡"

def get_index_pe(index_code):
    """获取指数PE"""
    stocks = get_index_stocks(index_code)
    q = query(valuation.code, valuation.pe_ratio, valuation.market_cap).filter(
        valuation.code.in_(stocks),
        valuation.pe_ratio > 0,
        valuation.pe_ratio < 100
    )
    df = get_fundamentals(q)
    if len(df) == 0:
        return 15
    # 市值加权PE
    total_cap = df['market_cap'].sum()
    weighted_pe = (df['market_cap'] * df['pe_ratio']).sum() / total_cap
    return weighted_pe

def get_north_money_flow():
    """获取北向资金流向（简化版）"""
    # 这里简化处理，实际可以通过更复杂的方法获取
    return np.random.choice([-1, 1])  # 随机返回流入或流出

def market_timing(context):
    """市场择时"""
    target_position = calculate_target_position()
    current_position = get_current_position_ratio(context)
    
    log.info(f"目标仓位: {target_position:.1%}, 当前仓位: {current_position:.1%}")
    
    # 如果需要大幅调整仓位
    if abs(target_position - current_position) > 0.20:
        adjust_position(context, target_position)

def calculate_target_position():
    """计算目标仓位"""
    base_position = 0.70  # 基础仓位
    
    # 根据市场环境调整
    if g.market_env == "高估值":
        return max(g.min_position, base_position - 0.30)
    elif g.market_env == "低估值":
        return min(g.max_position, base_position + 0.25)
    elif g.market_env == "资金流入":
        return min(g.max_position, base_position + 0.15)
    else:
        return base_position

def get_current_position_ratio(context):
    """获取当前仓位比例"""
    total_value = context.portfolio.total_value
    cash = context.portfolio.available_cash
    return 1 - cash / total_value

def adjust_position(context, target_position):
    """调整仓位"""
    current_position = get_current_position_ratio(context)
    
    if target_position < current_position:
        # 需要减仓
        reduce_ratio = (current_position - target_position) / current_position
        for stock in context.portfolio.positions:
            current_value = context.portfolio.positions[stock].value
            new_value = current_value * (1 - reduce_ratio)
            order_target_value(stock, new_value)
        log.info(f"减仓 {reduce_ratio:.1%}")

def core_rebalance(context):
    """核心持仓调仓（价值股）"""
    log.info("=== 核心持仓调仓 ===")
    
    # 获取价值股票池
    value_stocks = get_value_stock_pool(context)
    
    if len(value_stocks) == 0:
        log.info("没有符合条件的价值股")
        return
    
    # 选择核心持仓
    selected_core = value_stocks[:g.core_stocks]
    
    # 执行调仓
    execute_rebalance(context, selected_core, g.core_weight, "核心")

def satellite_rebalance(context):
    """卫星持仓调仓（成长股）"""
    log.info("=== 卫星持仓调仓 ===")
    
    # 获取成长股票池
    growth_stocks = get_growth_stock_pool(context)
    
    if len(growth_stocks) == 0:
        log.info("没有符合条件的成长股")
        return
    
    # 选择卫星持仓
    selected_satellite = growth_stocks[:g.satellite_stocks]
    
    # 执行调仓
    execute_rebalance(context, selected_satellite, g.satellite_weight, "卫星")

def get_value_stock_pool(context):
    """获取价值股票池（巴菲特+格雷厄姆风格）"""
    # 基础股票池
    stocks = get_basic_stock_pool(context)
    
    # 价值股筛选条件
    q = query(
        valuation.code,
        valuation.pe_ratio,
        valuation.pb_ratio,
        valuation.market_cap,
        indicator.roe,
        indicator.roa,
        balance.total_liability,
        balance.total_assets,
        cash_flow.net_operate_cash_flow,
        income.net_profit
    ).filter(
        valuation.code.in_(stocks),
        valuation.pe_ratio > 0,
        valuation.pe_ratio < 20,  # PE < 20
        valuation.pb_ratio > 0,
        valuation.pb_ratio < 3,   # PB < 3
        indicator.roe > 15,       # ROE > 15%
        indicator.roa > 5,        # ROA > 5%
        valuation.market_cap > 100  # 市值 > 100亿
    )
    
    df = get_fundamentals(q)
    
    if len(df) == 0:
        return []
    
    # 计算质量评分
    df['debt_ratio'] = df['total_liability'] / df['total_assets']
    df['cash_quality'] = df['net_operate_cash_flow'] / df['net_profit']
    
    # 过滤高质量股票
    df = df[
        (df['debt_ratio'] < 0.6) &  # 负债率 < 60%
        (df['cash_quality'] > 0.8)   # 现金流质量好
    ]
    
    # 计算综合评分
    df['value_score'] = (
        (20 - df['pe_ratio']) * 0.3 +  # PE评分
        (3 - df['pb_ratio']) * 0.2 +   # PB评分
        df['roe'] * 0.3 +               # ROE评分
        (1 - df['debt_ratio']) * 0.2    # 负债率评分
    )
    
    # 排序并返回
    df = df.sort_values('value_score', ascending=False)
    return df['code'].tolist()

def get_growth_stock_pool(context):
    """获取成长股票池（彼得林奇风格）"""
    # 基础股票池
    stocks = get_basic_stock_pool(context)
    
    # 成长股筛选条件
    q = query(
        valuation.code,
        valuation.pe_ratio,
        valuation.market_cap,
        indicator.inc_revenue_year_on_year,
        indicator.inc_net_profit_year_on_year,
        indicator.roe
    ).filter(
        valuation.code.in_(stocks),
        valuation.pe_ratio > 0,
        valuation.pe_ratio < 50,  # PE < 50
        indicator.inc_revenue_year_on_year > 15,  # 营收增长 > 15%
        indicator.inc_net_profit_year_on_year > 20,  # 净利润增长 > 20%
        indicator.roe > 12,  # ROE > 12%
        valuation.market_cap > 50  # 市值 > 50亿
    )
    
    df = get_fundamentals(q)
    
    if len(df) == 0:
        return []
    
    # 计算PEG
    df['peg'] = df['pe_ratio'] / df['inc_net_profit_year_on_year']
    
    # 过滤PEG < 1.5的股票
    df = df[df['peg'] < 1.5]
    
    # 计算成长评分
    df['growth_score'] = (
        df['inc_revenue_year_on_year'] * 0.3 +
        df['inc_net_profit_year_on_year'] * 0.4 +
        (1.5 - df['peg']) * 20 * 0.3  # PEG越小评分越高
    )
    
    # 排序并返回
    df = df.sort_values('growth_score', ascending=False)
    return df['code'].tolist()

def get_basic_stock_pool(context):
    """获取基础股票池"""
    # 获取全部A股
    stocks = list(get_all_securities(['stock']).index)
    
    # 基础过滤
    current_data = get_current_data()
    valid_stocks = []
    
    for stock in stocks:
        data = current_data[stock]
        # 过滤条件
        if (not data.paused and 
            not data.is_st and 
            'ST' not in data.name and
            '*' not in data.name and
            '退' not in data.name and
            stock[:2] not in ['68', '30', '00'] or stock[:3] == '000'):  # 排除科创板和部分创业板
            
            # 检查上市时间（排除次新股）
            security_info = get_security_info(stock)
            if (context.current_dt.date() - security_info.start_date).days > 365:
                valid_stocks.append(stock)
    
    return valid_stocks

def execute_rebalance(context, selected_stocks, total_weight, position_type):
    """执行调仓"""
    if len(selected_stocks) == 0:
        return
    
    # 计算每只股票权重
    weight_per_stock = min(total_weight / len(selected_stocks), g.max_single_weight)
    
    # 获取当前对应类型的持仓
    current_positions = get_current_positions_by_type(context, position_type)
    
    # 卖出不在新列表中的股票
    for stock in current_positions:
        if stock not in selected_stocks:
            order_target(stock, 0)
            if stock in g.position_cost:
                del g.position_cost[stock]
            if stock in g.hold_days:
                del g.hold_days[stock]
            log.info(f"卖出{position_type}持仓: {stock}")

    # 买入新股票
    buy_count = 0
    total_value = context.portfolio.total_value
    target_value = total_value * weight_per_stock

    for stock in selected_stocks:
        if stock not in current_positions:
            order_target_value(stock, target_value)
            g.position_cost[stock] = get_current_data()[stock].last_price
            g.hold_days[stock] = 0
            buy_count += 1
            log.info(f"买入{position_type}持仓: {stock}")

            if buy_count >= 3:  # 每次最多买入3只
                break

def get_current_positions_by_type(context, position_type):
    """根据类型获取当前持仓（简化版）"""
    # 这里简化处理，实际可以通过标记来区分核心和卫星持仓
    positions = list(context.portfolio.positions.keys())
    if position_type == "核心":
        return positions[:len(positions)//2]
    else:
        return positions[len(positions)//2:]

def risk_management(context):
    """风险管理"""
    current_data = get_current_data()
    
    # 个股止损
    for stock in list(context.portfolio.positions.keys()):
        position = context.portfolio.positions[stock]
        if position.total_amount == 0:
            continue
            
        cost = g.position_cost.get(stock, position.avg_cost)
        current_price = current_data[stock].last_price
        
        if current_price > 0 and cost > 0:
            return_rate = current_price / cost - 1
            
            # 止损
            if return_rate <= g.stop_loss:
                order_target_percent(stock, 0)
                log.info(f"止损卖出: {stock}, 亏损: {return_rate:.1%}")
                if stock in g.position_cost:
                    del g.position_cost[stock]
                if stock in g.hold_days:
                    del g.hold_days[stock]
            
            # 长期持有无表现的股票
            elif g.hold_days.get(stock, 0) > 252 and return_rate < 0.05:  # 持有超过1年且收益<5%
                order_target_percent(stock, 0)
                log.info(f"长期无表现卖出: {stock}, 持有{g.hold_days[stock]}天")
                if stock in g.position_cost:
                    del g.position_cost[stock]
                if stock in g.hold_days:
                    del g.hold_days[stock]

def after_trading_end(context):
    """收盘后统计"""
    total_value = context.portfolio.total_value
    positions = len([p for p in context.portfolio.positions.values() if p.total_amount > 0])
    position_ratio = get_current_position_ratio(context)
    
    log.info(f"收盘 - 总资产: {total_value:.0f}, 持仓: {positions}只, 仓位: {position_ratio:.1%}")
