#!/usr/bin/env python3
"""
快速测试数据下载优化功能
验证所有改进是否正常工作
"""

import sys
import os
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试增强版同步模块
        from sync.enhanced_data_sync import EnhancedTushareDataSync
        print("  ✅ 增强版同步模块导入成功")
        
        # 测试配置
        from config import get_config, TUSHARE_TABLES
        config = get_config()
        print(f"  ✅ 配置加载成功，支持 {len(TUSHARE_TABLES)} 个数据表")
        
        # 测试数据库模块
        from database.models import db_config
        print("  ✅ 数据库模块导入成功")
        
        # 测试API模块
        from api.admin import admin_bp
        from api.export import export_bp
        from api.monitor import monitor_bp
        print("  ✅ 新增API模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 模块导入失败: {e}")
        return False

def test_enhanced_syncer():
    """测试增强版同步器"""
    print("\n🔧 测试增强版同步器...")
    
    try:
        from sync.enhanced_data_sync import EnhancedTushareDataSync
        
        # 创建同步器实例
        syncer = EnhancedTushareDataSync()
        print("  ✅ 同步器创建成功")
        
        # 测试进度回调
        progress_received = False
        def test_callback(data):
            nonlocal progress_received
            progress_received = True
            print(f"  📊 收到进度回调: {data.get('progress', 0)}%")
        
        syncer_with_callback = EnhancedTushareDataSync(progress_callback=test_callback)
        print("  ✅ 进度回调功能正常")
        
        # 测试取消功能
        syncer.cancel_sync()
        progress = syncer.get_sync_progress()
        if progress.get('is_cancelled'):
            print("  ✅ 取消功能正常")
        else:
            print("  ⚠️  取消功能可能有问题")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 增强版同步器测试失败: {e}")
        return False

def test_cli_tool():
    """测试命令行工具"""
    print("\n💻 测试增强版命令行工具...")
    
    try:
        # 检查文件是否存在
        if os.path.exists('sync_cli_enhanced.py'):
            print("  ✅ 增强版命令行工具文件存在")
            
            # 尝试导入
            import importlib.util
            spec = importlib.util.spec_from_file_location("sync_cli_enhanced", "sync_cli_enhanced.py")
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            print("  ✅ 命令行工具模块加载成功")
            
            return True
        else:
            print("  ❌ 增强版命令行工具文件不存在")
            return False
            
    except Exception as e:
        print(f"  ❌ 命令行工具测试失败: {e}")
        return False

def test_web_app():
    """测试Web应用"""
    print("\n🌐 测试Web应用...")
    
    try:
        from app import app
        
        # 检查蓝图注册
        blueprint_names = [bp.name for bp in app.blueprints.values()]
        expected_blueprints = ['tushare_api', 'auth', 'admin', 'export', 'monitor']
        
        registered_count = 0
        for bp_name in expected_blueprints:
            if bp_name in blueprint_names:
                registered_count += 1
        
        print(f"  ✅ 蓝图注册: {registered_count}/{len(expected_blueprints)} 个")
        
        # 检查路由数量
        routes = list(app.url_map.iter_rules())
        print(f"  ✅ 总路由数: {len(routes)}")
        
        return registered_count >= len(expected_blueprints) * 0.8  # 80%通过
        
    except Exception as e:
        print(f"  ❌ Web应用测试失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    print("\n🗄️  测试数据库连接...")
    
    try:
        from database.models import db_config
        
        session = db_config.get_session()
        try:
            # 简单查询测试
            from sqlalchemy import text
            result = session.execute(text("SELECT 1"))
            if result.scalar() == 1:
                print("  ✅ 数据库连接正常")
                return True
            else:
                print("  ❌ 数据库查询异常")
                return False
        finally:
            session.close()
            
    except Exception as e:
        print(f"  ❌ 数据库连接测试失败: {e}")
        return False

def test_api_endpoints():
    """测试API端点"""
    print("\n🔌 测试API端点...")
    
    try:
        from app import app
        
        # 创建测试客户端
        with app.test_client() as client:
            # 测试基本端点
            endpoints_to_test = [
                ('/', 'GET', '主页'),
                ('/api/tables', 'GET', '数据表列表'),
                ('/api/database_status', 'GET', '数据库状态'),
            ]
            
            success_count = 0
            for endpoint, method, name in endpoints_to_test:
                try:
                    if method == 'GET':
                        response = client.get(endpoint)
                    else:
                        response = client.post(endpoint)
                    
                    if response.status_code in [200, 401, 403]:  # 200正常，401/403需要认证但端点存在
                        print(f"  ✅ {name} ({endpoint}) - 状态码: {response.status_code}")
                        success_count += 1
                    else:
                        print(f"  ⚠️  {name} ({endpoint}) - 状态码: {response.status_code}")
                        
                except Exception as e:
                    print(f"  ❌ {name} ({endpoint}) - 错误: {e}")
            
            print(f"  📊 API端点测试: {success_count}/{len(endpoints_to_test)} 个正常")
            return success_count >= len(endpoints_to_test) * 0.7  # 70%通过
            
    except Exception as e:
        print(f"  ❌ API端点测试失败: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    print("\n📁 测试文件结构...")
    
    required_files = [
        'sync/enhanced_data_sync.py',
        'sync_cli_enhanced.py',
        'api/admin.py',
        'api/export.py',
        'api/monitor.py',
        'scheduler/tasks.py',
        'templates/admin_panel.html',
        'static/admin_panel.js',
        'deploy/docker-compose.yml',
        'DATA_DOWNLOAD_OPTIMIZATION.md'
    ]
    
    existing_files = 0
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"  ✅ {file_path}")
            existing_files += 1
        else:
            print(f"  ❌ {file_path} - 缺失")
    
    print(f"  📊 文件完整性: {existing_files}/{len(required_files)} 个文件存在")
    return existing_files >= len(required_files) * 0.8  # 80%通过

def main():
    """主测试函数"""
    print("🚀 数据下载优化功能快速测试")
    print("=" * 50)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试项目
    tests = [
        ("模块导入", test_imports),
        ("增强版同步器", test_enhanced_syncer),
        ("命令行工具", test_cli_tool),
        ("Web应用", test_web_app),
        ("数据库连接", test_database_connection),
        ("API端点", test_api_endpoints),
        ("文件结构", test_file_structure),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"  ⚠️  {test_name} 部分功能可能有问题")
        except Exception as e:
            print(f"  ❌ {test_name} 测试异常: {e}")
    
    # 显示结果
    print("\n" + "=" * 50)
    print("🎯 快速测试结果")
    print("=" * 50)
    print(f"总测试项: {total}")
    print(f"通过测试: {passed}")
    print(f"成功率: {(passed/total)*100:.1f}%")
    
    if passed >= total * 0.8:  # 80%通过率
        print("\n🎉 恭喜！数据下载优化功能基本正常！")
        
        print("\n✨ 主要优化成果:")
        print("  • 🔄 增强版数据同步引擎")
        print("  • 💻 美观的命令行工具")
        print("  • 🌐 实时WebSocket通信")
        print("  • 🔧 完整的管理员面板")
        print("  • 📊 系统监控和导出功能")
        print("  • 🚀 Docker部署支持")
        
        print("\n🚀 下一步操作:")
        print("  1. 运行完整测试: python test_enhanced_download.py")
        print("  2. 启动Web服务: python app.py")
        print("  3. 使用命令行: python sync_cli_enhanced.py --help")
        print("  4. 查看优化报告: DATA_DOWNLOAD_OPTIMIZATION.md")
        
        return True
    else:
        print(f"\n❌ 部分功能测试失败 ({total-passed}/{total})")
        print("请检查错误信息并修复问题")
        print("\n💡 常见问题:")
        print("  • 检查TUSHARE_TOKEN是否配置")
        print("  • 确认所有依赖包已安装")
        print("  • 运行 pip install -r requirements.txt")
        
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
