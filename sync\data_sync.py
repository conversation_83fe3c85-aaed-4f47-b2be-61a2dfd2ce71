"""
数据同步核心模块
负责从Tushare获取数据并同步到本地数据库
"""

import tushare as ts
import pandas as pd
from datetime import datetime, timedelta
import time
import logging
import traceback
import requests
from typing import Optional, List, Dict, Any, Callable
from sqlalchemy import text
from sqlalchemy.dialects.sqlite import insert as sqlite_insert
from sqlalchemy.dialects.postgresql import insert as postgresql_insert
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

from database.models import db_config, SyncStatus
from config import get_config, TUSHARE_TABLES

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TushareDataSync:
    """Tushare数据同步器 - 优化版本"""

    def __init__(self, token: str = None, progress_callback: Callable = None):
        self.config = get_config()
        self.token = token or self.config.TUSHARE_TOKEN
        self.progress_callback = progress_callback

        # 初始化Tushare
        ts.set_token(self.token)
        self.pro = ts.pro_api()

        # 同步配置
        self.batch_size = self.config.SYNC_CONFIG['batch_size']
        self.max_offset = self.config.SYNC_CONFIG['max_offset']
        self.request_delay = self.config.SYNC_CONFIG['request_delay']
        self.segment_delay = self.config.SYNC_CONFIG['segment_delay']
        self.days_per_chunk = self.config.SYNC_CONFIG['days_per_chunk']
        self.max_retries = self.config.SYNC_CONFIG['max_retries']
        self.retry_delay = self.config.SYNC_CONFIG['retry_delay']

        # 状态跟踪
        self.is_cancelled = False
        self.current_progress = 0
        self.total_progress = 100
        self.lock = threading.Lock()

        # 验证Tushare连接
        self._validate_tushare_connection()

    def _validate_tushare_connection(self):
        """验证Tushare连接"""
        try:
            # 测试API连接
            test_df = self.pro.stock_basic(limit=1)
            if test_df.empty:
                raise Exception("Tushare API返回空数据")
            logger.info("Tushare API连接验证成功")
        except Exception as e:
            logger.error(f"Tushare API连接失败: {e}")
            raise Exception(f"无法连接到Tushare API: {e}")

    def cancel_sync(self):
        """取消同步"""
        with self.lock:
            self.is_cancelled = True
            logger.info("同步已被取消")

    def _update_progress(self, current: int, total: int, message: str = ""):
        """更新进度"""
        with self.lock:
            if total > 0:
                self.current_progress = int((current / total) * 100)
            else:
                self.current_progress = 0

            if self.progress_callback:
                self.progress_callback({
                    'progress': self.current_progress,
                    'current': current,
                    'total': total,
                    'message': message,
                    'is_cancelled': self.is_cancelled
                })

    def _check_cancellation(self):
        """检查是否被取消"""
        with self.lock:
            if self.is_cancelled:
                raise Exception("同步已被用户取消")

    def sync_table(self, table_name: str, start_date: str = None, end_date: str = None,
                   force_full: bool = False) -> Dict[str, Any]:
        """
        同步指定表的数据
        
        Args:
            table_name: 表名
            start_date: 开始日期 (YYYYMMDD)
            end_date: 结束日期 (YYYYMMDD)
            force_full: 是否强制全量同步
            
        Returns:
            同步结果字典
        """
        if table_name not in TUSHARE_TABLES:
            raise ValueError(f"不支持的表名: {table_name}")
        
        table_config = TUSHARE_TABLES[table_name]
        logger.info(f"开始同步表: {table_name} ({table_config['name']})")
        
        try:
            # 获取同步状态
            sync_status = self._get_sync_status(table_name)
            
            # 确定同步日期范围
            if not start_date or not end_date:
                start_date, end_date = self._determine_sync_dates(
                    table_config, sync_status, force_full
                )
            
            # 执行同步
            if table_config['sync_type'] == 'full' or force_full:
                result = self._sync_full_table(table_name, table_config)
            else:
                result = self._sync_incremental_table(
                    table_name, table_config, start_date, end_date
                )
            
            # 更新同步状态
            self._update_sync_status(table_name, result)
            
            logger.info(f"表 {table_name} 同步完成: {result['record_count']} 条记录")
            return result
            
        except Exception as e:
            logger.error(f"同步表 {table_name} 失败: {e}")
            self._update_sync_status(table_name, {
                'status': 'error',
                'error_message': str(e),
                'record_count': 0
            })
            raise
    
    def _sync_full_table(self, table_name: str, table_config: Dict) -> Dict[str, Any]:
        """全量同步表数据"""
        api_name = table_config['api_name']
        total_records = 0
        
        logger.info(f"执行全量同步: {table_name}")
        
        # 获取API方法
        api_method = getattr(self.pro, api_name)
        
        page = 1
        while True:
            try:
                offset = (page - 1) * self.batch_size
                if offset >= self.max_offset:
                    logger.warning(f"达到offset限制: {offset}")
                    break
                
                # 调用API获取数据
                df = api_method(limit=self.batch_size, offset=offset)
                
                if df.empty:
                    logger.info("没有更多数据")
                    break
                
                # 保存数据到数据库
                records_saved = self._save_dataframe_to_db(df, table_name)
                total_records += records_saved
                
                logger.info(f"第{page}页: {records_saved} 条记录，累计: {total_records}")
                
                page += 1
                time.sleep(self.request_delay)
                
            except Exception as e:
                if "offset不能大于" in str(e):
                    logger.info("达到API offset限制")
                    break
                else:
                    logger.error(f"获取第{page}页数据失败: {e}")
                    raise
        
        return {
            'status': 'success',
            'record_count': total_records,
            'sync_date': datetime.now().strftime('%Y%m%d'),
            'error_message': None
        }
    
    def _sync_incremental_table(self, table_name: str, table_config: Dict, 
                               start_date: str, end_date: str) -> Dict[str, Any]:
        """增量同步表数据"""
        api_name = table_config['api_name']
        date_field = table_config['date_field']
        total_records = 0
        
        logger.info(f"执行增量同步: {table_name}, 日期范围: {start_date} - {end_date}")
        
        # 分段同步
        date_ranges = self._split_date_range(start_date, end_date)
        
        for i, (seg_start, seg_end) in enumerate(date_ranges, 1):
            logger.info(f"同步第{i}/{len(date_ranges)}段: {seg_start} - {seg_end}")
            
            try:
                segment_records = self._sync_date_segment(
                    api_name, date_field, seg_start, seg_end, table_name
                )
                total_records += segment_records
                
                logger.info(f"第{i}段完成: {segment_records} 条记录")
                
                if i < len(date_ranges):
                    time.sleep(self.segment_delay)
                    
            except Exception as e:
                logger.error(f"同步第{i}段失败: {e}")
                raise
        
        return {
            'status': 'success',
            'record_count': total_records,
            'sync_date': end_date,
            'error_message': None
        }
    
    def _sync_date_segment(self, api_name: str, date_field: str, 
                          start_date: str, end_date: str, table_name: str) -> int:
        """同步指定日期段的数据"""
        api_method = getattr(self.pro, api_name)
        total_records = 0
        
        page = 1
        while True:
            try:
                offset = (page - 1) * self.batch_size
                if offset >= self.max_offset:
                    break
                
                # 构建API参数
                params = {
                    'limit': self.batch_size,
                    'offset': offset
                }
                
                if date_field:
                    params['start_date'] = start_date
                    params['end_date'] = end_date
                
                # 调用API
                df = api_method(**params)
                
                if df.empty:
                    break
                
                # 保存数据
                records_saved = self._save_dataframe_to_db(df, table_name)
                total_records += records_saved
                
                page += 1
                time.sleep(self.request_delay)
                
            except Exception as e:
                if "offset不能大于" in str(e):
                    break
                else:
                    raise
        
        return total_records
    
    def _save_dataframe_to_db(self, df: pd.DataFrame, table_name: str) -> int:
        """将DataFrame保存到数据库"""
        if df.empty:
            return 0
        
        session = db_config.get_session()
        
        try:
            # 转换数据类型
            df = self._prepare_dataframe(df, table_name)
            
            # 使用upsert操作（如果支持）
            records = df.to_dict('records')
            
            # 根据数据库类型选择插入方式
            if 'sqlite' in db_config.engine.url.drivername:
                self._upsert_sqlite(session, table_name, records)
            else:
                self._upsert_postgresql(session, table_name, records)
            
            session.commit()
            return len(records)
            
        except Exception as e:
            session.rollback()
            logger.error(f"保存数据到表 {table_name} 失败: {e}")
            raise
        finally:
            session.close()
    
    def _prepare_dataframe(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """准备DataFrame数据"""
        # 处理NaN值
        df = df.where(pd.notnull(df), None)
        
        # 处理日期字段
        date_columns = [col for col in df.columns if 'date' in col.lower()]
        for col in date_columns:
            if col in df.columns:
                df[col] = df[col].astype(str).replace('nan', None)
        
        return df
    
    def _upsert_sqlite(self, session, table_name: str, records: List[Dict]):
        """SQLite upsert操作"""
        # SQLite使用INSERT OR REPLACE
        for record in records:
            placeholders = ', '.join([f':{key}' for key in record.keys()])
            columns = ', '.join(record.keys())
            
            sql = f"INSERT OR REPLACE INTO {table_name} ({columns}) VALUES ({placeholders})"
            session.execute(text(sql), record)
    
    def _upsert_postgresql(self, session, table_name: str, records: List[Dict]):
        """PostgreSQL upsert操作"""
        # PostgreSQL使用ON CONFLICT
        if not records:
            return
        
        # 这里需要根据具体表的主键来处理冲突
        # 简化处理：先删除再插入
        for record in records:
            placeholders = ', '.join([f':{key}' for key in record.keys()])
            columns = ', '.join(record.keys())
            
            sql = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"
            session.execute(text(sql), record)
    
    def _split_date_range(self, start_date: str, end_date: str) -> List[tuple]:
        """分割日期范围"""
        start_dt = datetime.strptime(start_date, '%Y%m%d')
        end_dt = datetime.strptime(end_date, '%Y%m%d')
        
        date_ranges = []
        current_start = start_dt
        
        while current_start <= end_dt:
            current_end = min(
                current_start + timedelta(days=self.days_per_chunk-1), 
                end_dt
            )
            date_ranges.append((
                current_start.strftime('%Y%m%d'),
                current_end.strftime('%Y%m%d')
            ))
            current_start = current_end + timedelta(days=1)
        
        return date_ranges
    
    def _get_sync_status(self, table_name: str) -> Optional[SyncStatus]:
        """获取同步状态"""
        session = db_config.get_session()
        try:
            return session.query(SyncStatus).filter_by(table_name=table_name).first()
        finally:
            session.close()
    
    def _determine_sync_dates(self, table_config: Dict, sync_status: Optional[SyncStatus], 
                             force_full: bool) -> tuple:
        """确定同步日期范围"""
        if force_full or not sync_status or not sync_status.last_sync_date:
            # 全量同步或首次同步
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
        else:
            # 增量同步
            start_date = sync_status.last_sync_date
            end_date = datetime.now().strftime('%Y%m%d')
        
        return start_date, end_date
    
    def _update_sync_status(self, table_name: str, result: Dict[str, Any]):
        """更新同步状态"""
        session = db_config.get_session()
        
        try:
            sync_status = session.query(SyncStatus).filter_by(table_name=table_name).first()
            
            if not sync_status:
                sync_status = SyncStatus(table_name=table_name)
                session.add(sync_status)
            
            sync_status.last_sync_date = result.get('sync_date')
            sync_status.last_sync_time = datetime.now()
            sync_status.sync_status = result.get('status', 'error')
            sync_status.error_message = result.get('error_message')
            sync_status.record_count = result.get('record_count', 0)
            
            session.commit()
            
        except Exception as e:
            session.rollback()
            logger.error(f"更新同步状态失败: {e}")
            raise
        finally:
            session.close()

# 便捷函数
def sync_all_tables(force_full: bool = False) -> Dict[str, Any]:
    """同步所有表"""
    syncer = TushareDataSync()
    results = {}
    
    for table_name in TUSHARE_TABLES.keys():
        try:
            result = syncer.sync_table(table_name, force_full=force_full)
            results[table_name] = result
        except Exception as e:
            results[table_name] = {
                'status': 'error',
                'error_message': str(e),
                'record_count': 0
            }
    
    return results

def sync_daily_tables() -> Dict[str, Any]:
    """同步每日更新的表"""
    syncer = TushareDataSync()
    results = {}
    
    daily_tables = [
        name for name, config in TUSHARE_TABLES.items() 
        if config['sync_frequency'] == 'daily'
    ]
    
    for table_name in daily_tables:
        try:
            result = syncer.sync_table(table_name)
            results[table_name] = result
        except Exception as e:
            results[table_name] = {
                'status': 'error',
                'error_message': str(e),
                'record_count': 0
            }
    
    return results
