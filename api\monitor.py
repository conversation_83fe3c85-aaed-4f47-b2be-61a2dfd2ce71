"""
系统监控功能
提供系统性能监控、健康检查、实时统计等功能
"""

from flask import Blueprint, request, jsonify
from sqlalchemy import text, func
import psutil
import os
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

from database.models import db_config, User, SyncStatus
from config import get_config, TUSHARE_TABLES

# 创建监控蓝图
monitor_bp = Blueprint('monitor', __name__, url_prefix='/api/v1/monitor')

# 配置
config = get_config()

@monitor_bp.route('/system', methods=['GET'])
def get_system_info():
    """获取系统信息"""
    try:
        # CPU信息
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        cpu_freq = psutil.cpu_freq()
        
        # 内存信息
        memory = psutil.virtual_memory()
        
        # 磁盘信息
        disk = psutil.disk_usage('.')
        
        # 网络信息
        network = psutil.net_io_counters()
        
        # 进程信息
        process = psutil.Process()
        process_memory = process.memory_info()
        
        # 系统启动时间
        boot_time = datetime.fromtimestamp(psutil.boot_time())
        
        return jsonify({
            'code': 0,
            'msg': 'success',
            'data': {
                'cpu': {
                    'usage_percent': cpu_percent,
                    'count': cpu_count,
                    'frequency': {
                        'current': cpu_freq.current if cpu_freq else None,
                        'min': cpu_freq.min if cpu_freq else None,
                        'max': cpu_freq.max if cpu_freq else None
                    }
                },
                'memory': {
                    'total': memory.total,
                    'available': memory.available,
                    'used': memory.used,
                    'usage_percent': memory.percent,
                    'free': memory.free
                },
                'disk': {
                    'total': disk.total,
                    'used': disk.used,
                    'free': disk.free,
                    'usage_percent': (disk.used / disk.total) * 100
                },
                'network': {
                    'bytes_sent': network.bytes_sent,
                    'bytes_recv': network.bytes_recv,
                    'packets_sent': network.packets_sent,
                    'packets_recv': network.packets_recv
                },
                'process': {
                    'pid': process.pid,
                    'memory_rss': process_memory.rss,
                    'memory_vms': process_memory.vms,
                    'cpu_percent': process.cpu_percent(),
                    'create_time': datetime.fromtimestamp(process.create_time()).isoformat()
                },
                'system': {
                    'boot_time': boot_time.isoformat(),
                    'uptime_seconds': (datetime.now() - boot_time).total_seconds()
                },
                'timestamp': datetime.now().isoformat()
            }
        })
        
    except Exception as e:
        return jsonify({
            'code': -1,
            'msg': f'获取系统信息失败: {str(e)}',
            'data': None
        }), 500

@monitor_bp.route('/database', methods=['GET'])
def get_database_metrics():
    """获取数据库指标"""
    try:
        session = db_config.get_session()
        
        try:
            metrics = {
                'tables': {},
                'total_records': 0,
                'database_size': 0,
                'sync_status': {},
                'user_stats': {}
            }
            
            # 获取各表记录数
            for table_name in TUSHARE_TABLES.keys():
                try:
                    result = session.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                    count = result.scalar()
                    metrics['tables'][table_name] = {
                        'record_count': count,
                        'chinese_name': TUSHARE_TABLES[table_name]['name']
                    }
                    metrics['total_records'] += count
                except Exception as e:
                    metrics['tables'][table_name] = {
                        'record_count': 0,
                        'error': str(e),
                        'chinese_name': TUSHARE_TABLES[table_name]['name']
                    }
            
            # 获取数据库文件大小
            try:
                db_path = config.DATABASE_URL.replace('sqlite:///', '')
                if os.path.exists(db_path):
                    metrics['database_size'] = os.path.getsize(db_path)
            except:
                pass
            
            # 获取同步状态统计
            sync_statuses = session.query(SyncStatus).all()
            status_counts = {}
            for status in sync_statuses:
                status_key = status.sync_status or 'unknown'
                status_counts[status_key] = status_counts.get(status_key, 0) + 1
            
            metrics['sync_status'] = {
                'total_tables': len(TUSHARE_TABLES),
                'synced_tables': len(sync_statuses),
                'status_distribution': status_counts,
                'last_sync': None
            }
            
            # 获取最近同步时间
            if sync_statuses:
                latest_sync = max([s.last_sync_time for s in sync_statuses if s.last_sync_time], default=None)
                if latest_sync:
                    metrics['sync_status']['last_sync'] = latest_sync.isoformat()
            
            # 获取用户统计
            total_users = session.query(User).count()
            active_users = session.query(User).filter_by(is_active=True).count()
            premium_users = session.query(User).filter_by(is_premium=True).count()
            
            # 今日活跃用户
            today = datetime.now().date()
            today_active = session.query(User).filter(
                func.date(User.last_login) == today
            ).count()
            
            # API调用统计
            total_api_calls = session.query(func.sum(User.api_calls_today)).scalar() or 0
            
            metrics['user_stats'] = {
                'total_users': total_users,
                'active_users': active_users,
                'premium_users': premium_users,
                'today_active': today_active,
                'total_api_calls_today': total_api_calls
            }
            
            return jsonify({
                'code': 0,
                'msg': 'success',
                'data': metrics
            })
            
        finally:
            session.close()
            
    except Exception as e:
        return jsonify({
            'code': -1,
            'msg': f'获取数据库指标失败: {str(e)}',
            'data': None
        }), 500

@monitor_bp.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    try:
        health_status = {
            'status': 'healthy',
            'checks': {},
            'timestamp': datetime.now().isoformat()
        }
        
        # 数据库连接检查
        try:
            session = db_config.get_session()
            session.execute(text("SELECT 1"))
            session.close()
            health_status['checks']['database'] = {'status': 'ok', 'message': '数据库连接正常'}
        except Exception as e:
            health_status['checks']['database'] = {'status': 'error', 'message': f'数据库连接失败: {e}'}
            health_status['status'] = 'unhealthy'
        
        # Tushare API连接检查
        try:
            import tushare as ts
            ts.set_token(config.TUSHARE_TOKEN)
            pro = ts.pro_api()
            df = pro.stock_basic(limit=1)
            if not df.empty:
                health_status['checks']['tushare_api'] = {'status': 'ok', 'message': 'Tushare API连接正常'}
            else:
                health_status['checks']['tushare_api'] = {'status': 'warning', 'message': 'Tushare API返回空数据'}
        except Exception as e:
            health_status['checks']['tushare_api'] = {'status': 'error', 'message': f'Tushare API连接失败: {e}'}
            health_status['status'] = 'unhealthy'
        
        # 磁盘空间检查
        try:
            disk = psutil.disk_usage('.')
            free_gb = disk.free / (1024**3)
            if free_gb > 5:
                health_status['checks']['disk_space'] = {'status': 'ok', 'message': f'磁盘空间充足: {free_gb:.1f}GB'}
            elif free_gb > 1:
                health_status['checks']['disk_space'] = {'status': 'warning', 'message': f'磁盘空间较少: {free_gb:.1f}GB'}
            else:
                health_status['checks']['disk_space'] = {'status': 'error', 'message': f'磁盘空间不足: {free_gb:.1f}GB'}
                health_status['status'] = 'unhealthy'
        except Exception as e:
            health_status['checks']['disk_space'] = {'status': 'error', 'message': f'磁盘空间检查失败: {e}'}
        
        # 内存使用检查
        try:
            memory = psutil.virtual_memory()
            if memory.percent < 80:
                health_status['checks']['memory'] = {'status': 'ok', 'message': f'内存使用正常: {memory.percent:.1f}%'}
            elif memory.percent < 90:
                health_status['checks']['memory'] = {'status': 'warning', 'message': f'内存使用较高: {memory.percent:.1f}%'}
            else:
                health_status['checks']['memory'] = {'status': 'error', 'message': f'内存使用过高: {memory.percent:.1f}%'}
                health_status['status'] = 'unhealthy'
        except Exception as e:
            health_status['checks']['memory'] = {'status': 'error', 'message': f'内存检查失败: {e}'}
        
        return jsonify({
            'code': 0,
            'msg': 'success',
            'data': health_status
        })
        
    except Exception as e:
        return jsonify({
            'code': -1,
            'msg': f'健康检查失败: {str(e)}',
            'data': None
        }), 500

@monitor_bp.route('/performance', methods=['GET'])
def get_performance_metrics():
    """获取性能指标"""
    try:
        # 获取时间范围参数
        hours = int(request.args.get('hours', 24))  # 默认24小时
        
        session = db_config.get_session()
        
        try:
            # 计算时间范围
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours)
            
            # 获取同步性能数据
            sync_performance = session.query(SyncStatus).filter(
                SyncStatus.last_sync_time >= start_time,
                SyncStatus.last_sync_time <= end_time
            ).all()
            
            # 按小时分组统计
            hourly_stats = {}
            for sync in sync_performance:
                if sync.last_sync_time:
                    hour_key = sync.last_sync_time.strftime('%Y-%m-%d %H:00:00')
                    if hour_key not in hourly_stats:
                        hourly_stats[hour_key] = {
                            'sync_count': 0,
                            'success_count': 0,
                            'error_count': 0,
                            'total_records': 0
                        }
                    
                    hourly_stats[hour_key]['sync_count'] += 1
                    if sync.sync_status == 'success':
                        hourly_stats[hour_key]['success_count'] += 1
                        hourly_stats[hour_key]['total_records'] += sync.record_count or 0
                    elif sync.sync_status == 'error':
                        hourly_stats[hour_key]['error_count'] += 1
            
            # 获取API调用统计（简化版，实际应该有专门的API调用日志表）
            api_stats = {
                'total_users': session.query(User).count(),
                'active_users_today': session.query(User).filter(
                    func.date(User.last_login) == datetime.now().date()
                ).count(),
                'total_api_calls_today': session.query(func.sum(User.api_calls_today)).scalar() or 0
            }
            
            # 系统资源使用情况
            system_metrics = {
                'cpu_percent': psutil.cpu_percent(interval=1),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_percent': (psutil.disk_usage('.').used / psutil.disk_usage('.').total) * 100
            }
            
            return jsonify({
                'code': 0,
                'msg': 'success',
                'data': {
                    'time_range': {
                        'start_time': start_time.isoformat(),
                        'end_time': end_time.isoformat(),
                        'hours': hours
                    },
                    'sync_performance': {
                        'hourly_stats': hourly_stats,
                        'total_syncs': len(sync_performance),
                        'success_rate': len([s for s in sync_performance if s.sync_status == 'success']) / max(len(sync_performance), 1)
                    },
                    'api_performance': api_stats,
                    'system_metrics': system_metrics,
                    'generated_at': datetime.now().isoformat()
                }
            })
            
        finally:
            session.close()
            
    except Exception as e:
        return jsonify({
            'code': -1,
            'msg': f'获取性能指标失败: {str(e)}',
            'data': None
        }), 500

@monitor_bp.route('/alerts', methods=['GET'])
def get_alerts():
    """获取系统告警"""
    try:
        alerts = []
        
        # 检查磁盘空间
        disk = psutil.disk_usage('.')
        free_gb = disk.free / (1024**3)
        if free_gb < 1:
            alerts.append({
                'level': 'critical',
                'type': 'disk_space',
                'message': f'磁盘空间严重不足: 仅剩 {free_gb:.1f}GB',
                'timestamp': datetime.now().isoformat()
            })
        elif free_gb < 5:
            alerts.append({
                'level': 'warning',
                'type': 'disk_space',
                'message': f'磁盘空间不足: 剩余 {free_gb:.1f}GB',
                'timestamp': datetime.now().isoformat()
            })
        
        # 检查内存使用
        memory = psutil.virtual_memory()
        if memory.percent > 90:
            alerts.append({
                'level': 'critical',
                'type': 'memory',
                'message': f'内存使用过高: {memory.percent:.1f}%',
                'timestamp': datetime.now().isoformat()
            })
        elif memory.percent > 80:
            alerts.append({
                'level': 'warning',
                'type': 'memory',
                'message': f'内存使用较高: {memory.percent:.1f}%',
                'timestamp': datetime.now().isoformat()
            })
        
        # 检查数据同步状态
        session = db_config.get_session()
        try:
            # 检查是否有长时间未同步的表
            cutoff_time = datetime.now() - timedelta(days=3)
            old_syncs = session.query(SyncStatus).filter(
                SyncStatus.last_sync_time < cutoff_time
            ).all()
            
            for sync in old_syncs:
                if sync.last_sync_time:
                    days_ago = (datetime.now() - sync.last_sync_time).days
                    alerts.append({
                        'level': 'warning',
                        'type': 'sync_delay',
                        'message': f'表 {sync.table_name} 已 {days_ago} 天未同步',
                        'timestamp': datetime.now().isoformat()
                    })
            
            # 检查同步错误
            error_syncs = session.query(SyncStatus).filter(
                SyncStatus.sync_status == 'error'
            ).all()
            
            for sync in error_syncs:
                alerts.append({
                    'level': 'error',
                    'type': 'sync_error',
                    'message': f'表 {sync.table_name} 同步失败: {sync.error_message}',
                    'timestamp': datetime.now().isoformat()
                })
        
        finally:
            session.close()
        
        return jsonify({
            'code': 0,
            'msg': 'success',
            'data': {
                'alerts': alerts,
                'alert_count': len(alerts),
                'critical_count': len([a for a in alerts if a['level'] == 'critical']),
                'warning_count': len([a for a in alerts if a['level'] == 'warning']),
                'error_count': len([a for a in alerts if a['level'] == 'error']),
                'generated_at': datetime.now().isoformat()
            }
        })
        
    except Exception as e:
        return jsonify({
            'code': -1,
            'msg': f'获取系统告警失败: {str(e)}',
            'data': None
        }), 500
