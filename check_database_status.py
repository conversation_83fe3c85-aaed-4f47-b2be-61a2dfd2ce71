#!/usr/bin/env python3
"""
数据库状态检查工具
显示详细的数据库信息和表结构
"""

import sys
import os
import sqlite3

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_database_file():
    """检查数据库文件"""
    print("🔍 检查数据库文件...")
    
    db_path = os.path.join('data', 'tushare_mirror.db')
    
    if os.path.exists(db_path):
        file_size = os.path.getsize(db_path)
        print(f"✅ 数据库文件存在: {db_path}")
        print(f"📊 文件大小: {file_size:,} 字节 ({file_size/1024/1024:.2f} MB)")
        return db_path
    else:
        print(f"❌ 数据库文件不存在: {db_path}")
        return None

def check_database_tables(db_path):
    """检查数据库表"""
    print("\n🔍 检查数据库表...")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"📊 发现 {len(tables)} 个表:")
        
        table_info = {}
        
        for (table_name,) in tables:
            print(f"\n📋 表: {table_name}")
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            print(f"  字段数: {len(columns)}")
            print("  字段列表:")
            for col in columns:
                col_id, col_name, col_type, not_null, default_val, pk = col
                pk_mark = " (主键)" if pk else ""
                print(f"    {col_name}: {col_type}{pk_mark}")
            
            # 获取记录数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"  记录数: {count:,}")
            
            # 获取样本数据
            if count > 0:
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 1")
                sample = cursor.fetchone()
                print(f"  样本数据: {sample[:3] if len(sample) > 3 else sample}...")
            
            table_info[table_name] = {
                'columns': len(columns),
                'records': count,
                'column_details': [(col[1], col[2]) for col in columns]
            }
        
        conn.close()
        return table_info
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return None

def check_tushare_tables():
    """检查Tushare表配置"""
    print("\n🔍 检查Tushare表配置...")
    
    try:
        from config import TUSHARE_TABLES
        
        print(f"📊 配置的Tushare表数量: {len(TUSHARE_TABLES)}")
        
        for table_name, config in TUSHARE_TABLES.items():
            print(f"  📋 {table_name}: {config['name']} ({config['sync_type']})")
        
        return TUSHARE_TABLES
        
    except Exception as e:
        print(f"❌ Tushare表配置检查失败: {e}")
        return None

def check_sqlalchemy_connection():
    """检查SQLAlchemy连接"""
    print("\n🔍 检查SQLAlchemy连接...")
    
    try:
        from database.models import db_config
        from sqlalchemy import text
        
        session = db_config.get_session()
        
        try:
            # 测试查询
            result = session.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
            tables = [row[0] for row in result.fetchall()]
            
            print(f"✅ SQLAlchemy连接成功")
            print(f"📊 通过SQLAlchemy发现 {len(tables)} 个表")
            
            # 检查主要表的数据
            main_tables = ['stock_basic', 'daily', 'trade_cal', 'index_basic']
            
            for table in main_tables:
                if table in tables:
                    result = session.execute(text(f"SELECT COUNT(*) FROM {table}"))
                    count = result.scalar()
                    print(f"  📊 {table}: {count:,} 条记录")
                else:
                    print(f"  ❌ {table}: 表不存在")
            
            return True
            
        finally:
            session.close()
            
    except Exception as e:
        print(f"❌ SQLAlchemy连接失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def generate_status_report(db_path, table_info, tushare_config):
    """生成状态报告"""
    print("\n📊 生成状态报告...")
    
    report = {
        'database_file': {
            'path': db_path,
            'exists': os.path.exists(db_path) if db_path else False,
            'size_mb': os.path.getsize(db_path) / 1024 / 1024 if db_path and os.path.exists(db_path) else 0
        },
        'tables': table_info or {},
        'tushare_config': tushare_config or {},
        'summary': {
            'total_tables': len(table_info) if table_info else 0,
            'total_records': sum(info['records'] for info in table_info.values()) if table_info else 0,
            'configured_tables': len(tushare_config) if tushare_config else 0
        }
    }
    
    # 保存报告
    import json
    with open('database_status_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print("✅ 状态报告已保存: database_status_report.json")
    
    return report

def main():
    """主函数"""
    print("🔍 数据库状态检查工具")
    print("=" * 50)
    
    # 1. 检查数据库文件
    db_path = check_database_file()
    
    if not db_path:
        print("\n❌ 数据库文件不存在，请先初始化数据库")
        print("💡 运行: python sync_cli_enhanced.py init")
        return False
    
    # 2. 检查数据库表（直接SQLite连接）
    table_info = check_database_tables(db_path)
    
    # 3. 检查Tushare表配置
    tushare_config = check_tushare_tables()
    
    # 4. 检查SQLAlchemy连接
    sqlalchemy_ok = check_sqlalchemy_connection()
    
    # 5. 生成报告
    report = generate_status_report(db_path, table_info, tushare_config)
    
    # 6. 显示总结
    print("\n" + "=" * 50)
    print("📊 数据库状态总结")
    print("=" * 50)
    
    if table_info:
        print(f"✅ 数据库文件: {report['database_file']['size_mb']:.2f} MB")
        print(f"✅ 数据表数量: {report['summary']['total_tables']}")
        print(f"✅ 总记录数: {report['summary']['total_records']:,}")
        print(f"✅ 配置表数量: {report['summary']['configured_tables']}")
        print(f"✅ SQLAlchemy连接: {'正常' if sqlalchemy_ok else '异常'}")
        
        # 显示主要表状态
        print("\n📊 主要表状态:")
        main_tables = ['stock_basic', 'daily', 'trade_cal', 'index_basic']
        for table in main_tables:
            if table in table_info:
                records = table_info[table]['records']
                status = "✅ 有数据" if records > 0 else "⚪ 空表"
                print(f"  {table}: {records:,} 条记录 {status}")
            else:
                print(f"  {table}: ❌ 表不存在")
        
        print("\n✅ 数据库状态检查完成！")
        return True
    else:
        print("❌ 数据库检查失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
