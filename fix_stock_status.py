#!/usr/bin/env python3
"""
修复股票状态字段
解决 list_status 字段为 None 的问题
"""

import sys
import os
import pandas as pd
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_stock_status_issue():
    """检查股票状态字段问题"""
    print("🔍 检查股票状态字段问题...")
    
    try:
        from database.models import db_config
        from sqlalchemy import text
        
        session = db_config.get_session()
        
        # 检查 list_status 字段的分布
        result = session.execute(text("""
            SELECT list_status, COUNT(*) as count 
            FROM stock_basic 
            GROUP BY list_status
        """))
        
        status_data = result.fetchall()
        
        print("  📊 当前 list_status 分布:")
        for status, count in status_data:
            print(f"     {status}: {count:,} 只股票")
        
        # 检查是否有 delist_date 数据
        delist_result = session.execute(text("""
            SELECT COUNT(*) as total,
                   COUNT(delist_date) as has_delist_date
            FROM stock_basic
        """))
        
        total, has_delist = delist_result.fetchone()
        print(f"\n  📊 退市日期字段:")
        print(f"     总股票数: {total:,}")
        print(f"     有退市日期: {has_delist:,}")
        print(f"     无退市日期: {total - has_delist:,}")
        
        session.close()
        
        # 如果所有 list_status 都是 None，说明需要修复
        if len(status_data) == 1 and status_data[0][0] is None:
            print("\n  ❌ 发现问题: 所有股票的 list_status 都是 None")
            return True
        else:
            print("\n  ✅ list_status 字段正常")
            return False
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def fix_stock_status():
    """修复股票状态字段"""
    print("\n🔧 修复股票状态字段...")
    
    try:
        from database.models import db_config
        from sqlalchemy import text
        
        session = db_config.get_session()
        
        # 根据退市日期推断股票状态
        print("  📊 根据退市日期推断股票状态...")
        
        # 1. 有退市日期的设为 'D' (退市)
        update_delisted = session.execute(text("""
            UPDATE stock_basic 
            SET list_status = 'D' 
            WHERE delist_date IS NOT NULL AND delist_date != ''
        """))
        
        delisted_count = update_delisted.rowcount
        print(f"     设置 {delisted_count:,} 只股票为退市状态 (D)")
        
        # 2. 没有退市日期的设为 'L' (正常上市)
        update_listed = session.execute(text("""
            UPDATE stock_basic 
            SET list_status = 'L' 
            WHERE (delist_date IS NULL OR delist_date = '') AND list_status IS NULL
        """))
        
        listed_count = update_listed.rowcount
        print(f"     设置 {listed_count:,} 只股票为上市状态 (L)")
        
        # 3. 特殊处理：检查股票名称中的退市标识
        update_st = session.execute(text("""
            UPDATE stock_basic 
            SET list_status = 'D' 
            WHERE (name LIKE '%*ST%' OR name LIKE '%退市%' OR name LIKE '%终止%')
            AND list_status != 'D'
        """))
        
        st_count = update_st.rowcount
        print(f"     根据股票名称设置 {st_count:,} 只股票为退市状态")
        
        # 提交更改
        session.commit()
        
        # 验证修复结果
        print("\n  📊 修复后的状态分布:")
        result = session.execute(text("""
            SELECT list_status, COUNT(*) as count 
            FROM stock_basic 
            GROUP BY list_status
        """))
        
        for status, count in result.fetchall():
            status_name = {
                'L': '正常上市',
                'D': '已退市',
                'P': '暂停上市',
                None: '未知状态'
            }.get(status, f'其他({status})')
            print(f"     {status} ({status_name}): {count:,} 只股票")
        
        session.close()
        
        print("  ✅ 股票状态字段修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def verify_dynamic_stock_pool():
    """验证动态股票池功能"""
    print("\n🧪 验证动态股票池功能...")
    
    try:
        from strategy_backtest import StrategyBacktest
        
        # 创建回测实例
        backtest = StrategyBacktest()
        
        # 加载股票基础信息
        from database.models import db_config
        session = db_config.get_session()
        backtest.stock_basic = pd.read_sql("SELECT * FROM stock_basic", session.bind)
        session.close()
        
        print(f"  📊 加载股票基础信息: {len(backtest.stock_basic)} 只股票")
        
        # 检查状态分布
        status_counts = backtest.stock_basic['list_status'].value_counts(dropna=False)
        print(f"  📊 状态分布: {dict(status_counts)}")
        
        # 测试不同日期的可交易股票
        test_dates = ['20150101', '20200101', '20250101']
        
        print(f"\n  📅 测试不同日期的可交易股票:")
        for date in test_dates:
            tradable = backtest.get_tradable_stocks_on_date(date)
            print(f"     {date}: {len(tradable):,} 只股票可交易")
        
        # 检查是否有退市股票
        delisted_stocks = backtest.stock_basic[backtest.stock_basic['list_status'] == 'D']
        print(f"\n  📊 退市股票: {len(delisted_stocks):,} 只")
        
        if len(delisted_stocks) > 0:
            print("     示例退市股票:")
            for _, stock in delisted_stocks.head(5).iterrows():
                print(f"       {stock['ts_code']}: {stock['name']} (退市日期: {stock['delist_date']})")
        
        print("  ✅ 动态股票池功能验证完成")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def check_data_readiness():
    """检查数据是否准备就绪"""
    print("\n🔍 检查回测数据准备情况...")
    
    try:
        from database.models import db_config
        from sqlalchemy import text
        
        session = db_config.get_session()
        
        # 检查各表的数据量
        tables_info = {}
        
        # 股票基础信息
        stock_count = session.execute(text("SELECT COUNT(*) FROM stock_basic")).scalar()
        tables_info['股票基础信息'] = stock_count
        
        # 交易日历
        trade_days = session.execute(text(
            "SELECT COUNT(*) FROM trade_cal WHERE cal_date >= '20150101' AND cal_date <= '20251231' AND is_open = 1"
        )).scalar()
        tables_info['交易日历'] = trade_days
        
        # 日线数据
        daily_count = session.execute(text(
            "SELECT COUNT(*) FROM daily WHERE trade_date >= '20150101' AND trade_date <= '20251231'"
        )).scalar()
        tables_info['日线数据'] = daily_count
        
        # 基本面数据
        basic_count = session.execute(text(
            "SELECT COUNT(*) FROM daily_basic WHERE trade_date >= '20150101' AND trade_date <= '20251231'"
        )).scalar()
        tables_info['基本面数据'] = basic_count
        
        print("  📊 数据统计:")
        for table, count in tables_info.items():
            print(f"     {table}: {count:,} 条记录")
        
        # 检查数据日期范围
        if daily_count > 0:
            date_range = session.execute(text(
                "SELECT MIN(trade_date), MAX(trade_date) FROM daily WHERE trade_date >= '20150101'"
            )).fetchone()
            print(f"     日线数据日期范围: {date_range[0]} - {date_range[1]}")
        
        if basic_count > 0:
            basic_range = session.execute(text(
                "SELECT MIN(trade_date), MAX(trade_date) FROM daily_basic WHERE trade_date >= '20150101'"
            )).fetchone()
            print(f"     基本面数据日期范围: {basic_range[0]} - {basic_range[1]}")
        
        session.close()
        
        # 评估数据准备情况
        min_requirements = {
            '股票基础信息': 3000,
            '交易日历': 2000,
            '日线数据': 500000,
            '基本面数据': 500000
        }
        
        ready = True
        print(f"\n  📋 数据准备评估:")
        for table, count in tables_info.items():
            min_req = min_requirements.get(table, 0)
            status = "✅" if count >= min_req else "❌"
            print(f"     {status} {table}: {count:,} / {min_req:,} (最低要求)")
            if count < min_req:
                ready = False
        
        if ready:
            print(f"\n  🎉 数据准备就绪，可以开始回测！")
        else:
            print(f"\n  ⚠️  数据还不够充分，建议继续同步")
        
        return ready
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 股票状态字段修复工具")
    print("=" * 60)
    
    # 1. 检查股票状态问题
    needs_fix = check_stock_status_issue()
    
    # 2. 如果需要修复，则进行修复
    if needs_fix:
        if fix_stock_status():
            print("\n✅ 股票状态字段修复成功")
        else:
            print("\n❌ 股票状态字段修复失败")
            return
    
    # 3. 验证动态股票池功能
    verify_dynamic_stock_pool()
    
    # 4. 检查数据准备情况
    data_ready = check_data_readiness()
    
    print("\n" + "=" * 60)
    if data_ready:
        print("🎉 所有准备工作完成！现在可以运行回测了。")
        print("\n💡 下一步:")
        print("   python run_strategy_backtest.py")
    else:
        print("⚠️  还需要更多数据，建议继续同步历史数据。")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
