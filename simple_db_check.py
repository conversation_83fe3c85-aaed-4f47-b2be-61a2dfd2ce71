#!/usr/bin/env python3
"""
简单数据库检查
"""

import sqlite3
import os

def main():
    db_path = os.path.join('data', 'tushare_mirror.db')
    print(f'🔍 检查数据库: {db_path}')

    if os.path.exists(db_path):
        file_size = os.path.getsize(db_path)
        print(f'✅ 文件存在，大小: {file_size:,} 字节 ({file_size/1024/1024:.2f} MB)')
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 获取所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            print(f'📊 发现 {len(tables)} 个表:')
            
            total_records = 0
            for (table_name,) in tables:
                try:
                    cursor.execute(f'SELECT COUNT(*) FROM {table_name}')
                    count = cursor.fetchone()[0]
                    total_records += count
                    
                    # 获取表结构
                    cursor.execute(f'PRAGMA table_info({table_name})')
                    columns = cursor.fetchall()
                    
                    print(f'  📋 {table_name}: {count:,} 条记录, {len(columns)} 个字段')
                    
                    # 显示前几个字段
                    if columns:
                        field_names = [col[1] for col in columns[:5]]
                        print(f'     字段: {", ".join(field_names)}{"..." if len(columns) > 5 else ""}')
                    
                except Exception as e:
                        print(f'     ❌ 查询失败: {e}')
            
            print(f'\n📊 总计: {total_records:,} 条记录')
            
            # 检查主要表的样本数据
            main_tables = ['stock_basic', 'daily', 'trade_cal']
            print(f'\n📋 主要表样本数据:')
            
            for table in main_tables:
                try:
                    cursor.execute(f'SELECT * FROM {table} LIMIT 1')
                    sample = cursor.fetchone()
                    if sample:
                        print(f'  {table}: {sample[:3]}...')
                    else:
                        print(f'  {table}: 无数据')
                except:
                    print(f'  {table}: 表不存在')
            
            conn.close()
            print('\n✅ 数据库检查完成')
            
        except Exception as e:
            print(f'❌ 数据库检查失败: {e}')
    else:
        print('❌ 数据库文件不存在')

if __name__ == "__main__":
    main()
