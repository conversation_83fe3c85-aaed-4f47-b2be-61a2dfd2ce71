#!/usr/bin/env python3
"""
策略分析和可视化工具
分析回测结果，生成详细报告和图表
"""

import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class StrategyAnalyzer:
    """策略分析器"""
    
    def __init__(self, results_file='backtest_results.xlsx'):
        self.results_file = results_file
        self.daily_returns = None
        self.trade_records = None
        self.strategy_params = None
        
    def load_results(self):
        """加载回测结果"""
        try:
            print(f"📊 加载回测结果: {self.results_file}")
            
            # 读取Excel文件
            self.daily_returns = pd.read_excel(self.results_file, sheet_name='每日收益')
            self.trade_records = pd.read_excel(self.results_file, sheet_name='交易记录')
            self.strategy_params = pd.read_excel(self.results_file, sheet_name='策略参数')
            
            print(f"✅ 数据加载成功")
            print(f"   📅 回测天数: {len(self.daily_returns)}")
            print(f"   🔄 交易次数: {len(self.trade_records)}")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载结果失败: {e}")
            return False
    
    def calculate_metrics(self):
        """计算策略指标"""
        if self.daily_returns is None:
            return None
        
        # 基本指标
        initial_value = self.daily_returns.iloc[0]['portfolio_value']
        final_value = self.daily_returns.iloc[-1]['portfolio_value']
        total_return = (final_value / initial_value - 1) * 100
        
        # 计算日收益率
        self.daily_returns['daily_pct_change'] = self.daily_returns['portfolio_value'].pct_change()
        daily_returns_series = self.daily_returns['daily_pct_change'].dropna()
        
        # 年化收益率
        trading_days = len(self.daily_returns)
        years = trading_days / 250
        annual_return = (pow(final_value / initial_value, 1/years) - 1) * 100
        
        # 波动率
        volatility = daily_returns_series.std() * np.sqrt(250) * 100
        
        # 夏普比率（假设无风险利率3%）
        risk_free_rate = 0.03
        sharpe_ratio = (annual_return/100 - risk_free_rate) / (volatility/100)
        
        # 最大回撤
        cumulative_returns = (1 + daily_returns_series).cumprod()
        rolling_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - rolling_max) / rolling_max
        max_drawdown = drawdown.min() * 100
        
        # 胜率
        if len(self.trade_records) > 0:
            sell_trades = self.trade_records[self.trade_records['action'] == 'sell']
            if len(sell_trades) > 0:
                profitable_trades = sell_trades[sell_trades['return'] > 0]
                win_rate = len(profitable_trades) / len(sell_trades) * 100
                avg_return_per_trade = sell_trades['return'].mean() * 100
            else:
                win_rate = 0
                avg_return_per_trade = 0
        else:
            win_rate = 0
            avg_return_per_trade = 0
        
        metrics = {
            '总收益率(%)': round(total_return, 2),
            '年化收益率(%)': round(annual_return, 2),
            '年化波动率(%)': round(volatility, 2),
            '夏普比率': round(sharpe_ratio, 2),
            '最大回撤(%)': round(max_drawdown, 2),
            '胜率(%)': round(win_rate, 2),
            '平均单笔收益(%)': round(avg_return_per_trade, 2),
            '交易次数': len(self.trade_records),
            '回测天数': trading_days
        }
        
        return metrics
    
    def plot_performance(self):
        """绘制策略表现图表"""
        if self.daily_returns is None:
            return
        
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('策略回测分析报告', fontsize=16, fontweight='bold')
        
        # 1. 净值曲线
        ax1 = axes[0, 0]
        dates = pd.to_datetime(self.daily_returns['date'], format='%Y%m%d')
        portfolio_values = self.daily_returns['portfolio_value']
        
        ax1.plot(dates, portfolio_values, linewidth=2, color='blue', label='策略净值')
        ax1.set_title('策略净值曲线')
        ax1.set_ylabel('组合价值 (元)')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # 格式化y轴
        ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x/10000:.0f}万'))
        
        # 2. 累计收益率
        ax2 = axes[0, 1]
        cumulative_returns = self.daily_returns['cumulative_return'] * 100
        ax2.plot(dates, cumulative_returns, linewidth=2, color='green', label='累计收益率')
        ax2.set_title('累计收益率')
        ax2.set_ylabel('收益率 (%)')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        # 3. 回撤分析
        ax3 = axes[1, 0]
        daily_returns_series = self.daily_returns['portfolio_value'].pct_change().dropna()
        cumulative_returns_series = (1 + daily_returns_series).cumprod()
        rolling_max = cumulative_returns_series.expanding().max()
        drawdown = (cumulative_returns_series - rolling_max) / rolling_max * 100
        
        ax3.fill_between(dates[1:], drawdown, 0, alpha=0.3, color='red', label='回撤')
        ax3.plot(dates[1:], drawdown, linewidth=1, color='red')
        ax3.set_title('策略回撤')
        ax3.set_ylabel('回撤 (%)')
        ax3.grid(True, alpha=0.3)
        ax3.legend()
        
        # 4. 持仓数量变化
        ax4 = axes[1, 1]
        position_counts = self.daily_returns['position_count']
        ax4.plot(dates, position_counts, linewidth=2, color='orange', label='持仓数量')
        ax4.set_title('持仓股票数量')
        ax4.set_ylabel('股票数量')
        ax4.grid(True, alpha=0.3)
        ax4.legend()
        
        plt.tight_layout()
        plt.savefig('strategy_performance.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("📊 策略表现图表已保存: strategy_performance.png")
    
    def analyze_trades(self):
        """分析交易记录"""
        if self.trade_records is None or len(self.trade_records) == 0:
            print("❌ 没有交易记录")
            return
        
        print("\n📊 交易分析")
        print("=" * 50)
        
        # 买卖交易统计
        buy_trades = self.trade_records[self.trade_records['action'] == 'buy']
        sell_trades = self.trade_records[self.trade_records['action'] == 'sell']
        
        print(f"📈 买入交易: {len(buy_trades)} 次")
        print(f"📉 卖出交易: {len(sell_trades)} 次")
        
        if len(sell_trades) > 0:
            # 收益分析
            returns = sell_trades['return'] * 100
            print(f"\n💰 收益分析:")
            print(f"   平均收益: {returns.mean():.2f}%")
            print(f"   收益中位数: {returns.median():.2f}%")
            print(f"   最大收益: {returns.max():.2f}%")
            print(f"   最大亏损: {returns.min():.2f}%")
            print(f"   收益标准差: {returns.std():.2f}%")
            
            # 胜率分析
            profitable_trades = sell_trades[sell_trades['return'] > 0]
            win_rate = len(profitable_trades) / len(sell_trades) * 100
            print(f"\n🎯 胜率分析:")
            print(f"   总胜率: {win_rate:.1f}%")
            print(f"   盈利交易: {len(profitable_trades)} 次")
            print(f"   亏损交易: {len(sell_trades) - len(profitable_trades)} 次")
            
            if len(profitable_trades) > 0:
                avg_profit = profitable_trades['return'].mean() * 100
                print(f"   平均盈利: {avg_profit:.2f}%")
            
            loss_trades = sell_trades[sell_trades['return'] <= 0]
            if len(loss_trades) > 0:
                avg_loss = loss_trades['return'].mean() * 100
                print(f"   平均亏损: {avg_loss:.2f}%")
        
        # 行业分布分析
        if len(buy_trades) > 0:
            print(f"\n🏭 交易行业分布:")
            # 这里需要从股票基础信息中获取行业信息
            # 简化处理，显示交易最频繁的股票
            stock_trade_counts = buy_trades['ts_code'].value_counts().head(10)
            print("   交易最频繁的股票:")
            for stock, count in stock_trade_counts.items():
                print(f"     {stock}: {count} 次")
    
    def generate_report(self):
        """生成完整分析报告"""
        print("\n📋 策略分析报告")
        print("=" * 60)
        
        # 计算指标
        metrics = self.calculate_metrics()
        
        if metrics:
            print("\n📊 策略表现指标:")
            for metric, value in metrics.items():
                print(f"   {metric}: {value}")
        
        # 交易分析
        self.analyze_trades()
        
        # 策略参数
        if self.strategy_params is not None:
            print(f"\n⚙️  策略参数:")
            for _, param in self.strategy_params.iterrows():
                print(f"   {param['参数名']}: {param['参数值']}")
        
        # 绘制图表
        self.plot_performance()
        
        print(f"\n✅ 分析报告生成完成!")

def compare_with_benchmark():
    """与基准比较"""
    print("\n📊 基准比较分析")
    print("=" * 50)
    
    try:
        # 这里可以添加与沪深300、中证500等指数的比较
        # 需要获取基准指数数据
        print("💡 基准比较功能开发中...")
        print("   建议比较基准:")
        print("   - 沪深300指数")
        print("   - 中证500指数")
        print("   - 创业板指数")
        print("   - 等权重组合")
        
    except Exception as e:
        print(f"❌ 基准比较失败: {e}")

def main():
    """主函数"""
    print("📊 策略分析和可视化工具")
    print("=" * 50)
    
    # 检查回测结果文件
    results_file = 'backtest_results.xlsx'
    if not os.path.exists(results_file):
        print(f"❌ 找不到回测结果文件: {results_file}")
        print("💡 请先运行 strategy_backtest.py 生成回测结果")
        return
    
    # 创建分析器
    analyzer = StrategyAnalyzer(results_file)
    
    # 加载数据
    if analyzer.load_results():
        # 生成分析报告
        analyzer.generate_report()
        
        # 基准比较
        compare_with_benchmark()
    else:
        print("❌ 分析失败")

if __name__ == "__main__":
    main()
