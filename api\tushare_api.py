"""
Tushare兼容API接口
提供与Tushare完全兼容的数据查询接口
"""

from flask import Blueprint, request, jsonify
from sqlalchemy import text, and_, or_
from sqlalchemy.orm import sessionmaker
import pandas as pd
from datetime import datetime, timedelta
import json
import hashlib
import time
from typing import Dict, List, Any, Optional

from database.models import db_config, User
from config import get_config, API_ENDPOINTS, TUSHARE_TABLES

# 创建API蓝图
api_bp = Blueprint('tushare_api', __name__, url_prefix='/api/v1')

# 配置
config = get_config()

# API限流装饰器
def rate_limit(func):
    """API限流装饰器"""
    def wrapper(*args, **kwargs):
        # 获取用户token
        token = request.headers.get('Authorization') or request.args.get('token')
        
        if not token:
            return jsonify({
                'code': -1,
                'msg': '缺少访问token',
                'data': None
            }), 401
        
        # 验证token并检查限流
        user = verify_token(token)
        if not user:
            return jsonify({
                'code': -1,
                'msg': '无效的token',
                'data': None
            }), 401
        
        # 检查API调用限制
        if not check_rate_limit(user):
            return jsonify({
                'code': -1,
                'msg': '超过API调用限制',
                'data': None
            }), 429
        
        # 更新调用计数
        update_api_calls(user)
        
        return func(*args, **kwargs)
    
    wrapper.__name__ = func.__name__
    return wrapper

def verify_token(token: str) -> Optional[User]:
    """验证API token"""
    session = db_config.get_session()
    try:
        user = session.query(User).filter_by(api_token=token, is_active=True).first()
        return user
    finally:
        session.close()

def check_rate_limit(user: User) -> bool:
    """检查API调用限制"""
    # 检查每日限制
    if user.is_premium:
        daily_limit = config.API_RATE_LIMIT['premium_user']
    else:
        daily_limit = config.API_RATE_LIMIT['free_user']
    
    return user.api_calls_today < daily_limit

def update_api_calls(user: User):
    """更新API调用计数"""
    session = db_config.get_session()
    try:
        # 检查是否是新的一天
        today = datetime.now().date()
        if user.last_login and user.last_login.date() != today:
            user.api_calls_today = 0
        
        user.api_calls_today += 1
        user.last_login = datetime.now()
        session.commit()
    finally:
        session.close()

class TushareAPIHandler:
    """Tushare API处理器"""
    
    def __init__(self):
        self.session = db_config.get_session()
    
    def __del__(self):
        if hasattr(self, 'session'):
            self.session.close()
    
    def query_data(self, api_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """查询数据的核心方法"""
        try:
            # 验证API名称
            if api_name not in API_ENDPOINTS:
                return {
                    'code': -1,
                    'msg': f'不支持的API: {api_name}',
                    'data': None
                }
            
            # 获取API配置
            api_config = API_ENDPOINTS[api_name]
            table_name = api_config['table']
            
            # 构建查询
            query_result = self._build_and_execute_query(table_name, params)
            
            if query_result['success']:
                return {
                    'code': 0,
                    'msg': 'success',
                    'data': {
                        'fields': query_result['fields'],
                        'items': query_result['items'],
                        'has_more': query_result['has_more']
                    }
                }
            else:
                return {
                    'code': -1,
                    'msg': query_result['error'],
                    'data': None
                }
                
        except Exception as e:
            return {
                'code': -1,
                'msg': f'查询失败: {str(e)}',
                'data': None
            }
    
    def _build_and_execute_query(self, table_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """构建并执行查询"""
        try:
            # 基础查询
            base_query = f"SELECT * FROM {table_name}"
            conditions = []
            query_params = {}
            
            # 处理查询条件
            conditions, query_params = self._build_conditions(table_name, params)
            
            # 构建完整查询
            if conditions:
                base_query += " WHERE " + " AND ".join(conditions)
            
            # 处理排序
            if 'trade_date' in self._get_table_columns(table_name):
                base_query += " ORDER BY trade_date DESC"
            elif 'ann_date' in self._get_table_columns(table_name):
                base_query += " ORDER BY ann_date DESC"
            
            # 处理分页
            limit = params.get('limit', 1000)
            offset = params.get('offset', 0)
            
            # 限制最大返回数量
            limit = min(limit, 5000)
            
            base_query += f" LIMIT {limit} OFFSET {offset}"
            
            # 执行查询
            result = self.session.execute(text(base_query), query_params)
            rows = result.fetchall()
            columns = result.keys()
            
            # 转换为字典列表
            items = []
            for row in rows:
                item = {}
                for i, col in enumerate(columns):
                    value = row[i]
                    # 处理None值
                    if value is None:
                        item[col] = ""
                    else:
                        item[col] = value
                items.append(item)
            
            # 检查是否还有更多数据
            has_more = len(items) == limit
            
            return {
                'success': True,
                'fields': list(columns),
                'items': items,
                'has_more': has_more
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _build_conditions(self, table_name: str, params: Dict[str, Any]) -> tuple:
        """构建查询条件"""
        conditions = []
        query_params = {}
        
        # 股票代码条件
        if params.get('ts_code'):
            conditions.append("ts_code = :ts_code")
            query_params['ts_code'] = params['ts_code']
        
        # 日期范围条件
        if params.get('start_date') and params.get('end_date'):
            date_field = self._get_date_field(table_name)
            if date_field:
                conditions.append(f"{date_field} >= :start_date")
                conditions.append(f"{date_field} <= :end_date")
                query_params['start_date'] = params['start_date']
                query_params['end_date'] = params['end_date']
        elif params.get('trade_date'):
            conditions.append("trade_date = :trade_date")
            query_params['trade_date'] = params['trade_date']
        elif params.get('ann_date'):
            conditions.append("ann_date = :ann_date")
            query_params['ann_date'] = params['ann_date']
        
        # 其他特定条件
        if table_name == 'stock_basic':
            if params.get('is_hs'):
                conditions.append("is_hs = :is_hs")
                query_params['is_hs'] = params['is_hs']
            if params.get('list_status'):
                conditions.append("list_status = :list_status")
                query_params['list_status'] = params['list_status']
            if params.get('exchange'):
                conditions.append("exchange = :exchange")
                query_params['exchange'] = params['exchange']
        
        elif table_name == 'trade_cal':
            if params.get('exchange'):
                conditions.append("exchange = :exchange")
                query_params['exchange'] = params['exchange']
            if params.get('is_open') is not None:
                conditions.append("is_open = :is_open")
                query_params['is_open'] = params['is_open']
        
        return conditions, query_params
    
    def _get_date_field(self, table_name: str) -> Optional[str]:
        """获取表的日期字段"""
        date_fields = {
            'daily': 'trade_date',
            'daily_basic': 'trade_date',
            'income': 'ann_date',
            'balancesheet': 'ann_date',
            'index_daily': 'trade_date',
            'trade_cal': 'cal_date'
        }
        return date_fields.get(table_name)
    
    def _get_table_columns(self, table_name: str) -> List[str]:
        """获取表的列名"""
        try:
            result = self.session.execute(text(f"PRAGMA table_info({table_name})"))
            columns = [row[1] for row in result.fetchall()]
            return columns
        except:
            return []

# API路由定义
@api_bp.route('/query', methods=['POST'])
@rate_limit
def query():
    """通用查询接口"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'code': -1,
                'msg': '请求数据为空',
                'data': None
            })
        
        api_name = data.get('api_name')
        params = data.get('params', {})
        
        if not api_name:
            return jsonify({
                'code': -1,
                'msg': '缺少api_name参数',
                'data': None
            })
        
        # 创建API处理器
        handler = TushareAPIHandler()
        result = handler.query_data(api_name, params)
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            'code': -1,
            'msg': f'服务器错误: {str(e)}',
            'data': None
        })

# 具体API端点
@api_bp.route('/stock_basic', methods=['GET', 'POST'])
@rate_limit
def stock_basic():
    """股票基础信息"""
    params = request.args.to_dict() if request.method == 'GET' else request.get_json() or {}
    
    handler = TushareAPIHandler()
    result = handler.query_data('stock_basic', params)
    
    return jsonify(result)

@api_bp.route('/daily', methods=['GET', 'POST'])
@rate_limit
def daily():
    """日线行情"""
    params = request.args.to_dict() if request.method == 'GET' else request.get_json() or {}
    
    handler = TushareAPIHandler()
    result = handler.query_data('daily', params)
    
    return jsonify(result)

@api_bp.route('/daily_basic', methods=['GET', 'POST'])
@rate_limit
def daily_basic():
    """每日指标"""
    params = request.args.to_dict() if request.method == 'GET' else request.get_json() or {}
    
    handler = TushareAPIHandler()
    result = handler.query_data('daily_basic', params)
    
    return jsonify(result)

@api_bp.route('/trade_cal', methods=['GET', 'POST'])
@rate_limit
def trade_cal():
    """交易日历"""
    params = request.args.to_dict() if request.method == 'GET' else request.get_json() or {}
    
    handler = TushareAPIHandler()
    result = handler.query_data('trade_cal', params)
    
    return jsonify(result)

# API信息端点
@api_bp.route('/info', methods=['GET'])
def api_info():
    """API信息"""
    return jsonify({
        'code': 0,
        'msg': 'success',
        'data': {
            'version': '1.0.0',
            'name': 'Tushare Mirror API',
            'description': '与Tushare完全兼容的数据API',
            'endpoints': list(API_ENDPOINTS.keys()),
            'rate_limits': config.API_RATE_LIMIT
        }
    })
