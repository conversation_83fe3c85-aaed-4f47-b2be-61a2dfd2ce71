// 管理员面板JavaScript

// 全局变量
let systemChart = null;
let databaseChart = null;
let currentPage = 1;
let usersPerPage = 10;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    loadDashboardData();
    loadUsers();
    initCharts();
    
    // 设置定时刷新
    setInterval(refreshDashboard, 30000); // 30秒刷新一次
});

// 加载仪表板数据
async function loadDashboardData() {
    try {
        // 加载系统统计
        const statsResponse = await fetch('/admin/stats');
        const statsData = await statsResponse.json();
        
        if (statsData.code === 0) {
            const stats = statsData.data;
            document.getElementById('totalUsers').textContent = stats.user_stats.total_users;
            document.getElementById('totalRecords').textContent = formatNumber(stats.data_stats.total_records);
            document.getElementById('todayAPICalls').textContent = stats.api_stats.total_calls_today;
        }
        
        // 加载系统告警
        const alertsResponse = await fetch('/api/v1/monitor/alerts');
        const alertsData = await alertsResponse.json();
        
        if (alertsData.code === 0) {
            document.getElementById('alertCount').textContent = alertsData.data.alert_count;
            displayAlerts(alertsData.data.alerts);
        }
        
    } catch (error) {
        console.error('加载仪表板数据失败:', error);
        showMessage('加载仪表板数据失败', 'danger');
    }
}

// 加载用户列表
async function loadUsers(page = 1, search = '') {
    try {
        const response = await fetch(`/admin/users?page=${page}&per_page=${usersPerPage}&search=${search}`);
        const data = await response.json();
        
        if (data.code === 0) {
            displayUsers(data.data.users);
            displayUsersPagination(data.data.pagination);
        } else {
            showMessage('加载用户列表失败: ' + data.msg, 'danger');
        }
        
    } catch (error) {
        console.error('加载用户列表失败:', error);
        showMessage('加载用户列表失败', 'danger');
    }
}

// 显示用户列表
function displayUsers(users) {
    const tbody = document.getElementById('usersTableBody');
    
    if (users.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="text-center">暂无用户</td></tr>';
        return;
    }
    
    tbody.innerHTML = users.map(user => `
        <tr>
            <td>${user.id}</td>
            <td>${user.username}</td>
            <td>${user.email}</td>
            <td>
                <span class="badge ${user.is_active ? 'bg-success' : 'bg-danger'}">
                    ${user.is_active ? '激活' : '禁用'}
                </span>
            </td>
            <td>
                <span class="badge ${user.is_premium ? 'bg-warning' : 'bg-secondary'}">
                    ${user.is_premium ? '高级' : '免费'}
                </span>
            </td>
            <td>${user.api_calls_today} / ${user.api_limit_daily}</td>
            <td>${formatDateTime(user.created_at)}</td>
            <td>
                <button class="btn btn-sm btn-primary" onclick="editUser(${user.id})">
                    <i class="bi bi-pencil"></i>
                </button>
                <button class="btn btn-sm btn-warning" onclick="resetUserToken(${user.id})">
                    <i class="bi bi-key"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

// 显示用户分页
function displayUsersPagination(pagination) {
    const paginationEl = document.getElementById('usersPagination');
    
    let html = '';
    
    // 上一页
    if (pagination.page > 1) {
        html += `<li class="page-item">
            <a class="page-link" href="#" onclick="loadUsers(${pagination.page - 1})">上一页</a>
        </li>`;
    }
    
    // 页码
    for (let i = Math.max(1, pagination.page - 2); i <= Math.min(pagination.pages, pagination.page + 2); i++) {
        html += `<li class="page-item ${i === pagination.page ? 'active' : ''}">
            <a class="page-link" href="#" onclick="loadUsers(${i})">${i}</a>
        </li>`;
    }
    
    // 下一页
    if (pagination.page < pagination.pages) {
        html += `<li class="page-item">
            <a class="page-link" href="#" onclick="loadUsers(${pagination.page + 1})">下一页</a>
        </li>`;
    }
    
    paginationEl.innerHTML = html;
}

// 搜索用户
function searchUsers() {
    const search = document.getElementById('userSearch').value;
    loadUsers(1, search);
}

// 编辑用户
function editUser(userId) {
    // 获取用户信息并显示编辑模态框
    fetch(`/admin/users`)
        .then(response => response.json())
        .then(data => {
            if (data.code === 0) {
                const user = data.data.users.find(u => u.id === userId);
                if (user) {
                    document.getElementById('editUserId').value = user.id;
                    document.getElementById('editUsername').value = user.username;
                    document.getElementById('editEmail').value = user.email;
                    document.getElementById('editIsActive').checked = user.is_active;
                    document.getElementById('editIsPremium').checked = user.is_premium;
                    document.getElementById('editApiLimit').value = user.api_limit_daily;
                    
                    new bootstrap.Modal(document.getElementById('userEditModal')).show();
                }
            }
        });
}

// 保存用户编辑
async function saveUserEdit() {
    const userId = document.getElementById('editUserId').value;
    const data = {
        is_active: document.getElementById('editIsActive').checked,
        is_premium: document.getElementById('editIsPremium').checked,
        api_limit_daily: parseInt(document.getElementById('editApiLimit').value)
    };
    
    try {
        const response = await fetch(`/admin/users/${userId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.code === 0) {
            showMessage('用户信息更新成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('userEditModal')).hide();
            loadUsers(currentPage);
        } else {
            showMessage('更新失败: ' + result.msg, 'danger');
        }
        
    } catch (error) {
        showMessage('更新失败: ' + error.message, 'danger');
    }
}

// 重置用户Token
async function resetUserToken(userId) {
    if (!confirm('确定要重置该用户的API Token吗？')) {
        return;
    }
    
    try {
        const response = await fetch(`/admin/users/${userId}/reset_token`, {
            method: 'POST'
        });
        
        const result = await response.json();
        
        if (result.code === 0) {
            showMessage(`用户Token已重置: ${result.data.new_token}`, 'success');
        } else {
            showMessage('重置Token失败: ' + result.msg, 'danger');
        }
        
    } catch (error) {
        showMessage('重置Token失败: ' + error.message, 'danger');
    }
}

// 初始化图表
function initCharts() {
    // 系统资源图表
    const systemCtx = document.getElementById('systemChart').getContext('2d');
    systemChart = new Chart(systemCtx, {
        type: 'doughnut',
        data: {
            labels: ['CPU使用率', 'CPU空闲'],
            datasets: [{
                data: [0, 100],
                backgroundColor: ['#dc3545', '#e9ecef']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'CPU使用率'
                }
            }
        }
    });
    
    // 数据库状态图表
    const databaseCtx = document.getElementById('databaseChart').getContext('2d');
    databaseChart = new Chart(databaseCtx, {
        type: 'bar',
        data: {
            labels: [],
            datasets: [{
                label: '记录数',
                data: [],
                backgroundColor: '#0d6efd'
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: '数据表记录数'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // 加载图表数据
    loadChartData();
}

// 加载图表数据
async function loadChartData() {
    try {
        // 加载系统信息
        const systemResponse = await fetch('/api/v1/monitor/system');
        const systemData = await systemResponse.json();
        
        if (systemData.code === 0) {
            const cpuUsage = systemData.data.cpu.usage_percent;
            systemChart.data.datasets[0].data = [cpuUsage, 100 - cpuUsage];
            systemChart.update();
        }
        
        // 加载数据库信息
        const dbResponse = await fetch('/api/v1/monitor/database');
        const dbData = await dbResponse.json();
        
        if (dbData.code === 0) {
            const tables = Object.entries(dbData.data.tables).slice(0, 8); // 只显示前8个表
            const labels = tables.map(([name, info]) => info.chinese_name || name);
            const data = tables.map(([name, info]) => info.record_count);
            
            databaseChart.data.labels = labels;
            databaseChart.data.datasets[0].data = data;
            databaseChart.update();
        }
        
    } catch (error) {
        console.error('加载图表数据失败:', error);
    }
}

// 显示告警
function displayAlerts(alerts) {
    const container = document.getElementById('alertsContainer');
    
    if (alerts.length === 0) {
        container.innerHTML = '<div class="text-center text-muted">暂无告警</div>';
        return;
    }
    
    container.innerHTML = alerts.map(alert => {
        const alertClass = {
            'critical': 'danger',
            'warning': 'warning',
            'error': 'danger',
            'info': 'info'
        }[alert.level] || 'secondary';
        
        return `
            <div class="alert alert-${alertClass} alert-dismissible fade show">
                <i class="bi bi-exclamation-triangle"></i>
                <strong>${alert.type}:</strong> ${alert.message}
                <small class="text-muted d-block">${formatDateTime(alert.timestamp)}</small>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
    }).join('');
}

// 刷新仪表板
function refreshDashboard() {
    loadDashboardData();
    loadChartData();
}

// 工具函数
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

function formatDateTime(dateString) {
    if (!dateString) return '-';
    
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function showMessage(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.getElementById('messageContainer').appendChild(alertDiv);
    
    // 自动移除消息
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}
